//package com.differ.wdgj.api.component.multidb.event;
//
//import org.springframework.context.event.EventListener;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.scheduling.annotation.EnableAsync;
//import org.springframework.stereotype.Component;
//
///**
// * 日志事件监听器
// * <AUTHOR>
// * @date 2020-08-10 10:25
// */
//@Component
//@EnableAsync
//public class MultiDbChangeEventListener {
//
//    @EventListener
//    @Async
//    public void handler(MultiDbChangeEvent event) {
//        MultiDbDataSource.getInstance().onChanged(event);
//    }
//}
