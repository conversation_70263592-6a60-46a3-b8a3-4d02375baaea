package com.differ.wdgj.api.component.multidb.core;

/**
 * 数据库上下文信息基类
 * <AUTHOR>
 * @date 2020-07-14 15:41
 */
public abstract class BaseDataSourceContextContainer implements IDataSourceContextContainer {

    /**
     * 数据源上下文
     */
    protected DataSourceContext dataSourceContext;

    @Override
    public DataSourceContext getDataSourceContext() {
        return dataSourceContext;
    }

    @Override
    public void setDataSourceContext(DataSourceContext dataSourceContext) {
        this.dataSourceContext = dataSourceContext;
    }
}
