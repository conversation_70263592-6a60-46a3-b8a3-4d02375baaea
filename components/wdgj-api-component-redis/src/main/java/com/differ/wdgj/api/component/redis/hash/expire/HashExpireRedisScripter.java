package com.differ.wdgj.api.component.redis.hash.expire;

import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.HashMap;
import java.util.Map;

/**
 * redis脚本处理，支持hash过期
 * <AUTHOR>
 * @date 2021/11/29 17:00
 */
public class HashExpireRedisScripter {

    /**
     * lua脚本对象集合
     */
    private Map<String, DefaultRedisScript> redisScripts;

    /**
     * lua脚本路径,过期hash批量查询
     */
    private static final String LUA_HASH_EXPIRE_MULTI_GET = "scripts/hash.expire/hash_expire_multi_get.lua";

    /**
     * lua脚本路径,过期hash更新
     */
    private static final String LUA_HASH_EXPIRE_MULTI_SET = "scripts/hash.expire/hash_expire_multi_set.lua";

    /**
     * lua脚本路径,过期hash批量删除
     */
    private static final String LUA_HASH_EXPIRE_MULTI_DEL = "scripts/hash.expire/hash_expire_multi_del.lua";

    /**
     * lua脚本路径,过期hash批量删除
     */
    private static final String LUA_HASH_EXPIRE_KEY_DEL = "scripts/hash.expire/hash_expire_key_del.lua";

    /**
     * lua脚本路径,测试
     */
    private static final String LUA_HASH_EXPIRE_TEST = "scripts/hash.expire/hash_expire_test.lua";

    //region 构造和枚举单例

    private HashExpireRedisScripter() {
        // 私有，为了单例
    }

    /**
     * 枚举单例
     *
     * @return
     */
    public static HashExpireRedisScripter singleton() {
        return SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        SINGLETON;

        private HashExpireRedisScripter instance;

        private SingletonEnum() {
            HashExpireRedisScripter redisScripter = new HashExpireRedisScripter();
            redisScripter.initAllScript();
            instance = redisScripter;
        }
    }

    //endregion

    /**
     * 初始lua
     */
    protected void initAllScript() {
        redisScripts = new HashMap<>();
        initScript(LUA_HASH_EXPIRE_MULTI_GET, java.util.List.class);
        initScript(LUA_HASH_EXPIRE_MULTI_DEL, Integer.class);
        initScript(LUA_HASH_EXPIRE_KEY_DEL, Integer.class);
        initScript(LUA_HASH_EXPIRE_MULTI_SET, Integer.class);
        initScript(LUA_HASH_EXPIRE_TEST, String.class);
    }

    /**
     * 初始单个lua
     */
    protected void initScript(String script, Class<?> resultType) {
        DefaultRedisScript redisScript = new DefaultRedisScript();
        redisScript.setLocation(new ClassPathResource(script));
        redisScript.setResultType(resultType);
        redisScripts.put(script, redisScript);
    }

    /**
     * lua脚本对象,过期hash批量查询
     */
    public DefaultRedisScript redisScriptHashExpireMultiGet() {
        return redisScripts.get(LUA_HASH_EXPIRE_MULTI_GET);
    }

    /**
     * lua脚本对象,过期hash批量更新
     */
    public DefaultRedisScript redisScriptHashExpireMultiSet() {
        return redisScripts.get(LUA_HASH_EXPIRE_MULTI_SET);
    }

    /**
     * lua脚本对象,过期hash批量删除
     */
    public DefaultRedisScript redisScriptHashExpireMultiDel() {
        return redisScripts.get(LUA_HASH_EXPIRE_MULTI_DEL);
    }

    /**
     * lua脚本对象,过期hash删除key
     */
    public DefaultRedisScript redisScriptHashExpireKeyDel() {
        return redisScripts.get(LUA_HASH_EXPIRE_KEY_DEL);
    }

    /**
     * lua脚本对象,测试
     */
    public DefaultRedisScript redisScriptHashExpireTest() {
        return redisScripts.get(LUA_HASH_EXPIRE_TEST);
    }
}
