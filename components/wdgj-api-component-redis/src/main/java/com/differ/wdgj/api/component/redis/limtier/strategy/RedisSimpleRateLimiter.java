package com.differ.wdgj.api.component.redis.limtier.strategy;

import com.differ.wdgj.api.component.redis.MultiRedis;
import com.differ.wdgj.api.component.redis.limtier.AbstractRedisRateLimiter;

import java.util.Arrays;
import java.util.List;

/**
 * redis简单限流实现，算法：按时间段内通过的总个数
 * <AUTHOR>
 * @date 2021/6/16 11:29
 */
public class RedisSimpleRateLimiter extends AbstractRedisRateLimiter {

    /**
     * lua脚本路径
     */
    private static final String LUA_URL = "scripts/simple_limiter.lua";

    /**
     * lua脚本路径
     */
    private static final String LUA_SCRIPT_URL = "scripts/limit/simple_limiter.lua";

    /**
     * 限流时间周期，单位秒
     */
    private String timeCycle;
    /**
     * 限流上限
     */
    private String capacity;


    public RedisSimpleRateLimiter(MultiRedis multiRedis) {
        super(multiRedis);
    }

    /**
     * 初始化参数
     *
     * @param timeCycle 限流时间周期，单位秒
     * @param capacity  限流上限
     * @param onlyRefreshArgs    是否只刷新参数
     */
    public void init(int timeCycle, int capacity, boolean onlyRefreshArgs) {
        // 初始化lua脚本,刷新参数的时候不用再初始化脚本
        if(!onlyRefreshArgs) {
            initScript(LUA_URL);
            initRedisScript(LUA_SCRIPT_URL);
        }
        // 限流时间周期，单位秒
        this.timeCycle = String.valueOf(timeCycle);
        // 限流上限
        this.capacity = String.valueOf(capacity);
    }

    @Override
    protected List<String> getKeys(String key) {
        return Arrays.asList(key);
    }

    @Override
    protected List<String> getArgs(String permitsCount) {
        return Arrays.asList(timeCycle, capacity, permitsCount);
    }
}

