-- 返回监控信息
-- User: chuff
-- Date: 2021/9/13 10:45
--

-- 任务排队队列
local contentWaitListKey = KEYS[1]
-- 执行中的任务队列
local executeKey = KEYS[2]

-- 排队数据开始
local start = tonumber(ARGV[1])
-- 排队数据结束
local stop = tonumber(ARGV[2])
-- 执行中的key后缀
local executeKeyTail = tonumber(ARGV[3])
local executeKeyWithTail = executeKey .. executeKeyTail
local executeKeyWithBefore = executeKey .. (executeKeyTail - 1)

-- result数据结构:执行总数量|排队总数量|执行返回数量|执行数据|排队返回数量|排队数据
local result = {}
-- 执行中的数量
local execCount = redis.call("hlen", executeKeyWithTail) + redis.call("hlen", executeKeyWithBefore)
-- 排队数量
local waitCount = redis.call("llen", contentWaitListKey)
result[1] = tostring(execCount)
result[2] = tostring(waitCount)

local nextIndex = 3
-- 执行数据
if execCount > 0 then
    -- 记录数量位置
    local size = stop - start;
    local execCountIndex = nextIndex;
    nextIndex = nextIndex + 1

    -- hash结构的执行中数据获取
    local dataCount = 0
    local execList = redis.call("hscan", executeKeyWithBefore, 0, "match", "*", "count", size)
    local dic = execList[2]
    local lenTotal = #dic
    if lenTotal > size * 2 then
        -- hscan 有可能返回>sizeBefore
        lenTotal = size * 2
    end
    for i = 1, lenTotal, 2 do
        result[nextIndex] = dic[i]
        nextIndex = nextIndex + 1
        dataCount = dataCount + 1
    end
    if dataCount < size then
        -- hash结构的执行中数据获取
        local sizeCurrent = size - dataCount
        local execListCurrent = redis.call("hscan", executeKeyWithTail, 0, "match", "*", "count", sizeCurrent)
        local dicCurrent = execListCurrent[2]
        local lenTotalCurrent = #dicCurrent
        if lenTotalCurrent > sizeCurrent * 2 then
            -- hscan 有可能返回>sizeCurrent
            lenTotalCurrent = sizeCurrent * 2
        end
        for i = 1, lenTotalCurrent, 2 do
            result[nextIndex] = dicCurrent[i]
            nextIndex = nextIndex + 1
            dataCount = dataCount + 1
        end
    end
    result[execCountIndex] = tostring(dataCount)
else
    result[nextIndex] = "0"
    nextIndex = nextIndex + 1
end
-- 排队数据
if waitCount > 0 then
    local waitList = redis.call("lrange", contentWaitListKey, start, stop)
    local len = #waitList
    -- 第三位存排队数据长度
    result[nextIndex] = tostring(len)
    nextIndex = nextIndex + 1
    for i = 1, len, 1 do
        result[nextIndex] = waitList[i]
        nextIndex = nextIndex + 1
    end
else
    result[nextIndex] = "0"
end
return result