-- 拉取下一个排队数据，没有剩余排队时，执行中计数递减
-- User: chuff
-- Date: 2021/9/13 10:45
--

-- 排队任务数据（Hash）
local contentWaitKey = KEYS[1]
-- 任务排队队列
local contentWaitListKey = KEYS[2]
-- 排队任务执行中的临时队列
local executeKey = KEYS[3]
-- 任务执行过期时间
local executeExpire = tonumber(ARGV[1])
-- 执行中的key后缀
local executeKeyTail = tonumber(ARGV[2])
-- 执行中数量上限
local maxCapacity = tonumber(ARGV[3])

local executeKeyWithTail = executeKey..executeKeyTail
local executeKeyWithBefore = executeKey .. (executeKeyTail - 1)

-- 比较执行中的数量
local curExecCount = redis.call("hlen", executeKeyWithTail) + redis.call("hlen", executeKeyWithBefore)
if curExecCount >= maxCapacity then
    -- 执行中超上限时，直接返回
    return nil
end
-- 获取当前排队任务
local uniqueId = nil
local content = nil

-- 循环取任务，因为队列有任务，但是hash可能没数据，这时丢弃这个任务，再取下一个
while (content == nil)
do
    uniqueId = redis.call('rpop', contentWaitListKey) or nil
    if uniqueId == nil then
        -- 说明没有排队任务
        break
    else
        content = redis.call("hget", contentWaitKey, uniqueId) or nil
        if content == nil then
            -- 当冗余任务时，直接移除排队,及保证任务信息以Hash数据表为主，列表只是做排队
            redis.call('lrem', contentWaitListKey, 0, uniqueId)
        end
    end
end

if content == nil then
    -- 没有取到排队任务时，直接返回
    return nil
end
-- 拉取到排队任务时，刷新执行中的过期时间，加入执行中数据，计数加1
redis.call("hset", executeKeyWithTail, uniqueId, content)
redis.call('hdel', contentWaitKey, uniqueId)
redis.call("expire", executeKeyWithTail, executeExpire)
return { uniqueId, content }
