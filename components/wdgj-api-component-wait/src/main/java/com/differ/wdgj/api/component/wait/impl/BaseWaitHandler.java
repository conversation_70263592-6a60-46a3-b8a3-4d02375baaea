package com.differ.wdgj.api.component.wait.impl;

import com.differ.wdgj.api.component.wait.AbstractWaitEntity;
import com.differ.wdgj.api.component.wait.WaitLine;
import com.differ.wdgj.api.component.wait.WaitStrategy;
import com.differ.wdgj.api.component.wait.data.*;
import com.differ.wdgj.api.component.wait.strategy.SimpleStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description 排队抽象类, 实际使用的子类必须在构造中初始化上下文
 * 执行中的key使用时间后缀的原因：
 * 1.场景：当发生前面有个执行中任务，一直没有回调结果时，且后续一直有任务进入到执行中时,
 * 2.目的：也能自动超时过期,避免执行中的任务个数被一直占坑，这时通过容错检查，可以调整回正确的执行中总数
 * <AUTHOR>
 * @Date 2021/9/10 15:02
 */
public class BaseWaitHandler implements WaitLine {

    protected Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 排队策略
     */
    protected WaitStrategy waitStrategy;

    /**
     * 排队组件集合
     */
    protected Map<String, WaitLine> mapWait = new ConcurrentHashMap<>();

    /**
     * 初始化，任务执行方法
     *
     * @param context
     */
    @Override
    public void init(WaitContext context) {
        this.init(context, null);
    }

    /**
     * 初始化，任务执行方法
     *
     * @param context
     * @param strategy
     */
    @Override
    public void init(WaitContext context, WaitStrategy strategy) {
        if (strategy == null) {
            strategy = new SimpleStrategy();
        }
        this.waitStrategy = strategy;
        this.waitStrategy.init(context);
    }

    /**
     * 获取上下文
     *
     * @return
     */
    @Override
    public WaitContext getWaitContext() {
        return this.waitStrategy.getWaitContext();
    }

    /**
     * 刷新配置
     *
     * @param args
     */
    @Override
    public void refreshArgs(WaitConfigArgs args) {
        this.waitStrategy.refreshArgs(args);
    }

    /**
     * 是否要排队，排队时加入排队任务(任务排队类型,任务会员，任务ID，任务会员限制任务数，任务数据)
     * 不排队时，直接执行
     *
     * @param entity
     * @return true:放入排队, false:已直接执行
     */
    @Override
    public boolean putWaitOrExec(AbstractWaitEntity entity) {
        return this.waitStrategy.putWaitOrExec(entity);
    }

    /**
     * 是否要排队，排队时加入排队任务(任务排队类型,任务会员，任务ID，任务会员限制任务数，任务数据)
     * 不排队时，直接执行
     *
     * @param entity
     * @return 排队状态 {@link WaitStatusEnum}
     */
    @Override
    public WaitStatusEnum putAndGetStatus(AbstractWaitEntity entity) {
        return this.waitStrategy.putAndGetStatus(entity);
    }

    /**
     * 收到消息：处理下一个MQ事件
     * @return true:取到任务并且处理，false:没有取到任务
     */
    @Override
    public boolean receiveMessageHandleNext() {
        return this.waitStrategy.receiveMessageHandleNext();
    }

    /**
     * 执行异步任务完成后回调结果
     *
     * @param waitUniqueId 任务唯一ID
     */
    @Override
    public void callbackResultOnAsync(String waitUniqueId) {
        this.waitStrategy.callbackResultOnAsync(waitUniqueId);
    }

    /**
     * 检查并修复数据容错
     *
     * @return 修复的记录数
     */
    @Override
    public int checkAndRepair() {
        return this.waitStrategy.checkAndRepair();
    }

    /**
     * 取消排队
     *
     * @param waitUniqueId 任务ID
     * @return 返回是否有任务被取消掉
     */
    @Override
    public boolean cancel(String waitUniqueId) {
        return this.waitStrategy.cancel(waitUniqueId);
    }

    /**
     * 取消排队
     *
     * @param waitUniqueIds 任务IDs
     * @return
     */
    @Override
    public int cancel(List<String> waitUniqueIds) {
        return this.waitStrategy.cancel(waitUniqueIds);
    }

    /**
     * 是否排队中或查询排队状态
     *
     * @param waitUniqueId
     * @return
     */
    @Override
    public boolean isWaiting(String waitUniqueId) {
        return this.waitStrategy.isWaiting(waitUniqueId);
    }

    /**
     * 是否排队中或查询排队状态
     *
     * @param waitUniqueIds
     * @return
     */
    @Override
    public Map<String, Boolean> isWaiting(List<String> waitUniqueIds) {
        return this.waitStrategy.isWaiting(waitUniqueIds);
    }

    /**
     * 排队监控信息查询
     *
     * @return
     */
    @Override
    public WaitMonitorResult getMonitorInfo(MonitorQueryArgs queryArgs) {
        return this.waitStrategy.getMonitorInfo(queryArgs);
    }

    /**
     * 获取排队任务总数
     *
     * @return
     */
    @Override
    public Long getWaitSum() {
        return this.waitStrategy.getWaitSum();
    }

    /**
     * 获取执行中任务总数
     *
     * @return
     */
    @Override
    public Long getExecSum() {
        return this.waitStrategy.getExecSum();
    }

    /**
     * 获取排队任务数据
     *
     * @param bizData 请求参数
     * @return
     */
    @Override
    public Map<String, String> getWaitData (WaitQueryRequestBiaData bizData) {
        return this.waitStrategy.getWaitData(bizData);
    }

    /**
     * 拉取下一个排队任务,发布MQ事件
     */
    protected boolean publishPullNext() {
        WaitContext waitContext = this.waitStrategy.getWaitContext();
        try {
            waitContext.getWaitMessageSubscriber().publishNext(new BaseWaitMessage(waitContext.getWaitType()));
            return true;
        } catch (Exception ex) {
            log.error(String.format("%s排队触发：拉取下一个失败", waitContext.getWaitType()), ex);
        }
        return false;
    }

    /**
     * 倒序获取排队任务数据
     *
     * @param waitUniqueIds 排队任务唯一标识
     * @return
     */
    @Override
    public LinkedHashMap<String, String> getWaitDataDesc (List<String> waitUniqueIds) {
        return this.waitStrategy.getWaitDataDesc(waitUniqueIds);
    }

    /**
     * 获取排队任务key
     *
     * @return
     */
    @Override
    public List<String> getWaitKeys(long start, long end) {
        return this.waitStrategy.getWaitKeys(start, end);
    }

    /**
     * 获取执行中任务key
     *
     * @return
     */
    @Override
    public List<String> getExecKeys() {
        return this.waitStrategy.getExecKeys();
    }

    /**
     * 获取排队任务数据
     *
     * @param waitUniqueIds 排队任务唯一标识
     * @return
     */
    @Override
    public boolean deleteWaitData (List<String> waitUniqueIds) {
        return this.waitStrategy.deleteWaitData(waitUniqueIds);
    }

    /**
     * 获取排队组件
     *
     * @param key 排队键
     * @return
     */
    @Override
    public WaitLine getHandler(String key) {
        return mapWait.computeIfAbsent(key, k -> create(key));
    }

    /**
     * 根据Key创建排队实例
     *
     * @param key 排队键
     * @return
     */
    protected WaitLine create(String key) {
        return this;
    }

    /**
     * 获取排队任务数据
     *
     * @param waitUniqueIds 排队任务唯一标识
     * @return
     */
    @Override
    public Map<String, String> getWaitData(List<String> waitUniqueIds) {
        return this.waitStrategy.getWaitData(waitUniqueIds);
    }

    /**
     * 转换排队键
     *
     * @param key 排队键
     * @return 排队键对象
     */
    protected WaitKey convertWaitKey(String key) {
        WaitKey waitKey = new WaitKey();
        String[] keySplit = key.split("_");
        if (keySplit.length > 0) {
            // 设置模式键
            waitKey.setModeKey(keySplit[0]);
        }
        if (keySplit.length > 1) {
            // 设置特殊键
            waitKey.setSpecialKey(keySplit[1]);
        }
        return waitKey;
    }
}
