package com.differ.wdgj.api.component.wait.data;

import com.differ.wdgj.api.component.redis.MultiRedis;
import com.differ.wdgj.api.component.wait.WaitExecutor;
import com.differ.wdgj.api.component.wait.WaitNextPublisher;

import java.util.function.Supplier;

/**
 * @Description 排队上下文
 * <AUTHOR>
 * @Date 2021/9/10 15:01
 */
public class WaitContext extends WaitConfigArgs {

    /**
     * 获取指定会员的最大执行数
     */
    private Supplier<Integer> funGetMaxExecCount;

    /**
     * 指定会员是否调试模式
     */
    private Supplier<Boolean> funIsDebug;

    public WaitContext(MultiRedis multiRedis, String waitType, WaitExecutor executor, WaitNextPublisher waitMessageSubscriber, boolean asyncCallbackResult) {
        this.multiRedis = multiRedis;
        this.waitType = waitType;
        this.executor = executor;
        this.waitMessageSubscriber = waitMessageSubscriber;
        this.asyncCallbackResult = asyncCallbackResult;
    }

    /**
     * redis操作
     */
    private MultiRedis multiRedis;
    /**
     * 排队类型
     */
    private String waitType;

    /**
     * 任务执行器
     */
    private WaitExecutor executor;

    /**
     * 排队完成事件消息订阅接口
     */
    private WaitNextPublisher waitMessageSubscriber;

    /**
     * 是否异步回调结果
     */
    private boolean asyncCallbackResult;

    public String getWaitType() {
        return waitType;
    }

    public WaitExecutor getExecutor() {
        return executor;
    }

    public WaitNextPublisher getWaitMessageSubscriber() {
        return waitMessageSubscriber;
    }

    public MultiRedis getMultiRedis() {
        return multiRedis;
    }

    public boolean isAsyncCallbackResult() {
        return asyncCallbackResult;
    }

    public Supplier<Integer> getFunGetMaxExecCount() {
        return funGetMaxExecCount;
    }

    public Supplier<Boolean> getFunIsDebug() {
        return funIsDebug;
    }

    public void setFunGetMaxExecCount(Supplier<Integer> funGetMaxExecCount) {
        this.funGetMaxExecCount = funGetMaxExecCount;
    }

    public void setFunIsDebug(Supplier<Boolean> funIsDebug) {
        this.funIsDebug = funIsDebug;
    }

    @Override
    public int getMaxExec() {
        if (this.funGetMaxExecCount != null) {
            return funGetMaxExecCount.get();
        }
        return super.getMaxExec();
    }

    public boolean isDebug() {
        if (this.funIsDebug != null) {
            return funIsDebug.get();
        }
        return false;
    }

    public void refreshArgs(WaitConfigArgs args) {
        this.setMaxExec(args.getMaxExec());
        this.setExecutingExpire(args.getExecutingExpire());
        this.setWaitDataExpire(args.getWaitDataExpire());
        this.setInsureMode(args.isInsureMode());
    }

    @Override
    public WaitContext clone() {
        WaitContext context = new WaitContext(this.multiRedis, this.waitType, this.executor, this.waitMessageSubscriber, this.asyncCallbackResult);
        context.setFunIsDebug(context.getFunIsDebug());
        context.setFunGetMaxExecCount(context.getFunGetMaxExecCount());
        context.refreshArgs(this);
        return context;
    }
}
