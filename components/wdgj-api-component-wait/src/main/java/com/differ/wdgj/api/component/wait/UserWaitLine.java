package com.differ.wdgj.api.component.wait;

import com.differ.wdgj.api.component.wait.data.*;

import java.util.List;
import java.util.Map;

/**
 * @Description 排队接口抽象
 * <AUTHOR>
 * @Date 2021/9/10 14:26
 */
public interface UserWaitLine {
    /**
     * 初始化，任务执行方法
     *
     * @param context
     */
    void init(WaitContext context);

    /**
     * 用户特殊参数赋值
     * @param user
     * @return
     */
    default WaitConfigArgs getUserArgs(String user) {
        return null;
    }

    /**
     * 获取会员排队组件
     *
     * @param key
     * @return
     */
    WaitLine getHandler(String key);

    /**
     * 刷新配置
     *
     * @param args
     */
    void refreshArgs(String user, WaitConfigArgs args);

    /**
     * 刷新配置
     *
     * @param args
     */
    void refreshAllArgs(WaitConfigArgs args);

    /**
     * 是否要排队，排队时加入排队任务(任务排队类型,任务会员，任务ID，任务会员限制任务数，任务数据)
     * 不排队时，直接执行
     *
     * @param user   用户（吉客号）
     * @param entity 任务数据
     * @return true:放入排队, false:已直接执行
     */
    boolean putWaitOrExec(String user, AbstractWaitEntity entity);

    /**
     * 是否要排队，排队时加入排队任务(任务排队类型,任务会员，任务ID，任务会员限制任务数，任务数据)
     * 不排队时，直接执行
     *
     * @param entity
     * @return 排队状态 {@link WaitStatusEnum}
     */
    WaitStatusEnum putAndGetStatus(String user, AbstractWaitEntity entity);

    /**
     * 取消排队(任务排队类型,任务会员，任务ID)
     *
     * @param user         用户（吉客号）
     * @param waitUniqueId 任务ID
     * @return
     */
    boolean cancel(String user, String waitUniqueId);

    /**
     * 取消排队
     *
     * @param user          用户（吉客号）
     * @param waitUniqueIds 任务IDs
     * @return 取消的个数
     */
    int cancel(String user, List<String> waitUniqueIds);

    /**
     * 是否排队中或查询排队状态(任务排队类型,任务会员，任务ID)
     *
     * @param user         用户（吉客号）
     * @param waitUniqueId
     * @return
     */
    boolean isWaiting(String user, String waitUniqueId);

    /**
     * 是否排队中或查询排队状态(任务排队类型,任务会员，任务ID)
     *
     * @param user          用户（吉客号）
     * @param waitUniqueIds
     * @return
     */
    Map<String, Boolean> isWaiting(String user, List<String> waitUniqueIds);

    /**
     * 收到消息：处理下一个MQ事件
     *
     * @param user 用户（吉客号）
     */
    void receiveMessageHandleNext(String user);

    /**
     * 执行异步任务完成后回调结果
     *
     * @param user         用户（吉客号）
     * @param waitUniqueId 任务唯一ID
     */
    void callbackResultOnAsync(String user, String waitUniqueId);

    /**
     * 排队监控信息
     *
     * @param user      用户（吉客号）
     * @param queryArgs
     * @return
     */
    WaitMonitorResult getMonitorInfo(String user, MonitorQueryArgs queryArgs);

    /**
     * 检查并修复数据容错
     *
     * @param user 用户（吉客号）
     * @return 修复的记录数
     */
    int checkAndRepair(String user);
}
