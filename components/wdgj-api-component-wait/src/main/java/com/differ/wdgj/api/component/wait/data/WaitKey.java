package com.differ.wdgj.api.component.wait.data;

import java.io.Serializable;

/**
 * @Description 排队键
 * <AUTHOR>
 * @Date 2022/11/21 9:57
 */
public class WaitKey implements Serializable {

    private static final long serialVersionUID = 5058042361604574814L;
    /**
     * 模式键
     * 用户名 或 集群号
     */
    private String modeKey;

    /**
     * 特殊键
     * 如平台值（独立平台排队）
     */
    private String specialKey;

    public String getModeKey() {
        return modeKey;
    }

    public void setModeKey(String modeKey) {
        this.modeKey = modeKey;
    }

    public String getSpecialKey() {
        return specialKey;
    }

    public void setSpecialKey(String specialKey) {
        this.specialKey = specialKey;
    }
}
