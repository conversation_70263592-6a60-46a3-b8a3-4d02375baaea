package com.differ.wdgj.api.component.util.tools;

/**
 * 数字工具类
 *
 * <AUTHOR>
 * @date 2025/6/17 14:37
 */
public class NumberUtils {
    /**
     * 安全的int转换
     *
     * @param input  输入字符串
     * @return 是否转换成功
     */
    public static Integer safeParseInt(String input) {
        if (input == null || input.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(input.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
