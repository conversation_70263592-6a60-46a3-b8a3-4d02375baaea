package com.differ.wdgj.api.component.util.json.core;

import com.alibaba.fastjson.serializer.ValueFilter;

import java.math.BigDecimal;

/**
 * fastjson过滤器：
 *      1.json字符串的boolean类型value从true、false 转为 0,1
 *      2.保留2位小数
 * <AUTHOR>
 * @date 2024/2/18 11:43
 */
public class FastJsonValueFilter implements ValueFilter {

    /**
     * @param object 对象
     * @param name   对象的字段的名称
     * @param value  对象的字段的值
     * @return 转化的值
     */
    @Override
    public Object process(Object object, String name, Object value) {
        //对布尔类型的特殊处理
        if (value instanceof Boolean) {
            return (boolean) value ? "1" : "0";
        } else if (value instanceof BigDecimal) {
            return String.format("%.2f", ((BigDecimal) value).doubleValue());
        }

        return value;
    }
}
