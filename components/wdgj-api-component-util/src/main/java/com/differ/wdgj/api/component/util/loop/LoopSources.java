package com.differ.wdgj.api.component.util.loop;

import java.util.Arrays;

/**
 * 循环发起源对象
 *
 * <AUTHOR>
 * @date 2024/5/13 10:28
 */
public class LoopSources {
    /**
     * 循环发起源对象,用于区别是否同一个发起者
     */
    private Object[] sources;

    public LoopSources(AvoidLoopFun avoidLoopFun, Object[] sources) {
        this.sources = new Object[sources.length + 1];
        this.sources[0] = avoidLoopFun;
        System.arraycopy(sources, 0, this.sources, 1, sources.length);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LoopSources that = (LoopSources) o;
        Object[] thatSources = that.getSources();
        // 相同地址引用，或者都是null表示相等
        if (sources == thatSources) {
            return true;
        }

        // 到这里，已不可能都为null，所以判断只要有一个为null则不相等，长度不等也不相等
        if (thatSources == null || sources == null || thatSources.length != sources.length) {
            return false;
        }

        // 逐个比较，只要有一个不等则不相等
        for (int i = 0; i < sources.length; i++) {
            if (!sources[i].equals(thatSources[i])) {
                return false;
            }
        }
        return true;
    }

    @Override
    public int hashCode() {
        return Arrays.hashCode(sources);
    }

    public Object[] getSources() {
        return sources;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("LoopSources{");
        sb.append("sources=").append(Arrays.toString(sources));
        sb.append('}');
        return sb.toString();
    }
}
