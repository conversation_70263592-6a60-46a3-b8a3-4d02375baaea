package com.differ.wdgj.api.component.util.enums;


import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.function.Function;

/**
 * 枚举项缓存管理器
 * 使用方式：
 * 1.枚举必须实现以下接口中的至少一个 {@link CodeEnum} {@link CodeEnum} {@link CodeEnum}
 * 2.按对应实现接口，传转换类型 {@link EnumConvertType}
 *
 * <AUTHOR>
 * @since 2019-12-20  17:06
 */
public class EnumConvertCacheUtil {

    private EnumConvertCacheUtil() {
        // 静态类，私有构造
    }

    /**
     * 枚举项缓存集合。
     */
    @SuppressWarnings("rawtypes")
    private static EnumConvertItem valueConvertItem = new EnumConvertItem(ValueEnum.class, o -> ((ValueEnum) o).getValue().toString());

    /**
     * 枚举项缓存集合。
     */
    @SuppressWarnings("rawtypes")
    private static EnumConvertItem codeConvertItem = new EnumConvertItem(CodeEnum.class, o -> ((CodeEnum) o).getCode());

    /**
     * 枚举项缓存集合。
     */
    @SuppressWarnings("rawtypes")
    private static EnumConvertItem nameConvertItem = new EnumConvertItem(NameEnum.class, o -> ((NameEnum) o).getName());

    /**
     * 根据值获取对应的枚举。
     *
     * @param valueCodeName 枚举值
     * @param type          枚举类别
     * @return 枚举值对应的枚举对象
     */
    public static <T extends Enum> T convert(String valueCodeName, Class<T> type, EnumConvertType convertType) {
        T enumValue;
        switch (convertType) {
            case VALUE:
                enumValue = valueConvertItem.convertToEnum(valueCodeName, type);
                break;
            case CODE:
                enumValue = codeConvertItem.convertToEnum(valueCodeName, type);
                break;
            default:
                enumValue = nameConvertItem.convertToEnum(valueCodeName, type);
                break;
        }
        return enumValue;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param valueCodeName 枚举值
     * @param type          枚举类别
     * @return 枚举值对应的枚举对象
     */
    public static <T extends Enum> T convertFromEnumConvertTypes(String valueCodeName, Class<T> type, EnumConvertType... convertTypes) {
        T enumValue = null;
        for (EnumConvertType convertType : convertTypes) {
            enumValue = convert(valueCodeName, type, convertType);
            if (enumValue != null) {
                break;
            }
        }
        return enumValue;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 枚举值
     * @param type  枚举类别
     * @return 枚举值对应的枚举对象
     */
    public static <T extends Enum> T convert(int value, Class<T> type, EnumConvertType convertType) {
        return convert(Integer.toString(value), type, convertType);
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 枚举值
     * @param type  枚举类别
     * @return 枚举值对应的枚举对象
     */
    public static <T extends Enum> T convert(Type type, String value, EnumConvertType... convertTypes) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }

        // 如果非枚举类型则直接返回null。
        Class<T> enumClass = ((Class) type);
        if (!enumClass.isEnum()) {
            return null;
        }
        return convertFromEnumConvertTypes(value, enumClass, convertTypes);
    }

    /**
     * 单种转换方式
     */
    private static class EnumConvertItem {

        /**
         * 转换接口
         */
        private Class enumInterfaceClass;

        /**
         * 转换接口的取值方式
         */
        private Function<Object, String> funGet;

        /**
         * 并发控制锁对象。
         */
        private final Object LOCK = new Object();

        public EnumConvertItem(Class enumInterfaceClass, Function<Object, String> funGet) {
            this.enumInterfaceClass = enumInterfaceClass;
            this.funGet = funGet;
        }

        /**
         * 枚举项缓存集合。
         */
        @SuppressWarnings("rawtypes")
        private HashMap<Type, HashMap<String, Object>> enumItemsMap = new HashMap<>();

        /**
         * 根据值获取对应的枚举。
         *
         * @param value 枚举值
         * @param type  枚举类别
         * @return 枚举值对应的枚举对象
         */
        private <T extends Enum> T convertToEnum(String value, Class<T> type) {
            if (StringUtils.isEmpty(value)) {
                return null;
            }

            HashMap<String, Object> enumValueMap = null;

            // 根据类型获取字典对象。
            if (!enumItemsMap.containsKey(type)) {
                // 创建当前类型的字典并完成初始化。
                synchronized (LOCK) {
                    if (!enumItemsMap.containsKey(type)) {
                        enumItemsMap.put(type, init(value, type));
                    }
                }
            }
            enumValueMap = enumItemsMap.get(type);
            // 从本字典中获取对象。
            if (enumValueMap != null && enumValueMap.containsKey(value)) {
                return (T) enumValueMap.get(value);
            }

            return null;
        }

        private <T extends Enum> HashMap<String, Object> init(String value, Class<T> type) {
            if (!this.enumInterfaceClass.isAssignableFrom(type)) {
                return new HashMap<>();
            }
            HashMap<String, Object> map = new HashMap<>();
            T[] enumObjs = type.getEnumConstants();
            for (Object enumObj : enumObjs) {
                //计算枚举值
                String enumValue = funGet.apply(enumObj);
                //添加映射：枚举值和枚举
                map.put(enumValue, enumObj);
            }
            return map;
        }
    }
}

