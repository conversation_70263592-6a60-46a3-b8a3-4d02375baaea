package com.differ.wdgj.api.component.util.tools;


import com.differ.wdgj.api.component.util.enums.TimeFormatEnum;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 时间转换工具类
 *
 * <AUTHOR>
 * @since 2019-12-23  11:21
 */
public class DateTimeUtils {

    private DateTimeUtils(){
    }

    /**
     * 默认时间格式: yyyy-MM-dd HH:mm:ss
     */
    private static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER = TimeFormatEnum.LONG_DATE_PATTERN_LINE.getFormatter();

    /**
     * String 转时间(localDateTime)
     *
     * @param timeStr 时间字符串
     * @return 转时间
     */
    public static LocalDateTime stringToTime(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            return null;
        }
        return LocalDateTime.parse(timeStr, DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * String 转本地日期(LocalDate)
     *
     * @param timeStr 时间字符串
     * @return 转本地日期
     */
    public static LocalDate stringToLocalDate(String timeStr) {
        if (timeStr == null || "".equals(timeStr)) {
            return null;
        } else {
            return LocalDate.parse(timeStr);
        }
    }

    /**
     * String 转时间(localDateTime)
     *
     * @param timeStr 时间字符串
     * @param format  时间格式
     * @return 转时间
     */
    public static LocalDateTime stringToTime(String timeStr, TimeFormatEnum format) {
        return LocalDateTime.parse(timeStr, format.getFormatter());
    }

    /**
     * 时间转 String(localDateTine)
     *
     * @param time 时间
     * @return 时间字符串
     */
    public static String timeToString(LocalDateTime time) {
        return DEFAULT_DATETIME_FORMATTER.format(time);
    }

    /**
     * 时间转 String
     *
     * @param time   时间
     * @param format 时间格式(localDateTine)
     * @return 时间字符串
     */
    public static String timeToString(LocalDateTime time, TimeFormatEnum format) {
        return format.getFormatter().format(time);
    }

    /**
     * 时间戳转时间
     *
     * @param timeStamp 时间戳
     * @return 转时间
     */
    public static LocalDateTime timestampToTime(String timeStamp) {
        if (timeStamp.length() == 13) {
            return LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(timeStamp)), ZoneId.systemDefault());
        } else if (timeStamp.length() == 10) {
            return LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(timeStamp)), ZoneId.systemDefault());
        } else {
            return LocalDateTime.parse(timeStamp, TimeFormatEnum.LONG_DATE_PATTERN_WITH_MILSEC_LINE.getFormatter());
        }
    }

    /**
     * 将Date类型转换为LocalDateTime类型
     *
     * @param date Date类型
     * @return
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * 将LocalDateTime类型转换为Date类型
     *
     * @param localDateTime LocalDateTime类型
     * @return
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * 将LocalDate类型转换为Date类型。
     *
     * @param localDate LocalDate类型
     * @return
     */
    public static Date localDateToDate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * 将LocalDateTime类型时间精确到分钟。
     *
     * @param localDateTime LocalDateTime类型
     * @return 精确到分钟的LocalDateTime
     */
    public static LocalDateTime exactMinute(LocalDateTime localDateTime) {
        // LocalDateTime类型转换成String。
        String timeStr = timeToString(localDateTime);

        StringBuilder sb = new StringBuilder(timeStr);
        // 格式化日期(精确到分钟)。
        sb.replace(17, 19, "00");

        return stringToTime(sb.toString());
    }

    /**
     * 获取当前时间戳（13位）
     *
     * @return 时间戳
     */
    public static long getTimestamp() {
        return LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 判断传入的时间是否在时间间隔内(包含时间间隔的起始点)
     *
     * @param dtTarget 传入的时间
     * @param dtStart  起始时间
     * @param dtEnd    结束时间
     * @return 是否在时间间隔内
     */
    public static boolean isBetween(LocalDateTime dtTarget, LocalDateTime dtStart, LocalDateTime dtEnd) {
        // 不允许起始时间晚于结束时间
        if (dtStart.isAfter(dtEnd)) {
            return false;
        }
        return (dtTarget.isAfter(dtStart) || dtTarget.isEqual(dtStart)) && (dtTarget.isBefore(dtEnd) || dtTarget.isEqual(dtEnd));
    }

    /**
     * 将毫秒数转换为天时分秒的字符串。
     *
     * @param totalMs 毫秒数
     * @return 天时分秒
     */
    public static String toDayHourMinuteSeconds(double totalMs) {
        // 将毫秒数转换成秒
        double seconds = totalMs / 1000;

        // 转换为天
        int days = (int) (seconds / (24 * 60 * 60));
        seconds -= days * 24 * 60 * 60;

        // 转换为小时
        int hours = (int) (seconds / (60 * 60));
        seconds -= hours * 60 * 60;

        // 转换成分钟
        int minutes = (int) (seconds / 60);
        seconds -= minutes * 60;

        String time = "";
        if (days > 0) {
            time += String.format("%02d天", days);
        }
        if (hours > 0) {
            time += String.format("%02d时", hours);
        }
        if (minutes > 0) {
            time += String.format("%02d分", minutes);
        }
        time += String.format("%02d秒", (int) seconds);

        return time;
    }
}


