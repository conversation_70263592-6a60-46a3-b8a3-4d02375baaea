package com.differ.wdgj.api.component.util.tools;

import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 集合工具类
 *
 * <AUTHOR>
 * @date 2024/9/26 下午7:06
 */
public class CollectionsUtils {
    //region 构造
    private CollectionsUtils() {
    }
    //endregion

    /**
     * 拆分集合
     * <p>
     * 增强方法：
     * 1、使用 limit 和 skip 替代 subList 方法
     * 2、避免 subList 方法拆分后，子集合修改影响父集合，导致后续子集合访问失败（java.util.ConcurrentModificationException）
     *
     * @param list 集合
     * @param size 大小
     * @param <T>  泛型
     * @return 结果
     */
    public static <T> List<List<T>> subListByLimit(List<T> list, int size) {
        if (CollectionUtils.isEmpty(list) || 0 == size) {
            return Collections.emptyList();
        }

        List<List<T>> result = new ArrayList<>();
        for (int offset = 0; offset < list.size(); offset += size) {
            List<T> lst = list.stream()
                    .skip(offset)
                    .limit(size)
                    .collect(Collectors.toList());

            result.add(lst);
        }

        return result;
    }

    //region 去重
    /**
     * 根据指定字段对集合进行去重
     *
     * @param collection 原始集合
     * @param keyExtractors 用于提取比较字段的函数列表
     * @param <T> 集合元素类型
     * @return 去重后的集合
     */
    public static <T> List<T> distinctByFields(Collection<T> collection, List<Function<T, ?>> keyExtractors) {
        if (collection == null || collection.isEmpty() || keyExtractors == null || keyExtractors.isEmpty()) {
            return new ArrayList<>();
        }

        return collection.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                item -> generateKey(item, keyExtractors),
                                Function.identity(),
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    /**
     * 根据指定字段对集合进行去重，支持自定义比较器
     *
     * @param collection 原始集合
     * @param keyExtractors 用于提取比较字段的函数列表
     * @param <T> 集合元素类型
     * @return 去重后的集合
     */
    @SafeVarargs
    public static <T> List<T> distinctByFields(Collection<T> collection, Function<T, ?>... keyExtractors) {
        if (CollectionUtils.isEmpty(collection) || keyExtractors == null || keyExtractors.length == 0) {
            return new ArrayList<>();
        }

        return collection.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                item -> generateKey(item, Arrays.asList(keyExtractors)),
                                Function.identity(),
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    /**
     * 根据指定字段对集合进行去重，支持自定义比较器和保留策略
     *
     * @param collection 原始集合
     * @param keyExtractors 用于提取比较字段的函数列表
     * @param mergeFunction 当遇到重复元素时的合并策略
     * @param <T> 集合元素类型
     * @return 去重后的集合
     */
    public static <T> List<T> distinctByFields(Collection<T> collection,
                                               List<Function<T, ?>> keyExtractors,
                                               java.util.function.BinaryOperator<T> mergeFunction) {
        if (CollectionUtils.isEmpty(collection) || CollectionUtils.isEmpty(keyExtractors)) {
            return new ArrayList<>();
        }

        return collection.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                item -> generateKey(item, keyExtractors),
                                Function.identity(),
                                mergeFunction
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    /**
     * 生成用于比较的键
     *
     * @param item 集合元素
     * @param keyExtractors 用于提取比较字段的函数列表
     * @param <T> 集合元素类型
     * @return 比较键
     */
    private static <T> String generateKey(T item, List<Function<T, ?>> keyExtractors) {
        return keyExtractors.stream()
                .map(extractor -> {
                    Object value = extractor.apply(item);
                    return value != null ? value.toString() : "null";
                })
                .collect(Collectors.joining("|"));
    }
    //endregion

}
