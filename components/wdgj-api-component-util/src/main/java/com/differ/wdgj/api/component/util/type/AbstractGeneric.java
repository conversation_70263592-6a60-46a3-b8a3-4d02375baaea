package com.differ.wdgj.api.component.util.type;

import org.springframework.aop.support.AopUtils;

/**
 * 泛型类基类，单个数据类型参数
 * 注意：泛型编译后会擦除类型，所以实际使用时的子类中必须明确类型
 * 正例：public class Demo extends AbstractGeneric<String>{}
 * 反例：public class Demo<T> extends AbstractGeneric<T>{}
 * 特别说明：如果一定要使用这种反例的写法，那么使用时要生成匿名类的实例（即实际也会确定类型）:new Demo<String>(){};
 *
 * <AUTHOR>
 * @date 2024/3/28 18:27
 */
public abstract class AbstractGeneric<T> {

    /**
     * 泛型的业务数据类型
     */
    private Class<T> dataClazz;

    /**
     * 获取泛型的业务数据类型
     *
     * @return 泛型的业务数据类型
     */
    public Class<T> getDataClazz() {
        if (dataClazz == null) {
            Class clazz = AopUtils.getTargetClass(this);
            dataClazz = (Class<T>)GenericTypeUtil.getGenericDataType(clazz, 0);
        }
        return dataClazz;
    }
}
