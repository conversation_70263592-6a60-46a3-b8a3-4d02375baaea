package com.differ.wdgj.api.component.util.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description 方法参数要做返回值的要增加此注解来增加代码可读性
 * 辅助应规范第15点，http://s.jkyun.biz/7SEPnlN API代码开发规范补充
 * 原则上避免修改方法的参数中的数据，即避免使用方法参数作为返回值来防范代码风险，如果真有必要,必须注释清楚参数要做返回值，并且参数带注解@Out
 * <AUTHOR>
 * @Date 2023/10/16 15:17
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.SOURCE)
public @interface Out {
}
