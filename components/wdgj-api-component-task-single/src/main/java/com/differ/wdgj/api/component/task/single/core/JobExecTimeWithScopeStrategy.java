package com.differ.wdgj.api.component.task.single.core;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;

/**
 * @Description 定时任务, 任务是否到达可执行时间（每天指定时间段内执行一次）
 * <AUTHOR>
 * @Date 2022/8/26 10:24
 */
public class JobExecTimeWithScopeStrategy extends AbstractExecTimeReEntryStrategy {

    /**
     * 开始时间（支持隔天）
     */
    private LocalTime startTime;

    /**
     * 定点容错时间（单位毫秒）
     */
    private long range;

    /**
     * 定时任务名
     */
    private String caption;

    private static final Logger logger =  Logger.getLogger(JobExecTimeWithScopeStrategy.class);

    @Override
    protected boolean enableRun(long currentTimeMillis) {
        // 上次是否执行过
        if (currentTimeMillis - lastRunTime <= range) {
            return false;
        }

        long todayStart = LocalDateTime.of(LocalDate.now(), startTime).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        // 当前时间和开始时间的间隔
        long differ = (currentTimeMillis + 3600 * 24 * 1000 - todayStart) % (3600 * 24 * 1000);
        // 是否在容错时间段内
        if (differ <= range) {
            this.lastRunTime = currentTimeMillis;
            if (StringUtils.isNotBlank(caption)) {
                logger.info(String.format("%s执行，当前时间：%s", caption, LocalDateTime.now()));
            }

            return true;
        }
        return false;
    }

    public void setStartTime (LocalTime startTime) {
        this.startTime = startTime;
    }

    public void setRange (long secondRange) {
        this.range = secondRange;
    }

    public void setCaption (String caption) {
        this.caption = caption;
    }
}

