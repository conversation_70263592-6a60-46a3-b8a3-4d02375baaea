package com.differ.wdgj.api.component.task.single.core;

import org.apache.commons.lang3.StringUtils;
import org.quartz.DisallowConcurrentExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;

/**
 * @Description 独立非分布式的定时任务抽象类
 * <AUTHOR>
 * @Date 2020/11/24 11:55
 */
@DisallowConcurrentExecution
public abstract class AbstractSingleJob implements SingleJob {

    /**
     * corn配置键格式
     */
    private static final String JOB_CRON_FORMAT = "job.%s.cron";

    /**
     * 初始的任务参数
     */
    private SingleJobParameter jobParameter;

    /**
     * spring上下文
     */
    @Autowired
    protected ApplicationContext applicationContext;

    /**
     * 获取初始的任务参数
     * @return
     */
    @Override
    public SingleJobParameter getJobParameter(){
        if(jobParameter==null){
            jobParameter = this.getClass().getAnnotation(SingleJobParameter.class);
        }
        return jobParameter;
    }

    /**
     * 获取cron,优先从Application.properties配置取
     * 这里一般不再兼容dotnet的频率控制方式，后面由架构组提供配置管理来处理频率（21年3月份左右）
     * 如有必要兼容dotnet的频率控制方式，可重写此方法，转换为对应的cron表达式
     *
     * @return
     */
    @Override
    public String getRunningJobCorn() {
        // 获取job任务参数
        SingleJobParameter annotation = getJobParameter();
        // 从配置读cron
        String cron = getJobCronOnApplicationConfig(annotation.jobName());
        if (!StringUtils.isBlank(cron)) {
            return cron;
        }
        return annotation.cron();
    }


    /**
     * 从Application获取到cron表达式
     *
     * @param jobName
     * @return
     */
    private String getJobCronOnApplicationConfig(String jobName) {
        if (StringUtils.isBlank(jobName)) {
            return null;
        }
        Environment environment = applicationContext.getEnvironment();

        // 根据job获取到配置的cron表达式
        String cornKey = getCornKey(jobName);
        return environment.getProperty(cornKey);
    }

    /**
     * 获取任务的cron配置键
     *
     * @param jobName
     * @return
     */
    private String getCornKey(String jobName) {
        return String.format(JOB_CRON_FORMAT, jobName);
    }
}
