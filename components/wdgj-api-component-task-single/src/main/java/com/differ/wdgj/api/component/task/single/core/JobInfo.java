package com.differ.wdgj.api.component.task.single.core;

import org.quartz.JobDetail;
import org.quartz.Trigger;

/**
 * @Description 任务信息对象
 * <AUTHOR>
 * @Date 2021/8/4 11:28
 */
public class JobInfo {

    /**
     * quartz的JobDetail对象
     */
    private JobDetail jobDetail;

    /**
     * quartz的触发器对象
     */
    private Trigger trigger;

    public JobInfo(JobDetail jobDetail, Trigger trigger) {
        this.jobDetail = jobDetail;
        this.trigger = trigger;
    }

    public JobDetail getJobDetail() {
        return jobDetail;
    }

    public Trigger getTrigger() {
        return trigger;
    }
}
