package com.differ.wdgj.api.component.task.single.core;

import java.time.Instant;

/**
 * @Description 时间间隔执行策略
 * <AUTHOR>
 * @Date 2021/11/4 10:58
 */
public class DefaultJobExecTimeStrategy implements JobExecTimeStrategy {

    /**
     * 执行频率，单位秒
     */
    protected long runFrequency = 3;

    /**
     * 上次执行时间戳，单位秒
     */
    private long lastRunTime = 0;

    /**
     * 是否当前可以执行任务,传入的时间戳和上次执行的时间戳相同时，认为可执行，非线程安全
     *
     * @param currentTimeMillis 当时任务的时间戳(毫秒)
     * @return true:可以执行
     */
    @Override
    public boolean enableRunOnReEntry(long currentTimeMillis) {
        // 已上线业务过多，考虑改动风险，暂不支持
        throw new RuntimeException("时间间隔执行策略不支持重入场景");
    }

    @Override
    public boolean enableRunCurrent() {

        long nowSecond = Instant.now().getEpochSecond();
        long nextTime = lastRunTime + runFrequency;
        if (nowSecond < nextTime) {
            return false;
        }
        this.lastRunTime = nowSecond;
        return true;
    }

    public void setRunFrequency(long runFrequency) {
        this.runFrequency = runFrequency;
    }

    /**
     * 判断上次执行时间跟当前时间是否是同一天
     * @return
     */
    public boolean isRunSameDay() {
        return lastRunTime * 1000 / (24*60*60*1000L) == System.currentTimeMillis() / (24*60*60*1000L);
    }
}
