package com.differ.wdgj.api.component.task.single.core;

import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description 单体任务作业注解
 * <AUTHOR>
 * @Date 2021/11/4 11:33
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Component
public @interface SingletonBeanJob {
    /**
     * 任务运行所包含的站点
     * @return
     */
    String[] sitesToRun() default {};

}
