package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.domain.goods.match.data.ApiSysMatchCooperationNoEnhance;
import com.differ.wdgj.api.user.biz.domain.goods.match.data.enums.ApiSysMatchStopStateEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleGoodsSysMatchItem;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.GoodsShelfStateEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchResult;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.BasicOperateMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * api商品匹配仓储(表g_api_sysMatch)
 *
 * <AUTHOR>
 * @date 2024-03-05 14:03
 */
public interface ApiSysMatchMapper extends BasicOperateMapper<ApiSysMatchDO> {

    // region 查询

    /**
     * 批量获取商品匹配 + 商品匹配扩展
     *
     * @param ids 商品匹配表id - 列表
     * @return 商品匹配列表
     */
    List<ApiSysMatchDO> selectWithExtraByIds(@Param("ids") List<Integer> ids);

    /**
     * 基于erp消息通知批量获取商品匹配（包含扩展信息）
     *
     * @param erpNoticeIds erp消息通知Id列表
     * @param startId      商品匹配表起始Id
     * @param pageSize     页码
     * @return 商品匹配列表
     */
    List<ApiSysMatchDO> selectWithExtraByErpNotices(@Param("erpNoticeIds") List<Long> erpNoticeIds, @Param("startId") Integer startId, @Param("pageSize") Integer pageSize);

    /**
     * 基于平台商品Id批量获取商品匹配
     *
     * @param platGoodsIds 平台商品Id列表
     * @return 商品匹配列表
     */
    List<ApiSysMatchDO> selectWithExtraByNumIIds(@Param("platGoodsIds") List<String> platGoodsIds);

    /**
     * 通过平台规格Id批量获取商品匹配
     *
     * @param platGoodsIds
     * @param platSkuIds   平台规格Id列表
     * @return 商品匹配列表
     */
    List<AfterSaleGoodsSysMatchItem> selectWithExtraByPlatIds(@Param("platGoodsIds") List<String> platGoodsIds, @Param("platSkuIds") List<String> platSkuIds);

    // endregion

    // region 修改

    /**
     * 批量更新停用状态
     *
     * @param ids       匹配表主键
     * @param stopState 停用状态  {@link ApiSysMatchStopStateEnum}
     */
    void updateStopState(@Param("ids") List<Integer> ids, @Param("stopState") int stopState);

    /**
     * 批量更新停用状态
     *
     * @param ids        匹配表主键
     * @param shelfState 上下架状态  {@link GoodsShelfStateEnum}
     */
    void updateShelfState(@Param("ids") List<Integer> ids, @Param("shelfState") int shelfState);

    /**
     * 修改商品匹配合作编码
     *
     * @param cooperationNos 商品匹配合作编码列表
     */
    void updateGoodsCooperationNo(@Param("cooperationNos") List<ApiSysMatchCooperationNoEnhance> cooperationNos);

    /**
     * 修改商品的平台类型枚举
     *
     * @param goodsPolyType 商品频台类型
     * @param matchIds      匹配id列表
     */
    void updateGoodsPolyType(@Param("goodsPolyType") Integer goodsPolyType, @Param("matchIds") List<Integer> matchIds);

    /**
     * 批量更新库存同步结果
     *
     * @param apiSysMatchResults 库存同步结果列表
     */
    void updateSyncStockResult(@Param("apiSysMatchResults") List<StockSyncApiSysMatchResult> apiSysMatchResults);

    // endregion

    // region 新增

    // endregion

    // region 删除

    /**
     * 根据主键删除商品匹配
     *
     * @param ids 商品匹配表id - 列表
     */
    void deleteById(@Param("ids") List<Integer> ids);

    // endregion

}
