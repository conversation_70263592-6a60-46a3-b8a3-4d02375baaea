package com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.TriggerTypeEnum;

/**
 * 下载售后单入参
 *
 * <AUTHOR>
 * @date 2024/9/13 下午4:34
 */
public class LoadAfterSaleParam {
    /**
     * 会员
     */
    private String member;

    /**
     * 平台
     */
    private PolyPlatEnum plat;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 触发类型
     */
    private TriggerTypeEnum triggerType;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 下载基础参数
     */
    private AfterSaleLoadArgs loadArgs;

    //region get/set

    public String getMember() {
        return member;
    }

    public void setMember(String member) {
        this.member = member;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public TriggerTypeEnum getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(TriggerTypeEnum triggerType) {
        this.triggerType = triggerType;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public AfterSaleLoadArgs getLoadArgs() {
        return loadArgs;
    }

    public void setLoadArgs(AfterSaleLoadArgs loadArgs) {
        this.loadArgs = loadArgs;
    }

    public PolyPlatEnum getPlat() {
        return plat;
    }

    public void setPlat(PolyPlatEnum plat) {
        this.plat = plat;
    }

    //endregion
}
