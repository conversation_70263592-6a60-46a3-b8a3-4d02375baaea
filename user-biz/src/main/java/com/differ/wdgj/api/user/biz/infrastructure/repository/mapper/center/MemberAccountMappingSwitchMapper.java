package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.center;

import com.differ.wdgj.api.component.multidb.annotation.DataSourceAutoSwitcher;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.GloServerRdsMappingDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.MemberAccountMappingDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.BasicOperateMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.SwitchDbContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 外部账号映射
 *
 * <AUTHOR>
 * @date 2024/3/1 14:07
 */
@Repository
@DataSourceAutoSwitcher
public class MemberAccountMappingSwitchMapper {

    @Autowired
    private MemberAccountMappingMapper mapper;
    public MemberAccountMappingDO selectByUserName(SwitchDbContext context, String username){
        return mapper.selectByUserName(username);
    }
}
