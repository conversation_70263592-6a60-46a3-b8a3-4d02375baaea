package com.differ.wdgj.api.user.biz.domain.apicall.data;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * API调用头信息
 *
 * <AUTHOR>
 * @date 2023/2/13 15:40
 */
public class ApiCallHeader {

    /**
     * 构造方法
     *
     * @param memberName 会员名
     * @param shopId     店铺Id
     */
    public ApiCallHeader(String memberName, int shopId) {
        this.memberName = memberName;
        this.shopId = shopId;
    }

    /**
     * 构造方法
     *
     * @param memberName    会员名
     * @param shopId        店铺Id
     * @param polyContextId 菠萝派上下文Id
     */
    public ApiCallHeader(String memberName, Integer shopId, String polyContextId) {
        this.memberName = memberName;
        this.shopId = shopId;
        this.polyContextId = polyContextId;
    }

    /**
     * 会员名
     */
    private String memberName;

    /**
     * 店铺Id
     */
    private int shopId;

    /**
     * 菠萝派上下文Id
     */
    private String polyContextId;
    /**
     * 获取额外参数
     *
     * @return 结果
     */
    public Map<String, String> getExtraData() {

        // 初始化额外参数
        Map<String, String> extraData = new HashMap<>();

        // 添加菠萝派上下文Id
        if (StringUtils.isNotEmpty(this.polyContextId)) {
            extraData.put("contextId", this.polyContextId);
        }

        return extraData;
    }

    // region getter & setter

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public String getPolyContextId() {
        return polyContextId;
    }

    public void setPolyContextId(String polyContextId) {
        this.polyContextId = polyContextId;
    }

    // endregion
}
