package com.differ.wdgj.api.user.biz.domain.stock.notice.erp.processor.handler;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.notice.ApiSysMatchNotice;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;

/**
 * [平台商品库存变动通知]匹配级处理插件接口
 *
 * <AUTHOR>
 * @date 2024/11/12 下午6:59
 */
public interface INoticeProcessHandle {
    /**
     * 匹配数据转换为[平台商品库存变动通知]
     *
     * @param shopContext       店铺上下文
     * @param apiSysMatchNotice 匹配信息扩展
     * @return 匹配处理结果
     */
    StockContentResult<?> process(StockSyncContext shopContext, ApiSysMatchNotice apiSysMatchNotice);
}
