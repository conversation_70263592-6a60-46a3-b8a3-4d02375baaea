package com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core;

import com.differ.jackyun.framework.component.basic.interceptor.CommonContextHolder;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogContextIdUtil;
import com.differ.wdgj.api.user.biz.infrastructure.common.TimeEventCounter;
import com.differ.wdgj.api.user.biz.infrastructure.common.TimeSegmentCounter;
import com.differ.wdgj.api.user.biz.infrastructure.data.event.ThreadPoolBlockingQueueAlarmEvent;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @Description 线程池，附加新功能
 * 1.报警触发
 * 2.支持带吉客号的 {@link MemberAble}
 * <AUTHOR>
 * @Date 2021/11/19 14:11
 */
public class BaseThreadPoolExecutor extends ThreadPoolExecutor {

    private static Logger log = LoggerFactory.getLogger(BaseThreadPoolExecutor.class);

    /**
     * 区间报警计数器
     */
    private TimeEventCounter alarmEventCounter = new TimeSegmentCounter();

    /**
     * 线程枚举
     */
    private TaskEnum taskEnum;

    /**
     * 执行中的任务
     */
    private Map<Runnable, CustomThread> runningTaskThreads = new ConcurrentHashMap<>();

    public BaseThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, TaskEnum taskEnum, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, new ThreadFactoryBuilder().setNameFormat(String.format("%s-%%d", taskEnum.getNamePreFix())).build(), handler);
        this.taskEnum = taskEnum;
    }

    public int getBlockingQueueAlarmLimit() {
        return taskEnum.getBlockingQueueAlarmLimit();
    }

    public int getQueueCapacity() {
        return taskEnum.getQueueCapacity();
    }

    public String getNameDescription() {
        return String.format("%s(%s)", taskEnum.getNamePreFix(), taskEnum.getDescription());
    }

    /**
     * 执行任务前的处理
     *
     * @param t
     * @param r
     */
    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        try {
            // 执行中任务监控添加
            runningTaskThreads.put(r, CustomThread.create(t));

            if (r instanceof MemberTaskAble) {
                // 绑定吉客号和日志ID
                MemberTaskAble task = (MemberTaskAble) r;
                LogContextIdUtil.setLogId(task.getLoggerSn());
            }

            // 报警监控
            int blockingQueueAlarmLimit = taskEnum.getBlockingQueueAlarmLimit();
            if (blockingQueueAlarmLimit > 0) {
                int size = getQueue().size();
                if (size > blockingQueueAlarmLimit) {
                    if (alarmEventCounter.checkOnEvent()) {
                        // 触发阻塞队列报警事件
                        BeanContextUtil.publishEvent(new ThreadPoolBlockingQueueAlarmEvent(this.getNameDescription(), blockingQueueAlarmLimit, size, this.getRunningTasks()));
                    }
                }
            }
        } catch (Throwable th) {
            log.error("线程池执行前置处理异常", th);
        }
    }

    /**
     * 执行任务后的处理
     *
     * @param r
     * @param t
     */
    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        try {
            // 执行中任务监控添加
            runningTaskThreads.remove(r);

            // 错误日志
            if (t != null) {
                String errorInfo = String.format("%s线程池,业务执行有异常,日志：%d", this.getNameDescription(), CommonContextHolder.getLoggerSn());
                if (r instanceof MemberTaskAble) {
                    errorInfo = String.format("%s,吉客号:%s", errorInfo, ((MemberTaskAble) r).getMember());
                }
                log.error(errorInfo, t);
            }
        } catch (Throwable th) {
            log.error("线程池执行后置处理异常", th);
        }
    }

    /**
     * 获取执行中的任务
     *
     * @return
     */
    public Runnable[] getRunningTasks() {
        return runningTaskThreads.entrySet().stream().map(r -> r.getKey()).collect(Collectors.toList()).toArray(new Runnable[0]);
    }

    /**
     * 获取执行中的任务
     *
     * @return
     */
    public Set<Map.Entry<Runnable, CustomThread>> getRunningTaskThreads() {
        return runningTaskThreads.entrySet();
    }

    /**
     * 获取执行中和队列中的任务数
     *
     * @return
     */
    public ExecuteAndWaitCount getExecuteAndWait() {
        int executeCount = this.runningTaskThreads.size();
        int waitCount = this.getQueue().size();
        return new ExecuteAndWaitCount(waitCount, executeCount);
    }

    /**
     * 移除执行完成的任务，用于外部检测到遗留的任务，任务状态有：终止状态
     *
     * @param r
     */
    public boolean removeFinishTask(Runnable r) {
        CustomThread customThread = runningTaskThreads.get(r);
        if (customThread == null || customThread.getThread() == null) {
            return false;
        }
        Thread.State state = customThread.getThread().getState();
        if (state == Thread.State.TERMINATED) {
            CustomThread remove = runningTaskThreads.remove(r);
            boolean existsQueue = this.remove(r);
            log.warn("移除执行完成的任务，runningTaskThreads={}, workQueue={}", remove, existsQueue);
            return true;
        }
        return false;
    }

    /**
     *  判断任务是否在执行中或队列中
     * @param r
     * @return true: 存在
     */
    public boolean contains(Object r) {
        if(this.getQueue().contains(r)){
            return true;
        }
        return runningTaskThreads.containsKey(r);
    }

    /**
     * 执行中和队列中的任务数
     */
    public static class ExecuteAndWaitCount {
        /**
         * 队列中的的任务数
         */
        private int waitCount;
        /**
         * 执行中的任务数
         */
        private int executeCount;

        public ExecuteAndWaitCount(int waitCount, int executeCount) {
            this.waitCount = waitCount;
            this.executeCount = executeCount;
        }

        public int getWaitCount() {
            return waitCount;
        }

        public int getExecuteCount() {
            return executeCount;
        }

        public int getTotalCount() {
            return waitCount + executeCount;
        }
    }
}
