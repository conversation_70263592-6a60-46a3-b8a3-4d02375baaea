package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyTypeEnum;

import java.util.Objects;

/**
 * 内存缓存Key - 配置键
 *
 * <AUTHOR>
 * @date 2024-03-11 19:59
 */
public class DbConfigKey {

    /**
     * 获取或设置 配置键。
     */
    private ConfigKeyEnum configKey;

    /**
     * 获取或设置 配置版本号。
     */
    private String version;

    /**
     * 获取或设置 ConfigTypes枚举值之一[Api=1;]。
     */
    private ConfigKeyTypeEnum configType;


    //region 重写基类方法

    /**
     * hashcode
     *
     * @return hashcode
     */
    @Override
    public int hashCode() {
        return Objects.hash(this.configKey, this.version, this.configType);
    }

    /**
     * equals
     *
     * @param obj obj
     * @return 是否相等
     */
    @Override
    public boolean equals(Object obj) {

        if (this == obj) {
            return true;
        }

        if (!(obj instanceof DbConfigKey)) {
            return false;
        }

        DbConfigKey key = (DbConfigKey) obj;
        return this.getConfigKey().equals(key.getConfigKey())
                && this.getVersion().equals(key.getVersion())
                && this.getConfigType() == key.getConfigType();
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DbConfigKey{");
        sb.append("configKey=").append(configKey);
        sb.append(", version='").append(version).append('\'');
        sb.append(", configType=").append(configType);
        sb.append('}');
        return sb.toString();
    }

    //endregion

    //region get/set

    public ConfigKeyEnum getConfigKey() {
        return configKey;
    }

    public void setConfigKey(ConfigKeyEnum configKey) {
        this.configKey = configKey;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public ConfigKeyTypeEnum getConfigType() {
        return configType;
    }

    public void setConfigType(ConfigKeyTypeEnum configType) {
        this.configType = configType;
    }
    //endregion
}
