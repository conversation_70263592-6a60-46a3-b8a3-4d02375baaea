package com.differ.wdgj.api.user.biz.domain.stock.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.NameEnum;
import com.differ.wdgj.api.component.util.enums.ValueEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.bizfeature.PlatBizFeatureConfigModeEnum;

/**
 * 库存同步 - 同步模式
 *
 * <AUTHOR>
 * @date 2024-03-11 17:02
 */
public enum SyncStockFlagEnum implements ValueEnum {

    /**
     * 不更新标识
     */
    None("不更新标识", -1),

    /**
     * 全量
     */
    Whole("全量", 0),

    /**
     * 增量
     */
    Increment("增量", 1),

    /**
     * 限制（一段时间内不可自动库存同步，可以手动库存同步）
     */
    Limit("限制", 2),

    /**
     * 禁止（一段时间内不可库存同步，包括手动和自动）
     */
    Forbid("禁止", 3),

    ;


    SyncStockFlagEnum(String title, Integer value){
        this.title = title;
        this.value = value;
    }

    /**
     * 标题
     */
    private String title;

    /**
     * 值
     */
    private Integer value;

    /**
     * 获取枚举值
     *
     * @return 枚举值
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    /**
     * 获取标题
     *
     * @return 标题
     */
    public String getTitle() {
        return this.title;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static SyncStockFlagEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, SyncStockFlagEnum.class, EnumConvertType.VALUE);
    }
}
