//package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core;
//
//import com.differ.newapi.base.managers.SpringResolveManager;
//import org.springframework.stereotype.Component;
//
///**
// * 通用缓存代理。
// * <p>
// * 特别说明：
// * 1. 此缓存代理适用于零散、结构不统一的缓存。
// * 2. 缓存Hash键建议配置在此类静态变量区。
// *
// * <AUTHOR>
// * @since 2019-12-24  15:01
// */
//@Component("CommonDataCacheProxy")
//public class CommonDataProxy extends AbstractHashCache<CommonEntity> {
//    // region 构造器
//
//    public CommonDataProxy() {
//        this.cacheKey = DataCacheKeyEnum.CUSTOMER_COMMON.getPolyCode();
//        this.hashItemType = CommonEntity.class;
//        this.splitCacheKey = false;
//    }
//
//    // endregion
//
//    //region 静态变量
//
//    /**
//     * 缓存Hash键(工作任务推送数据状态)
//     */
//    public static final String HASHKEY_WORKERPOSTDATASTATUS = "worker.postdatastatus.%s";
//
//    //endregion
//
//    //region 获取缓存数据
//
//    /**
//     * 获取缓存数据。
//     *
//     * @param hashKey Hash键
//     * @return 缓存数据
//     */
//    public String getData(String hashKey) {
//        return this.hashGetStrValue(hashKey);
//    }
//
//    /**
//     * 同步缓存数据。
//     *
//     * @param hashKey Hash键
//     * @param value   缓存值
//     */
//    public void syncData(String hashKey, String value) {
//        this.hashSyncStrValue(hashKey, value);
//    }
//
//    //endregion
//
//    //region 获取单例
//
//    /**
//     * 获取缓存代理对象。
//     *
//     * @return 缓存代理对象
//     */
//    public static CommonDataProxy get() {
//        return SpringResolveManager.resolve(CommonDataProxy.class);
//    }
//
//    //endregion
//
//    //region 专用增强方法
//
//    //endregion
//}
