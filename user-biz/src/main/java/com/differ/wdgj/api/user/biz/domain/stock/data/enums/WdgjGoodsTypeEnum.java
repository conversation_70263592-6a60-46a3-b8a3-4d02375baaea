package com.differ.wdgj.api.user.biz.domain.stock.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.NameEnum;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 网店管家云端商品类型
 *
 * <AUTHOR>
 * @date 2024/8/8 下午4:57
 */
public enum WdgjGoodsTypeEnum implements NameEnum, ValueEnum {
    /**
     * 标准商品
     */
    Standard(2, "标准商品"),

    /**
     * 组合装
     */
    AssembleGoods(3, "组合装"),

    /**
     * 批次商品
     */
    BatchGoods(3, "批次商品"),

    ;

    /**
     * 构造
     *
     * @param value 库存同步同步类型值
     * @param name  库存同步同步类型名
     */
    WdgjGoodsTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 库存同步同步类型值
     */
    private final Integer value;

    /**
     * 库存同步同步类型名
     */
    private final String name;


    /**
     * 获取库存同步状态值
     *
     * @return 库存同步状态值
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    /**
     * 获取枚举名称
     *
     * @return 枚举名称
     */
    @Override
    public String getName() {
        return this.name;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static WdgjGoodsTypeEnum create(Integer value) {
        return EnumConvertCacheUtil.convert(value, WdgjGoodsTypeEnum.class, EnumConvertType.VALUE);
    }
}
