package com.differ.wdgj.api.user.biz.domain.apicall.filter;

import com.differ.wdgj.api.user.biz.domain.apicall.AbstractApiCall;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatResponseBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallExtendedInfo;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * API调用过滤器链
 *
 * <AUTHOR>
 * @date 2022-03-14 15:50
 */
public class ApiCallFilterChain<RequestBizData extends BasePlatRequestBizData, ResponseBizData extends BasePlatResponseBizData>
        implements IApiCallFilter<RequestBizData, ResponseBizData> {

    /**
     * 构造方法
     *
     * @param filters 过滤器
     * @param apiCall API调用实例
     */
    public ApiCallFilterChain(List<IApiCallFilter<RequestBizData, ResponseBizData>> filters, AbstractApiCall<RequestBizData, ResponseBizData> apiCall) {
        this.filters = filters;
        this.apiCall = apiCall;
    }

    /**
     * 过滤器
     */
    private final List<IApiCallFilter<RequestBizData, ResponseBizData>> filters;

    /**
     * API调用实例
     */
    private final AbstractApiCall<RequestBizData, ResponseBizData> apiCall;

    /**
     * 链式索引
     */
    private int index = 0;

    /**
     * 执行过滤
     *
     * @param context          上下文
     * @param requestBizData   请求业务数据
     * @param callExtendedInfo 请求扩展数据
     * @param response         响应
     * @param filterChain      过滤器链
     */
    @Override
    public void doFilter(
            ApiCallContext context,
            RequestBizData requestBizData,
            ApiCallExtendedInfo callExtendedInfo,
            ApiCallResponse<ResponseBizData> response,
            ApiCallFilterChain<RequestBizData, ResponseBizData> filterChain
    ) {

        // 过滤器全部执行完成，调用实例方法
        if (this.index == this.filters.size()) {
            ApiCallResponse<ResponseBizData> temp = this.apiCall.onRequest(context, requestBizData, callExtendedInfo);
            BeanUtils.copyProperties(temp, response);
            return;
        }

        // 链式执行
        IApiCallFilter<RequestBizData, ResponseBizData> filter = filters.get(index);
        this.index++;
        filter.doFilter(context, requestBizData, callExtendedInfo, response, filterChain);
    }
}
