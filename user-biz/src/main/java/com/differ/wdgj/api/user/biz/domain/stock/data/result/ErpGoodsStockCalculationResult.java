package com.differ.wdgj.api.user.biz.domain.stock.data.result;

import java.math.BigDecimal;

/**
 * erp商品库存计算结果
 *
 * <AUTHOR>
 * @date 2025/6/17 10:10
 */
public class ErpGoodsStockCalculationResult {
    /**
     * 是否成功
     */
    protected boolean success;

    /**
     * 错误信息
     */
    protected String message;

    /**
     * 库存数量
     */
    private BigDecimal stockCount;

    /**
     * 库存详情
     */
    private String originalDetailCount;

    //region 静态方法

    /**
     * 成功结果
     *
     * @param stockCount 库存数量
     * @param originalDetailCount       菠萝派库存详情库存同步商品级别列表
     * @return 结果
     */
    public static ErpGoodsStockCalculationResult success(BigDecimal stockCount, String originalDetailCount) {
        ErpGoodsStockCalculationResult result = new ErpGoodsStockCalculationResult();
        result.setSuccess(true);
        result.setStockCount(stockCount);
        result.setOriginalDetailCount(originalDetailCount);
        return result;
    }

    /**
     * 失败结果
     *
     * @param message 错误信息
     * @return 结果
     */
    public static ErpGoodsStockCalculationResult failed(String message) {
        ErpGoodsStockCalculationResult result = new ErpGoodsStockCalculationResult();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
    //endregion

    //region 公共方法
    public boolean isFailed() {
        return !success;
    }
    //endregion

    //region get/set
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BigDecimal getStockCount() {
        return stockCount;
    }

    public void setStockCount(BigDecimal stockCount) {
        this.stockCount = stockCount;
    }

    public String getOriginalDetailCount() {
        return originalDetailCount;
    }

    public void setOriginalDetailCount(String originalDetailCount) {
        this.originalDetailCount = originalDetailCount;
    }

    //endregion
}
