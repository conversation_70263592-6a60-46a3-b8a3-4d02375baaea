package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.callback.fail;

import com.differ.wdgj.api.component.util.hash.HashUtil;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.AlarmOperator;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.data.AlarmContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AlarmIntervalTypeEnum;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueHeader;

/**
 * 多队列失败回调：重试最大次数失败时回调
 *
 * <AUTHOR>
 * @date 2024/4/3 14:38
 */
public class DefaultMultiConsumerFailCallback<T> extends com.differ.wdgj.api.user.biz.tasks.mq.multi.core.callback.fail.AbstractMultiConsumerFailCallback<T> {

    @Override
    public void multiFailCallback(T message, QueueHeader header) {
        try {
            String messageJson = JsonUtils.toJson(message);
            String alarmMessage = String.format("队列：%s，重试次数：%d,失败消息：%s", header.getHandlerCode(), header.getRetryCount(), messageJson);
            AlarmContent content = AlarmContent.build(AlarmIntervalTypeEnum.MULTI_FAIL_CALLBACK, alarmMessage);
            AlarmOperator.singleton().alarmInterval(AlarmIntervalTypeEnum.MULTI_FAIL_CALLBACK, String.valueOf(HashUtil.murMurHash(messageJson)), content);
        } catch (Exception e) {
            log.error("多队列失败回调异常", e);
        }
    }

}