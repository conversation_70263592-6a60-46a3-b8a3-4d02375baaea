package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.goodsexchange;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseExchangeGoodInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.AfterSaleGoodsMatchResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceExchangeGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.AfterSaleGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractExchangeGoodsConvertHandle;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.wdgj.MatchOrderGoodsResultDto;

/**
 * 换货单-换货商品商品匹配插件
 *
 * <AUTHOR>
 * @date 2024/8/8 下午4:47
 */
public class ExchangeGoodsMatchHandle extends AbstractExchangeGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseExchangeGoodInfo> {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public ExchangeGoodsMatchHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类逻辑

    /**
     * 转换商品级信息
     *
     * @param orderItem     原始售后单数据
     * @param goodsItem     原始售后退货商品数据
     * @param targetOrder   目标售后单数据
     * @param exchangeGoods 目标售后换货商品数据
     * @return 结果
     */
    @Override
    protected GoodsConvertHandleResult convertGoods(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> orderItem, SourceExchangeGoodsItem<BusinessGetExchangeResponseExchangeGoodInfo> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods) {
        // 调用存储过程进行商品匹配
        AfterSaleGoodsMatchResult matchResult = AfterSaleGoodsMatchOperation.exchangeGoodsErpMatch(context, orderItem.getAfterSaleNo(), goodsItem.getGoodsSysMatchItem(), exchangeGoods);
        if (matchResult.isSuccess() && matchResult.getGoodsMatchResult() != null) {
            MatchOrderGoodsResultDto goodsMatchInfo = matchResult.getGoodsMatchResult();
            exchangeGoods.setGoodsId(goodsMatchInfo.getGoodsId());
            exchangeGoods.setSpecId(goodsMatchInfo.getSpecId());
            exchangeGoods.setbFit(goodsMatchInfo.getbFit());
        }
        return GoodsConvertHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "换货单-换货商品商品匹配插件";
    }
    //endregion
}
