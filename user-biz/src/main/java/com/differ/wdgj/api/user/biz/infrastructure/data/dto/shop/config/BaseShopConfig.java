package com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config;

import com.alibaba.fastjson.annotation.JSONField;
import com.differ.wdgj.api.component.util.json.core.StringDateTimeDeserializer;

import java.time.LocalDateTime;

/**
 * 基础店铺配置实体
 *
 * <AUTHOR>
 * @date 2024/7/15 上午11:35
 */
public class BaseShopConfig {
    /**
     * 外部用户名
     */
    private String outAccount;

    /**
     * 店铺Id
     */
    private Integer apiShopId;

    /**
     * 外部店铺id
     */
    private Integer shopId;

    /**
     * 更新时间
     */
    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime modifiedTime;

    //region get/set
    public String getOutAccount() {
        return outAccount;
    }

    public void setOutAccount(String outAccount) {
        this.outAccount = outAccount;
    }

    public Integer getApiShopId() {
        return apiShopId;
    }

    public void setApiShopId(Integer apiShopId) {
        this.apiShopId = apiShopId;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }
    //endregion
}
