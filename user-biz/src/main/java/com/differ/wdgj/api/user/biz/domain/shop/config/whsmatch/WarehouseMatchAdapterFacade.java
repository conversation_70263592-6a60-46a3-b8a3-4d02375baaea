package com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch.data.CommonWarehouseMatchDTO;
import com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch.plugins.NormalWarehouseMatchAdapter;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;

import java.util.Collections;
import java.util.Set;

/**
 * 仓库匹配数据适配器 工厂
 * <a href="https://s.jkyun.biz/zFUrGI9">仓库匹配数据存储逻辑</a>
 *
 * <AUTHOR>
 * @date 2024/12/19 下午7:02
 */
public class WarehouseMatchAdapterFacade {
    //region 构造
    private WarehouseMatchAdapterFacade() {
    }
    //endregion

    //region 公共静态方法
    /**
     * 获取店铺级仓库匹配数据列表
     *
     * @param memberName 会员名
     * @param shopId     管家店铺Id
     * @return 仓库匹配数据列表
     */
    public static Set<CommonWarehouseMatchDTO> getAll(String memberName, int shopId) {
        ApiShopBaseDto shopBase = ShopInfoUtils.singleByOutShopId(memberName, shopId);
        if(shopBase == null){
            return Collections.emptySet();
        }

        // 获取店铺级仓库匹配数据
        return createAdapter(shopBase).getListByShop(memberName, shopId);
    }
    //endregion

    //region 工厂
    /**
     * 创建仓库匹配数据适配器
     *
     * @param shopBase   店铺基础信息
     * @return 仓库匹配数据适配器
     */
    private static IWarehouseMatchAdapter createAdapter(ApiShopBaseDto shopBase) {
        PolyPlatEnum plat = shopBase.getPlat();
        switch (plat){
            default:
                return new NormalWarehouseMatchAdapter();
        }
    }
    //endregion
}
