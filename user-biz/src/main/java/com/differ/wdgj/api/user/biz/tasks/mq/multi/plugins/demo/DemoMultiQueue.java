package com.differ.wdgj.api.user.biz.tasks.mq.multi.plugins.demo;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.AbstractMultiQueue;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.DelayGradeEnum;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueHeader;
import com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data.QueueResult;


/**
 * 多队列示例：普通（非延迟和重试）
 *
 * <AUTHOR>
 * @date 2024/3/14 14:25
 */
//@ApiMultiMQ(code = "api.multi.demo",
//        subQueues = {SubQueueEnum.JMQ, SubQueueEnum.JMQ_BACK, SubQueueEnum.KAFKA,},
//        sitesToSend = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
//        sitesToReceive = {SiteTypeCodeConst.WDGJ_API_BUSINESS},
//        jmqBatch = @JmqBatchListenAnnotation(value = "test.extra",jmqMatchListenStrategy = ContainsMatchListenStrategy.class)
//
//)
//@ConditionalOnSite(sites = {SiteTypeCodeConst.WDGJ_API_BUSINESS})
//@ConditionalOnEnvType({SystemEnvTypeEnum.DEV})
public class DemoMultiQueue extends AbstractMultiQueue<DemoData> {

    /**
     * 发送消息
     *
     * @param message
     */
    @Override
    public void sendMulti(DemoData message) {
        QueueHeader header = new QueueHeader();
        // 同一队列中存在不同的数据格式类型时可设置数据类型，否则不需要设置头信息
        header.setDataType("demo");
        // 头信息可支持平台
        header.setDataPlat(PolyPlatEnum.BUSINESS_Taobao.getValue());
        super.sendMulti(message, header);
    }

    @Override
    public QueueResult receiveMulti(DemoData message, QueueHeader header) {
        log.info("收到类型：{},消息:{}", header.getDataType(), message);
        return QueueResult.ACK;
    }

    /**
     * 延迟消息的延迟级别，可重写来支持动态设定，默认使用注解配置的值
     *
     * @param header 队列头信息上下文
     * @return 延迟级别
     */
    @Override
    protected DelayGradeEnum getDelayGrade(QueueHeader header) {
        // 正式业务一般通过配置来动态指定，JMQ时，配置的值必须是JMQ配置值的范围内
        int retryCount = header.getRetryCount();
        if (retryCount < DelayGradeEnum.GRADE_2H.getGrade()) {
            return DelayGradeEnum.getByGrade(retryCount);
        }
        return DelayGradeEnum.GRADE_2H;
    }

}
