package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;

import java.util.Objects;

/**
 * 会员库店铺配置唯一键
 *
 * <AUTHOR>
 * @date 2024-06-25 17:30
 */
public class ApiShopConfigKey {
    /**
     * 外部用户名
     */
    private String outAccount;

    /**
     * 店铺Id
     */
    private Integer shopId;

    /**
     * 业务类型
     */
    private ApiShopConfigBizTypes bizType;

    /**
     * 创建实例
     *
     * @param outAccount 外部用户名
     * @param shopId     店铺Id
     * @param bizType    业务类型
     * @return 结果
     */
    public static ApiShopConfigKey create(String outAccount, Integer shopId, ApiShopConfigBizTypes bizType) {
        ApiShopConfigKey key = new ApiShopConfigKey();
        key.setOutAccount(outAccount);
        key.setShopId(shopId);
        key.setBizType(bizType);
        return key;
    }

    /**
     * 创建实例
     *
     * @param shopId  店铺Id
     * @param bizType 业务类型
     * @return 结果
     */
    public static ApiShopConfigKey create(Integer shopId, ApiShopConfigBizTypes bizType) {
        ApiShopConfigKey key = new ApiShopConfigKey();
        key.setShopId(shopId);
        key.setBizType(bizType);
        return key;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ApiShopConfigKey that = (ApiShopConfigKey) o;
        return Objects.equals(outAccount, that.outAccount) && Objects.equals(shopId, that.shopId) && bizType == that.bizType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(outAccount, shopId, bizType);
    }

    // region get/set

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public ApiShopConfigBizTypes getBizType() {
        return bizType;
    }

    public void setBizType(ApiShopConfigBizTypes bizType) {
        this.bizType = bizType;
    }

    public String getOutAccount() {
        return outAccount;
    }

    public void setOutAccount(String outAccount) {
        this.outAccount = outAccount;
    }
    // endregion
}
