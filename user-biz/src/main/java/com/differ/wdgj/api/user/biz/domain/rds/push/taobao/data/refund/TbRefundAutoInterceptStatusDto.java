package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund;

import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.enums.LogisticInterceptStatusEnum;

/**
 * refund_get_response.Refund.attribute 属性内部部分结构
 * <a href="https://www.yuque.com/upn61c/ymbugx/rxft9ioho4qngzog">平台文档</a>
 *
 * <AUTHOR>
 * @date 2025/4/7 上午10:04
 */
public class TbRefundAutoInterceptStatusDto {
    /**
     * 子订单号
     */
    private String subBizOrderId;

    /**
     * 拦截时间（时间戳）
     */
    private String interceptDate;

    /**
     * 商品截单状态
     * @see LogisticInterceptStatusEnum
     */
    private String logisticInterceptEnum;

    /**
     * 截单失败原因
     */
    private String interceptFailCode;

    /**
     * 运单号
     */
    private String mailNo;

    /**
     * 物流公司code
     */
    private String cpCode;

    /**
     * 物流公司名称
     */
    private String cpName;


    //region get/set
    public String getSubBizOrderId() {
        return subBizOrderId;
    }

    public void setSubBizOrderId(String subBizOrderId) {
        this.subBizOrderId = subBizOrderId;
    }

    public String getInterceptDate() {
        return interceptDate;
    }

    public void setInterceptDate(String interceptDate) {
        this.interceptDate = interceptDate;
    }

    public String getLogisticInterceptEnum() {
        return logisticInterceptEnum;
    }

    public void setLogisticInterceptEnum(String logisticInterceptEnum) {
        this.logisticInterceptEnum = logisticInterceptEnum;
    }

    public String getInterceptFailCode() {
        return interceptFailCode;
    }

    public void setInterceptFailCode(String interceptFailCode) {
        this.interceptFailCode = interceptFailCode;
    }

    public String getMailNo() {
        return mailNo;
    }

    public void setMailNo(String mailNo) {
        this.mailNo = mailNo;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getCpName() {
        return cpName;
    }

    public void setCpName(String cpName) {
        this.cpName = cpName;
    }
    //endregion
}
