package com.differ.wdgj.api.user.biz.infrastructure.data.enums;

import com.differ.wdgj.api.component.util.enums.ValueEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;

/**
 * 管家订单的平台类型枚举
 * 新平台不再新增，值使用{@link PolyPlatEnum} 平台值platValue
 *
 * <AUTHOR>
 * @date 2024-03-05 15:05
 */
public enum OrderPolyEnum implements ValueEnum {


    ;

    @Override
    public Integer getValue() {
        return null;
    }
}
