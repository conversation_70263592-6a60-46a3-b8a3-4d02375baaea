package com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins;

import com.alibaba.fastjson.annotation.JSONField;
import com.differ.wdgj.api.component.util.enums.EnumCodeValueDeserializer;
import com.differ.wdgj.api.component.util.enums.EnumCodeValueWriteSerializer;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockMultiWhsModeEnum;

/**
 * 平台业务特性 - 约束内容 - 库存同步
 *
 * <AUTHOR>
 * @date 2024/12/26 下午3:41
 */
public class SyncStockContent {
    /**
     * 多仓库存同步模式
     */
    @JSONField(serializeUsing = EnumCodeValueWriteSerializer.class, deserializeUsing = EnumCodeValueDeserializer.class)
    private StockMultiWhsModeEnum multiWhsMode;

    //region get/set

    public StockMultiWhsModeEnum getMultiWhsMode() {
        return multiWhsMode;
    }

    public void setMultiWhsMode(StockMultiWhsModeEnum multiWhsMode) {
        this.multiWhsMode = multiWhsMode;
    }

    //endregion
}
