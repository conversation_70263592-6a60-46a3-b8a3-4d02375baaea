package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.wdgj;

import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.AbstractHashReadOnlyCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.DataCacheKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.WdgjYunMemberDataSourceDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 网店管家云端 - 会员数据库链接缓存 - 只读
 *
 * <AUTHOR>
 * @date 2024-03-26 14:40
 */
public class WdgjYunMemberDataSourceCache extends AbstractHashReadOnlyCache<WdgjYunMemberDataSourceDto> {

    //region 单例

    /**
     * 构造方法
     */
    private WdgjYunMemberDataSourceCache() {
        super(DataCacheKeyEnum.WDGJYUN_BASICCFG_MEMBER_DATASOURCE.getOriginalCode());
    }

    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static WdgjYunMemberDataSourceCache singleton() {
        return WdgjYunMemberDataSourceCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final WdgjYunMemberDataSourceCache instance;

        private SingletonEnum() {
            instance = new WdgjYunMemberDataSourceCache();
        }
    }
    //endregion

    //region 重写基类方法

    /**
     * 获取值类型
     *
     * @return 值类型
     */
    @Override
    public Class<WdgjYunMemberDataSourceDto> getValueClazz() {
        return WdgjYunMemberDataSourceDto.class;
    }
    //endregion

    /**
     * 获取 会员数据库链接缓存
     *
     * @param outAccount 外部会员名
     * @return 会员数据库链接缓存实体
     */
    public WdgjYunMemberDataSourceDto getMemberDataSource(String outAccount) {
        return getCache(cacheKey, outAccount);
    }

    /**
     * 获取所有有效会员
     *
     * @return
     */
    public List<String> getAllMembers() {
        List<String> members = new ArrayList<>();
        this.cacher.hashScanAll(cacheKey, 100, (key, value) -> members.add(key));
        return members;
    }
}
