package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;

import java.time.LocalDateTime;

/**
 * 会员店铺配置内存缓存实体
 *
 * <AUTHOR>
 * @date 2024-06-25 17:49
 */
public class ApiShopConfigLocalDto {
    /**
     * api店铺Id
     */
    private Integer apiShopId;

    /**
     * 外部店铺id
     */
    private Integer shopId;

    /**
     * 业务类型
     */
    private ApiShopConfigBizTypes bizType;

    /**
     * 配置约束，Json数据
     */
    private Object configValue;

    /**
     * 更新时间
     */
    private LocalDateTime modifiedTime;

    // region get/set

    public Integer getApiShopId() {
        return apiShopId;
    }

    public void setApiShopId(Integer apiShopId) {
        this.apiShopId = apiShopId;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public ApiShopConfigBizTypes getBizType() {
        return bizType;
    }

    public void setBizType(ApiShopConfigBizTypes bizType) {
        this.bizType = bizType;
    }

    public Object getConfigValue() {
        return configValue;
    }

    public void setConfigValue(Object configValue) {
        this.configValue = configValue;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

// endregion
}
