package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plugin;

import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncResultPackage;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plugin.preprocess.ISyncStockShopPreProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plugin.shopresult.ISyncStockShopResultProcessor;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 库存同步插件组合类
 * 用于管理所有类型的库存同步插件
 *
 * <AUTHOR>
 * @date 2024-05-22 19:00
 */
public class SyncStockPluginComposite {
    //region 变量
    private static final Logger log = LoggerFactory.getLogger(SyncStockPluginComposite.class);

    /**
     * 前置处理器列表
     */
    private final List<ISyncStockShopPreProcessor> preProcessors;

    /**
     * 结果处理器列表
     */
    private final List<ISyncStockShopResultProcessor> resultProcessors;
    //endregion

    //region 构造
    /**
     * 构造函数
     */
    public SyncStockPluginComposite() {
        this.preProcessors = new ArrayList<>();
        this.resultProcessors = new ArrayList<>();
    }

    /**
     * 构造函数
     *
     * @param preProcessors   前置处理器列表
     * @param resultProcessors 结果处理器列表
     */
    public SyncStockPluginComposite(List<ISyncStockShopPreProcessor> preProcessors, List<ISyncStockShopResultProcessor> resultProcessors) {
        this.preProcessors = preProcessors != null ? preProcessors : new ArrayList<>();
        this.resultProcessors = resultProcessors != null ? resultProcessors : new ArrayList<>();
    }
    //endregion

    //region 前置处理器
    /**
     * 注册前置处理器
     *
     * @param preProcessor 前置处理器
     */
    public void registerPreProcessor(ISyncStockShopPreProcessor preProcessor) {
        if (preProcessor != null) {
            this.preProcessors.add(preProcessor);
        }
    }

    /**
     * 注册多个前置处理器
     *
     * @param preProcessors 前置处理器列表
     */
    public void registerPreProcessors(List<ISyncStockShopPreProcessor> preProcessors) {
        if (CollectionUtils.isNotEmpty(preProcessors)) {
            this.preProcessors.addAll(preProcessors);
        }
    }

    /**
     * 执行前置处理
     *
     * @param context    上下文
     * @param goodsMatch 商品匹配数据
     * @return 处理结果
     */
    public StockContentResult<?> processPreProcessors(StockSyncContext context, GoodsMatchEnhance goodsMatch) {
        if (CollectionUtils.isEmpty(preProcessors)) {
            return StockContentResult.success();
        }

        // 依次执行所有前置处理器
        for (ISyncStockShopPreProcessor processor : preProcessors) {
            try {
                StockContentResult<?> result = processor.preProcess(context, goodsMatch);
                // 如果有处理器返回失败，则直接返回失败结果
                if (result.isFailed()) {
                    return result;
                }
            } catch (Exception e) {
                String message = String.format("【%s】执行前置处理器异常：%s", context.getVipUser(), e.getMessage());
                log.error(message, e);
                return StockContentResult.failed(message);
            }
        }

        return StockContentResult.success();
    }
    //endregion

    //region 结果处理器
    /**
     * 注册结果处理器
     *
     * @param resultProcessor 结果处理器
     */
    public void registerResultProcessor(ISyncStockShopResultProcessor resultProcessor) {
        if (resultProcessor != null) {
            this.resultProcessors.add(resultProcessor);
        }
    }

    /**
     * 注册多个结果处理器
     *
     * @param resultProcessors 结果处理器列表
     */
    public void registerResultProcessors(List<ISyncStockShopResultProcessor> resultProcessors) {
        if (CollectionUtils.isNotEmpty(resultProcessors)) {
            this.resultProcessors.addAll(resultProcessors);
        }
    }

    /**
     * 执行结果处理
     *
     * @param context       上下文
     * @param resultPackage 库存同步结果包
     */
    public void processResultProcessors(StockSyncContext context, StockSyncResultPackage resultPackage) {
        if (CollectionUtils.isEmpty(resultProcessors)) {
            return;
        }

        // 依次执行所有结果处理器
        for (ISyncStockShopResultProcessor processor : resultProcessors) {
            try {
                processor.postProcess(context, resultPackage);
            } catch (Exception e) {
                String message = String.format("【%s】执行结果处理器异常：%s", context.getVipUser(), e.getMessage());
                log.error(message, e);
            }
        }
    }
    //endregion
}
