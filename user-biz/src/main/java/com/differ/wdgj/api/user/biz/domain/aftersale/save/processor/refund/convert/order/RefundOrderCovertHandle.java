package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.order;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleProcessTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.WdgjRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleSaveBizType;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractOrderConvertHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleConstUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleCovertUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleLogUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnLogDO;

import java.time.LocalDateTime;

/**
 * 退货退款单-订单级基础数据转换插件
 *
 * <AUTHOR>
 * @date 2024/7/17 下午4:21
 */
public class RefundOrderCovertHandle extends AbstractOrderConvertHandle<BusinessGetRefundOrderResponseOrderItem> {
    //region 构造
    public RefundOrderCovertHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    public AfterSaleHandleResult convertOrder(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        DbAfterSaleOrderItem dbOrder = sourceOrder.getDbOrder();
        AfterSaleSaveBizType bizType = sourceOrder.getBizType();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();

        // 基础信息
        if (dbOrder.getAfterSaleOrder() != null) {
            afterSaleOrder.setBillId(dbOrder.getAfterSaleOrder().getBillId());
            afterSaleOrder.setCurStatus(dbOrder.getAfterSaleOrder().getCurStatus());
        }
        afterSaleOrder.setShopID(context.getShopId());
        afterSaleOrder.setGetTime(context.getLoadTime());
        // 原始单信息
        if (dbOrder.getApiTrade() != null) {
            afterSaleOrder.setOldBillID(dbOrder.getApiTrade().getBillId());
        }
        afterSaleOrder.setOldTid(ployOrder.getPlatOrderNo());
        afterSaleOrder.setOldOid(ployOrder.getSubPlatOrderNo());
        // 基础售后信息
        afterSaleOrder.setRefundId(ployOrder.getRefundNo());
        afterSaleOrder.setType(covertWdgjRefundType(ployOrder));
        afterSaleOrder.setReturnStatus(covertWdgjRefundStatus(ployOrder));
        afterSaleOrder.setReturnReason(AfterSaleCovertUtils.replaceEmoji(ployOrder.getReason()));
        afterSaleOrder.setRemark(AfterSaleCovertUtils.replaceEmoji(ployOrder.getDesc()));

        // 创建时间
        LocalDateTime createTime = ployOrder.getCreateTime() == null
                ? LocalDateTime.of(1900,1,1,0,0,0,0)
                : ployOrder.getCreateTime();
        afterSaleOrder.setCreatedTime(createTime);
        // 退货物流信息
        afterSaleOrder.setLogisticName(ployOrder.getLogisticName());
        afterSaleOrder.setLogisticNo(ployOrder.getLogisticNo());
        // 金额
        afterSaleOrder.setRefundFee(ployOrder.getRefundAmount());
        // 买家信息
        afterSaleOrder.setCustomerId(ployOrder.getBuyerNick());
        afterSaleOrder.setNickUId(ployOrder.getBuyerOpenUid());
        // ERP支持的特殊售后单类型
        if(AfterSaleConstUtils.ERP_SUPPORT_SPECIAL_AFTER_SALE_TYPE.contains(bizType.getApiAfterSaleOrderType())){
            afterSaleOrder.setPlatRefundType(bizType.getApiAfterSaleOrderType().getDescription());
        }

        // 处理类别
        AfterSaleProcessTypeEnum processType = dbOrder.getAfterSaleOrder() == null
                ? AfterSaleProcessTypeEnum.INSERTER
                : AfterSaleProcessTypeEnum.UPDATE;
        targetOrder.setProcessType(processType);

        // 日志信息
        ApiReturnLogDO apiReturnLog = AfterSaleLogUtils.covertLogNoBillId(context, processType);
        targetOrder.getAfterSaleOrderLogs().add(apiReturnLog);

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "订单级基础数据转换";
    }
    //endregion

    //region 私有方法

    /**
     * 转换网店管家售后状态
     *
     * @param ployOrder 菠萝派退货退款单
     * @return 网店管家售后状态
     */
    private String covertWdgjRefundStatus(BusinessGetRefundOrderResponseOrderItem ployOrder) {
        PolyRefundStatusEnum polyRefundStatusEnum = PolyRefundStatusEnum.create(ployOrder.getRefundStatus());
        if (polyRefundStatusEnum == null) {
            return "未知状态";
        }

        return polyRefundStatusEnum.getWdgjValue();
    }

    /**
     * 转换网店管家售后类别
     *
     * @param ployOrder 菠萝派退货退款单
     * @return 网店管家售后类别
     */
    private int covertWdgjRefundType(BusinessGetRefundOrderResponseOrderItem ployOrder) {
        WdgjRefundTypeEnum wdgjRefundType = ployOrder.getHasGoodsReturn() ? WdgjRefundTypeEnum.REFUND : WdgjRefundTypeEnum.REFUND_PAY;
        return wdgjRefundType.getValue();
    }
    //endregion
}
