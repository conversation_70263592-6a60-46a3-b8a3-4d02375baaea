package com.differ.wdgj.api.user.biz.infrastructure.cache.local.core;

import com.differ.wdgj.api.component.util.loop.LoopUtil;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.SystemErrorCodes;
import com.differ.wdgj.api.user.biz.infrastructure.exception.AppException;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.benmanes.caffeine.cache.RemovalListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 网店管家数据键缓存
 *
 * <AUTHOR>
 * @date 2021/1/13 13:49
 */
public abstract class AbstractLocalCache<K, V> {

    protected Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * 本地缓存
     */
    protected LoadingCache<K, Optional<V>> cache;

    /**
     * 是否初始化
     */
    private boolean inited;

    /**
     * 默认最大数据个数,500
     */
    protected int cacheMaxSize = 1000;

    /**
     * 默认缓存有效时间,10
     */
    protected int expire = 10;

    /**
     * 默认缓存有效时间的单位,(分钟)
     */
    protected TimeUnit timeUnit = TimeUnit.MINUTES;

    /**
     * 初始缓存大小
     */
    protected int initialCapacity = 32;

    /**
     * 初始化缓存
     */
    protected void checkInit() {
        if (inited) {
            return;
        }
        synchronized (this) {
            if (inited) {
                return;
            }
            this.cache = Caffeine.newBuilder()
                    // 最多存放数据个数
                    .maximumSize(cacheMaxSize)
                    // 设置初始缓存大小
                    .initialCapacity(initialCapacity)
                    // 缓存有效时间
                    .expireAfterWrite(expire, timeUnit)
                    .removalListener((RemovalListener<K, Optional<V>>) (k, v, removalCause) -> onRemove(k, v))
                    // 开启:记录状态数据功能
                    .recordStats()
                    .build(key -> loadData(key));
            inited = true;
        }
    }

    /**
     * 当缓存不存在时，会调用此函数来加载数据源
     *
     * @param key key
     * @return value
     */
    private Optional<V> loadData(K key) {
        try {
            V value = loadSource(key);
            if(value == null) {
                return Optional.empty();
            }
            return Optional.of(value);
        } catch (Throwable ex) {
            log.warn("本地缓存加载数据", ex);
            throw ex;
        }
    }

    /**
     * 当缓存不存在时，会调用此函数来加载数据源
     *
     * @param key
     * @return
     */
    protected abstract V loadSource(K key);

    /**
     * 当缓存不存在时，会调用此函数来加载数据源
     *
     * @param key
     * @return
     */
    protected void onRemove(K key, Optional<V> value) {
        log.debug("缓存移除消息，键：{},值：{}", key, value);
    }

    /**
     * 优先取缓存的有效数据,然后从数据源取
     *
     * @param key
     * @return
     */
    public V getCacheThenSource(K key) {
        try {
            if (key == null) {
                throw new RuntimeException("取缓存数据key不能为空");
            }
            checkInit();

            Optional<V> value = LoopUtil.avoidThreadRecursionThrowable(() -> cache.get(key), this, key);
            return value.orElse(null);

        } catch (Throwable ex) {
            log.error("本地优先取缓存数据", ex);
            throw new RuntimeException(ex);
        }
    }

    /**
     * 优先取缓存的有效数据,然后从数据源异步加载
     *
     * @param key 键
     * @return 值
     */
    @Deprecated
    public V getCacheAsyncSource(K key) {
        try {
            checkInit();
            // 从现有的缓存中获取，如果缓存中有key，则返回value，如果没有则返回null
            Optional<V> value = cache.getIfPresent(key);
            if (value == null) {
                // 异步加载配置数据
                cache.refresh(key);
                return null;
            }
            return value.orElse(null);
        } catch (Exception ex) {
            throw new AppException(SystemErrorCodes.SYSTEMERROR, ex);
        }
    }

    /**
     * 设置缓存无效
     *
     * @param keys 缓存键
     */
    public void invalidate(List<K> keys) {
        try {
            checkInit();
            this.cache.invalidateAll(keys);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * 设置缓存无效
     */
    public void invalidateAll() {
        try {
            checkInit();
            this.cache.invalidateAll();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }
}
