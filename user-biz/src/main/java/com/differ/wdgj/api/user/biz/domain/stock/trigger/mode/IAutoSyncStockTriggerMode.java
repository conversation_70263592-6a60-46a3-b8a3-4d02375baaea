package com.differ.wdgj.api.user.biz.domain.stock.trigger.mode;

import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.AutoSyncStockTriggerMessage;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.ManualSyncStockTriggerMessage;

/**
 * 库存同步-业务处理模式接口
 *
 * <AUTHOR>
 * @date 2024-02-26 14:13
 */
public interface IAutoSyncStockTriggerMode {
    /**
     * 自动库存同步
     * @param triggerMessage 触发信息
     */
    void autoSyncStockTrigger(AutoSyncStockTriggerMessage triggerMessage);

    /**
     * 手动库存同步
     * @param triggerMessage 触发信息
     */
    void manualSyncStockTrigger(ManualSyncStockTriggerMessage triggerMessage);
}
