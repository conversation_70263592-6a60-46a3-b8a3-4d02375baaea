package com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch.data.enums;

import com.differ.wdgj.api.component.util.enums.CodeEnum;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import org.apache.commons.lang3.StringUtils;

/**
 * 仓库匹配业务类别枚举
 *
 * <AUTHOR>
 * @date 2024/12/19 下午7:35
 */
public enum WarehouseMatchBizTypeEnum implements CodeEnum {
    /**
     * 默认匹配（通常用于不返回仓库信息的委外订单）
     */
    DEFAULT_MATCH("-1", "默认匹配"),

    /**
     * 基础匹配
     */
    BASICS_MATCH("1", "基础匹配"),

    /**
     * 公共商家仓匹配
     */
    COMMON_MERCHANT_WHS_MATCH("2", "公共商家仓匹配"),

    /**
     * 公共门店仓匹配
     */
    COMMON_STORE_WHS_MATCH("6", "公共门店仓匹配"),

    /**
     * 公共门店匹配
     */
    COMMON_STORE_MATCH("7", "公共门店匹配"),

    /**
     * 库存同步商家仓匹配
     */
    STOCK_MERCHANT_WHS_MATCH("WAREHOUSE", "库存同步商家仓匹配"),

    /**
     * 库存同步门店仓匹配
     */
    STOCK_STORE_WHS_MATCH("STORE", "库存同步门店仓匹配"),

    ;

    /**
     * 名称
     */
    private String name;

    /**
     * 枚举值
     */
    private String code;

    //region 构造
    private WarehouseMatchBizTypeEnum(String code, String name) {
        this.name = name;
        this.code = code;
    }
    //endregion

    //region 公共方法
    @Override
    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
    //endregion

    //region 公共静态方法

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static WarehouseMatchBizTypeEnum create(String value) {
        // 兼容逻辑，底层若value为""直接返回null
        if(StringUtils.EMPTY.equals(value)){
            return WarehouseMatchBizTypeEnum.BASICS_MATCH;
        }

        return EnumConvertCacheUtil.convert(value, WarehouseMatchBizTypeEnum.class, EnumConvertType.CODE);
    }
    //endregion
}
