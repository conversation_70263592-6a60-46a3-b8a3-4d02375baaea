package com.differ.wdgj.api.user.biz.domain.apicall.utils;

import com.differ.jackyun.framework.component.utils.basic.enums.ErrorCodes;
import com.differ.wdgj.api.component.util.tools.ExtUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallGateway;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatResponseBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.exception.AppException;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.web.client.ResourceAccessException;

import java.util.function.Supplier;

/**
 * 外部请求日志工具类
 *
 * <AUTHOR>
 * @date 2024/8/16 下午1:46
 */
public class ApiCallLogUtils {
    //region 变量
    /**
     * 日志
     */
    private static final Logger log = LogFactory.get("外部接口请求");
    // endregion

    //region 构造
    private ApiCallLogUtils() {
    }
    //endregion

    /**
     * 记录请求跟踪日志
     *
     * @param context       上下文
     * @param caption       日志标题
     * @param funLogContent 自定义信息
     */
    public static void writeRequestInfoLog(ApiCallContext context, String caption, Supplier<StringBuilder> funLogContent) {
        writeRequestInfoLog(context.getApiType(), context.getPlat(), context.getMemberName(), caption, funLogContent);
    }

    /**
     * 记录请求跟踪日志
     *
     * @param apiType       接口类型
     * @param plat          平台值
     * @param memberName    会员名
     * @param caption       日志标题
     * @param funLogContent 自定义信息
     */
    public static void writeRequestInfoLog(PolyAPITypeEnum apiType, PolyPlatEnum plat, String memberName, String caption, Supplier<StringBuilder> funLogContent) {
        String logKey = String.format("%s-%s-%s", apiType, plat, memberName);
        LogFactory.info(caption, logKey, () ->
                ExtUtils.stringBuilderAppend(String.format("【请求外部接口调试日志-%s】[%s-%s-%s]", caption, apiType, plat, memberName), funLogContent.get().toString()));
    }

    /**
     * 记录请求异常日志
     *
     * @param context    上下文
     * @param gateway    网关
     * @param returnMsg  返回结果
     * @param logContent 自定义信息
     * @param e          异常信息
     */
    public static void writeRequestErrorLog(ApiCallContext context, ApiCallGateway gateway, String returnMsg, String logContent, Exception e) {
        // 上下文信息
        String apiType = context.getApiType().getOpenAPIMethod();
        String plat = context.getPlat().getName();
        String memberName = context.getMemberName();
        // 网关信息
        String gatewayName = StringUtils.EMPTY;
        String gatewayUrl = StringUtils.EMPTY;
        if (gateway != null) {
            gatewayName = gateway.getType().getName();
            gatewayUrl = gateway.getGateway();
        }
        String returnMsgSub = StringUtils.substring(returnMsg, 0, 2000);
        log.error("【请求外部接口异常日志】[{}-{}-{}]网关：{}-{}；返回结果：{}；自定义信息：{}；异常信息：", apiType, plat, memberName, gatewayName, gatewayUrl, returnMsgSub, logContent, e);
    }

    /**
     * 记录异常日志
     *
     * @param context    上下文
     * @param logContent 日志信息
     * @param e          异常信息
     */
    public static void writeErrorLog(ApiCallContext context, String logContent, Exception e) {
        // 上下文信息
        String apiType = context.getApiType().getOpenAPIMethod();
        String plat = context.getPlat().getName();
        String memberName = context.getMemberName();
        log.error("【请求外部接口异常日志】[{}-{}-{}]{}；异常信息：", apiType, plat, memberName, logContent, e);
    }

    /**
     * 处理异常
     *
     * @param response 响应
     * @param ex       异常
     */
    public static void processException(ApiCallResponse<? extends BasePlatResponseBizData> response, Exception ex) {
        response.setSuccess(false);
        response.setCode(ApiCallConstantUtils.POLY_FAIL_CODE);

        // IO异常
        if (ex instanceof java.io.IOException) {
            response.setSubCode(ErrorCodes.POLYAPINETWORK.getValue().toString());
            response.setMsg("POLYAPINETWORK");
            response.setSubMessage("请求菠萝派网络异常");
        }
        // IO异常(RestTemplate 661)
        if (ex instanceof ResourceAccessException) {
            Throwable cause = ex.getCause();
            if (cause instanceof java.io.IOException) {
                response.setSubCode(ErrorCodes.POLYAPINETWORK.getValue().toString());
                response.setMsg("POLYAPINETWORK");
                response.setSubMessage("请求菠萝派网络异常");
            }
        }
        // 自定义异常
        else if (ex instanceof AppException) {
            response.setSubCode(ErrorCodes.LOGICERROR.getValue().toString());
            response.setMsg("LOGICERROR");
            response.setSubMessage(ex.getMessage());
        }
        // 系统异常
        else {
            response.setSubCode(ErrorCodes.SYSTEMERROR.getValue().toString());
            response.setMsg("SYSTEMERROR");
            response.setSubMessage("系统异常");
        }
    }
}
