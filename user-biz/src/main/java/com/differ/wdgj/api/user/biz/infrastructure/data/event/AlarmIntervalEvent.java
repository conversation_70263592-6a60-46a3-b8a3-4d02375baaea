package com.differ.wdgj.api.user.biz.infrastructure.data.event;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AlarmIntervalTypeEnum;
import org.springframework.context.ApplicationEvent;

/**
 * @Description 间隔报警事件
 * <AUTHOR>
 * @Date 2021/11/23 21:47
 */
public class AlarmIntervalEvent extends ApplicationEvent {

    /**
     * 报警间隔类型
     */
    private AlarmIntervalTypeEnum alarmIntervalType;
    /**
     * 报警去重建
     */
    private String alarmDuplicateKey;
    /**
     * 报警内容
     */
    private String alarmText;

    public AlarmIntervalEvent(AlarmIntervalTypeEnum alarmIntervalType, String alarmDuplicateKey, String alarmText) {
        super(alarmIntervalType);
        this.alarmIntervalType = alarmIntervalType;
        this.alarmDuplicateKey = alarmDuplicateKey;
        this.alarmText = alarmText;
    }

    public AlarmIntervalTypeEnum getAlarmIntervalType() {
        return alarmIntervalType;
    }

    public String getAlarmDuplicateKey() {
        return alarmDuplicateKey;
    }

    public void setAlarmText(String alarmText) {
        this.alarmText = alarmText;
    }

    public String getAlarmText() {
        return alarmText;
    }
}