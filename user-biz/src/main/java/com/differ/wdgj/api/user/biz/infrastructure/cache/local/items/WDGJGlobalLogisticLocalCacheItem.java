//package com.differ.wdgj.api.user.biz.infrastructure.cache.local.items;
//
//import com.differ.newapi.base.infrastructure.component.cache.ICacheDataItem;
//
///**
// * 全球物流信息缓存实体
// * @author: xgy
// * @date: 2021-02-05 11:12
// */
//public class WDGJGlobalLogisticLocalCacheItem implements ICacheDataItem {
//    @Override
//    public String getDataVersion() {
//        return null;
//    }
//
//    @Override
//    public String getHashKey() {
//        return null;
//    }
//
//    /**
//     * 全局标准物流名称
//     */
//    private String globalLogisticName;
//
//    /**
//     * 全局标准物流代码
//     */
//    private String logisticCode;
//
//    /**
//     * 合作物流名称（SndStyle2）
//     */
//    private String cfgLogisticName;
//
//    public String getGlobalLogisticName() {
//        return globalLogisticName;
//    }
//
//    public void setGlobalLogisticName(String globalLogisticName) {
//        this.globalLogisticName = globalLogisticName;
//    }
//
//    public String getLogisticCode() {
//        return logisticCode;
//    }
//
//    public void setLogisticCode(String logisticCode) {
//        this.logisticCode = logisticCode;
//    }
//
//    public String getCfgLogisticName() {
//        return cfgLogisticName;
//    }
//
//    public void setCfgLogisticName(String cfgLogisticName) {
//        this.cfgLogisticName = cfgLogisticName;
//    }
//}
