package com.differ.wdgj.api.user.biz.domain.stock.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * erp货品库存变动原因
 *
 * <AUTHOR>
 * @date 2025/3/13 下午2:41
 */
public enum ErpStockNoticeChangeEnum implements ValueEnum {
    /**
     * 入库
     */
    WAREHOUSING("入库", 1, 1000),

    /**
     * 其他
     */
    OTHER("其他", 2, 2000);

    /**
     * 构造
     *
     * @param name      变动原因名
     * @param apiValue  API值
     * @param wdgjValue 网店管家值
     */
    ErpStockNoticeChangeEnum(String name, Integer apiValue, Integer wdgjValue) {
        this.name = name;
        this.apiValue = apiValue;
        this.wdgjValue = wdgjValue;
    }

    /**
     * 匹配类型名
     */
    private String name;

    /**
     * 匹配类型值
     */
    private Integer apiValue;

    /**
     * 网店管家值
     */
    private Integer wdgjValue;

    @Override
    public Integer getValue() {
        return wdgjValue;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static SyncStockFlagEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, SyncStockFlagEnum.class, EnumConvertType.VALUE);
    }
}
