package com.differ.wdgj.api.user.biz.domain.aftersale.load.data.out;

/**
 * 下载结果
 *
 * <AUTHOR>
 * @date 2024/9/13 下午4:33
 */
public class LoadResult {
    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String message;


    //region 公共方法
    /**
     * 成功结果
     *
     * @return 结果
     */
    public static LoadResult successResult() {
        LoadResult result = new LoadResult();
        result.setSuccess(true);
        return result;
    }

    /**
     * 失败结果
     *
     * @param message 错误信息
     * @return 结果
     */
    public static LoadResult failedResult(String message) {
        LoadResult result = new LoadResult();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }
    //endregion

    //region get/set
    public boolean isSuccess() {
        return success;
    }

    public boolean isFailed() {
        return !success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
    //endregion
}
