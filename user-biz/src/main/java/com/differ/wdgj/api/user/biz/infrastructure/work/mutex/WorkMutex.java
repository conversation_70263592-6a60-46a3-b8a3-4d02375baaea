package com.differ.wdgj.api.user.biz.infrastructure.work.mutex;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.CreateResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;

/**
 * 工作任务互斥接口
 *
 * <AUTHOR>
 * @date 2024/7/10 9:56
 */
public interface WorkMutex {
    /**
     * 创建任务，当已存在互斥的任务未完成时，返回false
     *
     * @param workData    工作数据
     * @param dataOperate 数据操作工具
     * @return 创建结果
     */
    CreateResult createIfNoExists(WorkData<?> workData, WorkDataOperate dataOperate);
}
