package com.differ.wdgj.api.user.biz.domain.aftersale.load.work;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadSubTask;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.LoadAfterSaleWorkResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.adapter.AfterSaleLoadTaskContextAdapter;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.mutex.LoadAfterSaleWorkMutex;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor.BaseLoadAfterSaleProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor.plat.AliExpressLoadAfterSaleProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor.plat.JDLoadAfterSaleProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor.plat.TaobaoLoadAfterSaleProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.work.business.page.PageWorkBusinessProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.factory.AbstractPageWorkFactory;
import com.differ.wdgj.api.user.biz.infrastructure.work.mutex.WorkMutex;

/**
 * 售后单下载执行下载工作任务的工厂
 *
 * <AUTHOR>
 * @date 2024/9/11 下午2:49
 */
public class LoadAfterSaleWorkFactory extends AbstractPageWorkFactory<WorkData<AfterSaleLoadArgs>, AfterSaleLoadSubTask, LoadAfterSaleWorkResult> {
    //region 构造
    public LoadAfterSaleWorkFactory() {
        super(WorkEnum.LOAD_AFTER_SALE);
    }
    //endregion

    /**
     * 获取业务处理对象
     *
     * @param workData 任务信息
     * @return 业务处理对象
     */
    @Override
    public PageWorkBusinessProcessor<WorkData<AfterSaleLoadArgs>, AfterSaleLoadSubTask, LoadAfterSaleWorkResult> createBusinessProcessor(WorkData<AfterSaleLoadArgs> workData) {
        // 1、构建上下文
        AfterSaleLoadTaskContext context = new AfterSaleLoadTaskContextAdapter().toLoadTaskContext(workData);

        // 2、返回业务处理对象
        switch (context.getApiShopBaseInfo().getPlat()){
            case BUSINESS_AliExpress:
                return new AliExpressLoadAfterSaleProcessor(context);
            case BUSINESS_JD:
                return new JDLoadAfterSaleProcessor(context);
            case BUSINESS_Taobao:
                return new TaobaoLoadAfterSaleProcessor(context);
            default:
                return new BaseLoadAfterSaleProcessor(context);
        }
    }

    /**
     * 工作任务互斥对象
     *
     * @return 工作任务互斥对象
     */
    @Override
    public WorkMutex createWorkMutex() {
        return new LoadAfterSaleWorkMutex();
    }

    /**
     * 获取工作任务的参数类型
     * com.differ.jackyun.omsapi.user.biz.infrastructure.work.data.WorkData<T extends WorkArgs>
     *
     * @return T的类型
     */
    @Override
    public Class<?> getWorkDataArgClass() {
        return AfterSaleLoadArgs.class;
    }
}
