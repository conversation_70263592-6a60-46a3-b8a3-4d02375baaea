package com.differ.wdgj.api.user.biz.domain.goods.match.data;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;

import java.util.Objects;

/**
 * 商品匹配操作扩展类
 *
 * <AUTHOR>
 * @date 2024-03-11 15:08
 */
public class ApiSysMatchOperationEnhance {

    /**
     * 匹配信息
     */
    private ApiSysMatchDO apiSysMatch;

    /**
     * 操作原因
     */
    private String operationReason;

    /**
     * 构建 商品匹配操作扩展
     * @param apiSysMatch 商品匹配
     * @param operationReason 操作原因
     * @return 商品匹配操作扩展
     */
    public static ApiSysMatchOperationEnhance create(ApiSysMatchDO apiSysMatch, String operationReason){
        ApiSysMatchOperationEnhance apiSysMatchOperation = new ApiSysMatchOperationEnhance();
        apiSysMatchOperation.setApiSysMatch(apiSysMatch);
        apiSysMatchOperation.setOperationReason(operationReason);
        return apiSysMatchOperation;
    }

    //region get/set
    public ApiSysMatchDO getApiSysMatch() {
        return apiSysMatch;
    }

    public void setApiSysMatch(ApiSysMatchDO apiSysMatch) {
        this.apiSysMatch = apiSysMatch;
    }

    public String getOperationReason() {
        return operationReason;
    }

    public void setOperationReason(String operationReason) {
        this.operationReason = operationReason;
    }
    //endregion

    //region 重写基类方法
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiSysMatchOperationEnhance that = (ApiSysMatchOperationEnhance) o;
        return apiSysMatch.getId() == that.apiSysMatch.getId();
    }

    @Override
    public int hashCode() {
        return Objects.hash(apiSysMatch.getId());
    }
    //endregion
}
