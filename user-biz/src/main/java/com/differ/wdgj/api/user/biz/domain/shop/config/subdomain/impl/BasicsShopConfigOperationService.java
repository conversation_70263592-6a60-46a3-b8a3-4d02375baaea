package com.differ.wdgj.api.user.biz.domain.shop.config.subdomain.impl;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.shop.config.subdomain.IBasicsShopConfigOperationService;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.CfgShopListExtMapper;

/**
 * 店铺配置基础操作实现
 *
 * <AUTHOR>
 * @date 2025/2/24 上午10:14
 */
public class BasicsShopConfigOperationService implements IBasicsShopConfigOperationService {
    //region 更新
    /**
     * 更新店铺配置
     *
     * @param memberName      会员名
     * @param shopId      erp店铺id
     * @param bizType     业务类型
     * @param configValue 配置值
     * @return 结果
     */
    @Override
    public boolean updateShopConfig(String memberName, int shopId, ApiShopConfigBizTypes bizType, String configValue) {
        CfgShopListExtMapper shopListExtMapper = BeanContextUtil.getBean(CfgShopListExtMapper.class);
        DBSwitchUtil.doDBWithUser(memberName,
                () ->{
                    switch (bizType){
                        case DOWNLOAD_ORDER:
                            shopListExtMapper.updateDownloadOrderShopConfig(shopId, configValue);
                            break;
                        case AFTER_SALES:
                            shopListExtMapper.updateAfterSaleShopConfig(shopId, configValue);
                            break;
                        case SYNC_STOCK:
                            shopListExtMapper.updateSyncStockShopConfig(shopId, configValue);
                            break;
                        default:
                    }
                });
        return true;
    }

    /**
     * 更新api店铺Id
     *
     * @param shopId    erp店铺id
     * @param apiShopId api店铺Id
     * @return 结果
     */
    @Override
    public boolean updateApiShopId(String memberName, int shopId, int apiShopId) {
        CfgShopListExtMapper shopListExtMapper = BeanContextUtil.getBean(CfgShopListExtMapper.class);
        DBSwitchUtil.doDBWithUser(memberName,
                () ->{
                    shopListExtMapper.updateApShopIdByShopId(shopId, apiShopId);
                });
        return true;
    }
    //endregion
}
