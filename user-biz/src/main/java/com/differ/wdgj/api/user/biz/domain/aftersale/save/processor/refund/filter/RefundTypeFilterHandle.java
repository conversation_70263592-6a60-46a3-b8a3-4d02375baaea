package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.filter;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleSaveBizType;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPreFiltrationOrderHandle;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.AfterSalesShopConfigUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 退货退款单类别过滤器
 *
 * <AUTHOR>
 * @date 2024-06-07 15:10
 */
public class RefundTypeFilterHandle extends AbstractPreFiltrationOrderHandle<BusinessGetRefundOrderResponseOrderItem> {
    //region 构造
    public RefundTypeFilterHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法

    /**
     * 前置过滤
     *
     * @param orderItem 原始售后单列表
     * @return 过滤结果
     */
    @Override
    public AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> orderItem, TargetCovertOrderItem targetOrder) {
        // 基础数据
        AfterSaleSaveBizType bizType = orderItem.getBizType();
        String shopTypeCode = bizType.getShopType().getCode();

        // 获取售后单业务类型
        ApiAfterSaleTypeEnum apiAfterSaleOrderType = bizType.getApiAfterSaleOrderType();
        if (apiAfterSaleOrderType == null) {
            return AfterSaleHandleResult.failed("API不支持的售后类型");
        }

        // 推送平台不校验店铺业务配置
        if (context.getAfterSalesConfigPlatFeature(shopTypeCode).isMessageNotificationNoBizCheck()) {
            return AfterSaleHandleResult.success();
        }

        // 售后店铺配置获取
        AfterSalesShopConfig shopAfterSalesConfig = context.getAfterSalesShopConfig();
        if (shopAfterSalesConfig == null || CollectionUtils.isEmpty(shopAfterSalesConfig.getSaveTypes())) {
            return AfterSaleHandleResult.failed("店铺售后单未配置");
        }

        // 订单 or 店铺类型验证
        List<AfterSalesShopConfig.TypeItem> bizShopOrderTypes = shopAfterSalesConfig.getSaveTypes().stream().filter(x -> x.getBizType().equals(shopTypeCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bizShopOrderTypes)) {
            return AfterSaleHandleResult.failed(String.format("订单类型【%s】不符合店铺配置", shopTypeCode));
        }
        // 售后单类型验证
        if (!AfterSalesShopConfigUtils.checkShopTypeConfig(apiAfterSaleOrderType, bizShopOrderTypes)) {
            return AfterSaleHandleResult.failed(String.format("订单类型【%s】售后店铺配置未勾选售后单类型：【%s】", shopTypeCode, apiAfterSaleOrderType.getDescription()));
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "退货退款单类别过滤器";
    }
    //endregion
}
