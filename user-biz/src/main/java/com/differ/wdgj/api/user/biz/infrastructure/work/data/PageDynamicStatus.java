package com.differ.wdgj.api.user.biz.infrastructure.work.data;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.RunStatusEnum;

import java.time.LocalDateTime;

/**
 * 任务执行过程中需要的动态参数(增量模式时是增量参数)
 *
 * <AUTHOR>
 * @date 2024/7/4 13:35
 */
public class PageDynamicStatus extends DynamicStatus {

    /**
     * 时间类型
     */
    private String timeType;

    /**
     * 开始下载订单的时间。
     */
    private LocalDateTime loadStartTime;

    /**
     * 结束下载订单的时间。
     */
    private LocalDateTime loadEndTime;

    /**
     * 最后成功的数据时间
     */
    private LocalDateTime lastSuccessDataTime;

    /**
     * 总页数
     * <p>
     * 当前拆分子任务的总页数,如果下次需要拆分任务，值会被重置
     * </p>
     */
    private int totalPages;

    /**
     * 页码,基于1。
     */
    private int pageIndex;

    /**
     * 页码
     */
    private int pageSize;

    /**
     * 执行状态
     */
    private RunStatusEnum runStatus;

    /**
     * 下一页模式的token
     */
    private String nextToken;

    /**
     * 当前子任务下载数据大小
     */
    private int dataSize;

    public LocalDateTime getLoadStartTime() {
        return loadStartTime;
    }

    public void setLoadStartTime(LocalDateTime loadStartTime) {
        this.loadStartTime = loadStartTime;
    }

    public LocalDateTime getLoadEndTime() {
        return loadEndTime;
    }

    public void setLoadEndTime(LocalDateTime loadEndTime) {
        this.loadEndTime = loadEndTime;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    public RunStatusEnum getRunStatus() {
        return runStatus;
    }

    public void setRunStatus(RunStatusEnum runStatus) {
        this.runStatus = runStatus;
    }

    public String getNextToken() {
        return nextToken;
    }

    public void setNextToken(String nextToken) {
        this.nextToken = nextToken;
    }

    public String getTimeType() {
        return timeType;
    }

    public void setTimeType(String timeType) {
        this.timeType = timeType;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public LocalDateTime getLastSuccessDataTime() {
        return lastSuccessDataTime;
    }

    public void setLastSuccessDataTime(LocalDateTime lastSuccessDataTime) {
        this.lastSuccessDataTime = lastSuccessDataTime;
    }

    public int getDataSize() {
        return dataSize;
    }

    public void setDataSize(int dataSize) {
        this.dataSize = dataSize;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
