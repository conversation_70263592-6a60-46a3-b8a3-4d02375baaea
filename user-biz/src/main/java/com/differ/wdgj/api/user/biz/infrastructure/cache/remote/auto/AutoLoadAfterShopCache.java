package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.auto;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.tools.ExtUtils;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop.ApiAllShopLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.AbstractHashEnhanceCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.DataCacheKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.LoadAfterSalesConfigContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.ApiAllShopDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.AutoLoadAfterSaleAllShopDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.AutoLoadAfterSaleShopDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.common.SimpleResult;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.load.OrderAutoModeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopAuthStatusEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.plat.LoadAfterSaleBizFeatureUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.AfterSalesShopConfigUtils;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.ShopInfoUtils;
import com.mchange.lang.IntegerUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 自动下载售后单店铺-Redis缓存
 *
 * <AUTHOR>
 * @date 2024/11/27 下午3:50
 */
public class AutoLoadAfterShopCache extends AbstractHashEnhanceCache<AutoLoadAfterSaleAllShopDto> {
    //region 变量
    /**
     * 标题
     */
    private final String caption = "自动下载售后单店铺内存缓存";
    // endregion

    //region 构造

    /**
     * 构造方法
     */
    public AutoLoadAfterShopCache() {
        super(DataCacheKeyEnum.USER_API_AUTO_LOAD_AFTER_SALE.getCode());
        // 过期时间 半 ~ 1 小时随机
        this.minCacheKeyTimeOut = 3600;
        this.maxCacheKeyTimeOut = 7200;
    }
    //endregion

    //region 单例

    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static AutoLoadAfterShopCache singleton() {
        return AutoLoadAfterShopCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final AutoLoadAfterShopCache instance;

        private SingletonEnum() {
            instance = new AutoLoadAfterShopCache();
        }
    }
    //endregion

    //region 公共方法
    /**
     * 移除缓存-单个
     *
     * @param memberName  会员名
     */
    public void clearCache(String memberName) {
        // 校验入参
        if (StringUtils.isEmpty(memberName)) {
            return;
        }

        // 移除缓存
        super.removeCache(cacheKey, memberName);
    }
    //endregion


    //region 实现基类逻辑
    @Override
    protected AutoLoadAfterSaleAllShopDto loadSource(String memberName) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 店铺基础信息
        List<AutoLoadAfterSaleShopDto> autoList = new ArrayList<>();
        List<Pair<Integer, String>> errorShopList = new ArrayList<>();
        ApiAllShopDto apiAllShopDto = ApiAllShopLocalCache.singleton().getCacheThenSource(memberName);
        if (apiAllShopDto != null) {
            List<ApiAllShopDto.ShopInfo> shopInfoList = apiAllShopDto.getShopInfoList();
            for (ApiAllShopDto.ShopInfo shopInfo : shopInfoList) {
                String errorMessage = StringUtils.EMPTY;
                try {
                    // 新字段，空数据兼容
                    if (shopInfo.getApiShopId() == 0) {
                        errorMessage = "店铺基础数据getApiShopId为空";
                        continue;
                    }

                    ApiShopBaseDto shopBase = ShopInfoUtils.singleByShopId(memberName, shopInfo.getApiShopId());
                    // 店铺基础信息过滤
                    SimpleResult shopResult = isShopAutoLoad(shopBase);
                    if (shopResult.isFailed()) {
                        errorMessage = shopResult.getMessage();
                        continue;
                    }

                    // 平台业务特性过滤
                    LoadAfterSalesConfigContent feature = LoadAfterSaleBizFeatureUtils.getFeature(shopBase, StringUtils.EMPTY);
                    SimpleResult platFeatureResult = isPlatFeatureAutoLoad(feature);
                    if (platFeatureResult.isFailed()) {
                        errorMessage = platFeatureResult.getMessage();
                        continue;
                    }

                    // 店铺配置过滤
                    AfterSalesShopConfig shopConfig = AfterSalesShopConfigUtils.singleByShopId(memberName, shopInfo.getShopId());
                    SimpleResult shopConfigResult = isShopConfigAutoLoad(shopConfig);
                    if (shopConfigResult.isFailed()) {
                        errorMessage = shopConfigResult.getMessage();
                        continue;
                    }

                    // 构建店铺级结果
                    AutoLoadAfterSaleShopDto autoLoadAfterSaleShopDto = new AutoLoadAfterSaleShopDto();
                    autoLoadAfterSaleShopDto.setShopId(IntegerUtils.parseInt(shopBase.getOutShopId(), 0));
                    autoLoadAfterSaleShopDto.setPlat(shopBase.getPlat());
                    autoLoadAfterSaleShopDto.setInterval(feature.getTaskInterval());
                    autoList.add(autoLoadAfterSaleShopDto);
                } catch (Exception e) {
                    errorMessage = String.format("【%s-%s】验证是否支持售后单自动下载异常", memberName, shopInfo.getShopId());
                } finally {
                    if (StringUtils.isNotEmpty(errorMessage)) {
                        errorShopList.add(Pair.of(shopInfo.getShopId(), errorMessage));
                    }
                }
            }
        }

        stopWatch.stop();
        // 调试日志
        LogFactory.info(caption, memberName, () -> ExtUtils.stringBuilderAppend(String.format("自动下载售后单店铺 - 耗时：%s；自动下载店铺数据：%s；不自动下载店铺数据：%s；", stopWatch.getTotalTimeMillis(), JsonUtils.toJson(autoList), JsonUtils.toJson(errorShopList))));

        // 存在值则返回
        if (CollectionUtils.isNotEmpty(autoList)) {
            return new AutoLoadAfterSaleAllShopDto(autoList);
        }
        return null;
    }

    @Override
    protected Map<String, AutoLoadAfterSaleAllShopDto> loadSource(List<String> hashFields) {
        return Collections.emptyMap();
    }

    @Override
    public Class<AutoLoadAfterSaleAllShopDto> getValueClazz() {
        return AutoLoadAfterSaleAllShopDto.class;
    }
    //endregion

    //region 私有方法

    /**
     * 店铺是否有效
     *
     * @param shop         店铺基础信息
     * @return 结果
     */
    private SimpleResult isShopAutoLoad(ApiShopBaseDto shop) {
        if (shop == null) {
            return SimpleResult.failed("店铺基础数据为空");
        }
        // 店铺停用或删除
        if (!shop.isActived() || shop.isDelete()) {
            return SimpleResult.failed("店铺停用或删除");
        }
        // 店铺未授权
        if (shop.getAuthStatus() != ShopAuthStatusEnum.AUTHORIZED) {
            return SimpleResult.failed("店铺未授权");
        }
        return SimpleResult.success();
    }

    /**
     * 平台是否支持自动下载
     *
     * @param feature      平台业务特性
     * @return 结果
     */
    private SimpleResult isPlatFeatureAutoLoad(LoadAfterSalesConfigContent feature) {
        // 平台业务特性不存在
        if (feature == null) {
            return SimpleResult.failed("平台业务特性不存在");
        }
        // 平台不支持下载订单
        if (feature.getAutoMode() != OrderAutoModeEnum.CUSTOMIZATION_AUTO_LOAD && feature.getAutoMode() != OrderAutoModeEnum.PUSH_AND_LOAD) {
            return SimpleResult.failed("平台不支持下载订单");
        }
        return SimpleResult.success();
    }

    /**
     * 店铺配置是否支持自动下载
     *
     * @param shopConfig   店铺配置
     * @return 结果
     */
    private SimpleResult isShopConfigAutoLoad(AfterSalesShopConfig shopConfig) {
        // 店铺配置不存在
        if (shopConfig == null) {
            return SimpleResult.failed("售后店铺配置不存在");
        }
        // 店铺未开启自动下载
        if (!shopConfig.isEnableDownload()) {
            return SimpleResult.failed("店铺未开启自动下载");
        }
        // 未勾选下载类型
        if (CollectionUtils.isEmpty(shopConfig.getSaveTypes())) {
            return SimpleResult.failed("未勾选下载类型");
        }
        return SimpleResult.success();
    }
    //endregion
}
