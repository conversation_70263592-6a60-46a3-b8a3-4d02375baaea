package com.differ.wdgj.api.user.biz.domain.stock.data.enums;

import com.differ.wdgj.api.component.util.enums.CodeEnum;

/**
 * 库存同步处理
 *
 * <AUTHOR>
 * @date 2024-02-23 13:40
 */
public enum SyncStockModeEnum implements CodeEnum {

    /**
     * 全量
     */
    Full("JH_01", "全量"),
    /**
     * 增量
     */
    Increment("JH_02", "增量");


    SyncStockModeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }


    private final String value;

    private final String description;

    @Override
    public String getCode() {
        return value;
    }
}
