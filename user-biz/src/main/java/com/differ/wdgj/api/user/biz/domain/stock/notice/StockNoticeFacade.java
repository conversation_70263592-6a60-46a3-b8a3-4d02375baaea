package com.differ.wdgj.api.user.biz.domain.stock.notice;

import com.differ.wdgj.api.component.util.tools.ExtUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockNoticeConvertTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.notice.ApiStockNoticeSaveResult;
import com.differ.wdgj.api.user.biz.domain.stock.notice.erp.processor.IPlatStockNoticeProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.notice.erp.processor.plugins.MultiWarePlatStockNoticeProcessor;
import com.differ.wdgj.api.user.biz.domain.stock.notice.erp.processor.plugins.NormalPlatStockNoticeProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;

/**
 * 库存同步-变动通知处理 门面类
 *
 * <AUTHOR>
 * @date 2024-02-26 16:44
 */
public class StockNoticeFacade {
    /**
     * erp货品库存变动通知 -> api匹配库存变动通知
     *
     * @param memberName 会员名
     */
    public StockContentResult<ApiStockNoticeSaveResult> convertErpToPlatStockNotice(String memberName, StockNoticeConvertTypeEnum convertType) {
        // todo 会员状态校验

        //获取转换器
        IPlatStockNoticeProcessor converter = getConverter(memberName, convertType);
        // 执行转换
        StockContentResult<ApiStockNoticeSaveResult> result = converter.convertPlatStockNotice();

        // 调试日志记录
        LogFactory.info("库存同步变动通知处理", memberName, () -> ExtUtils.stringBuilderAppend(String.format("[%]发起[%s]转换，转换数量[%s]", memberName, convertType.getName(), covertDebuggingResult(result))));

        return result;
    }

    //region 私有方法
    /**
     * 获取转换器
     *
     * @param memberName  会员名
     * @param convertType 转换类型
     * @return 结果
     */
    private IPlatStockNoticeProcessor getConverter(String memberName, StockNoticeConvertTypeEnum convertType) {
        switch (convertType) {
            case MULTI_WARE:
                return new MultiWarePlatStockNoticeProcessor(memberName);
            case NORMAL:
            default:
                return new NormalPlatStockNoticeProcessor(memberName);
        }
    }

    /**
     * 处理结果
     *
     * @param result 结果
     * @return 结果调试记录信息
     */
    private String covertDebuggingResult(StockContentResult<ApiStockNoticeSaveResult> result) {
        if (result == null) {
            return "返回结果为空";
        }

        if (result.getSuccess()) {
            if (result.getContent() == null) {
                return "转换成功，获取转换数量失败";
            }
            return String.format("转换成功，转换数量：%s", result.getContent().getApiStockNoticeCount());
        } else {
            return String.format("转换失败，失败原因:%s", result.getMessage());
        }
    }
    //endregion
}
