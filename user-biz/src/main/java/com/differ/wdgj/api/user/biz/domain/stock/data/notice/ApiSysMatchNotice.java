package com.differ.wdgj.api.user.biz.domain.stock.data.notice;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.ErpStockNoticeChangeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;

/**
 * erp商品变动 - 商品匹配信息扩展
 *
 * <AUTHOR>
 * @date 2024/12/30 下午3:36
 */
public class ApiSysMatchNotice {
    /**
     * 平台商品匹配信息
     */
    private ApiSysMatchDO platGoodsMatch;

    /**
     * 店铺Id
     */
    private int shopId;

    /**
     * ERP变动仓库Id
     */
    private String warehouseId;

    /**
     * ERP变动原因
     *
     * @see ErpStockNoticeChangeEnum
     */
    private String changeSource;

    //region get/set
    public ApiSysMatchDO getPlatGoodsMatch() {
        return platGoodsMatch;
    }

    public void setPlatGoodsMatch(ApiSysMatchDO platGoodsMatch) {
        this.platGoodsMatch = platGoodsMatch;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getChangeSource() {
        return changeSource;
    }

    public void setChangeSource(String changeSource) {
        this.changeSource = changeSource;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }
    //endregion
}
