package com.differ.wdgj.api.user.biz.tasks.job.queue.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.MemberAble;

/**
 * 用户级别负载均衡任务数据
 *
 * <AUTHOR>
 * @date 2023-12-29 10:07
 */
public class UserQueueJobData extends AbstractQueueJobData implements MemberAble {

    /**
     * 会员名
     */
    private String memberName;

    // region 接口实现

    /**
     * 唯一键
     *
     * @return 唯一键
     */
    @Override
    public String uniqueSign() {
        return this.memberName;
    }

    /**
     * 任务唯一键ID
     *
     * @return
     */
    @Override
    public String taskUniqueId() {
        return this.memberName;
    }

    /**
     * 取会员名
     *
     * @return
     */
    @JSONField(serialize = false)
    @Override
    public String getMember() {
        return this.memberName;
    }

    // endregion

    // region 公共方法

    /**
     * 获取会员名
     *
     * @return 结果
     */
    public String getMemberName() {
        return this.memberName;
    }

    /**
     * 设置会员名
     *
     * @param memberName 会员名
     */
    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    // endregion
}
