package com.differ.wdgj.api.user.biz.infrastructure.work.data;


import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.RunStatusEnum;

import java.time.LocalDateTime;

/**
 * 子任务执行结果
 *
 * <AUTHOR>
 * @date 2024/7/5 13:24
 */
public class RunPageResult {

    /**
     * 子任务执行状态
     */
    private RunStatusEnum runStatus;

    /**
     * 子任务执行结果
     */
    private String errorMsg;

    /**
     * 工作任务结果
     */
    private WorkResult workResult;

    //region 分页参数
    /**
     * 是否有下一页
     */
    private boolean hasNextPage;

    /**
     * 下一页的token
     */
    private String nextToken;

    /**
     * 当前页下载数据大小
     */
    private int dataSize;

    /**
     * 总页数
     * <p>
     * 当前拆分子任务的总页数,如果下次需要拆分任务，值会被重置
     * </p>
     */
    private int totalPages;

    /**
     * 业务限制最大页数
     */
    private int maxTotalPages;

    /**
     * 最后成功的数据时间
     */
    private LocalDateTime lastSuccessDataTime;

    //endregion

    //region 构造
    private RunPageResult(RunStatusEnum runStatus, String errorMsg) {
        this.runStatus = runStatus;
        this.errorMsg = errorMsg;
    }
    //endregion

    //region 公共方法
    public static RunPageResult success() {
        return new RunPageResult(RunStatusEnum.SUCCESS, null);
    }

    public static RunPageResult failure(String errorMsg) {
        return new RunPageResult(RunStatusEnum.FAILED, errorMsg);
    }

    public static RunPageResult running() {
        return new RunPageResult(RunStatusEnum.RUNNING, null);
    }

    public RunPageResult onSuccess(WorkResult workResult) {
        this.runStatus = RunStatusEnum.SUCCESS;
        this.workResult = workResult;
        return this;
    }

    public RunPageResult onFail(WorkResult workResult, String errorMsg) {
        this.runStatus = RunStatusEnum.FAILED;
        this.errorMsg = errorMsg;
        this.workResult = workResult;
        return this;

    }
    //endregion

    //region get/set
    public RunStatusEnum getRunStatus() {
        return runStatus;
    }

    public void setRunStatus(RunStatusEnum runStatus) {
        this.runStatus = runStatus;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public boolean isHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(boolean hasNextPage) {
        this.hasNextPage = hasNextPage;
    }

    public String getNextToken() {
        return nextToken;
    }

    public void setNextToken(String nextToken) {
        this.nextToken = nextToken;
    }

    public WorkResult getWorkResult() {
        return workResult;
    }

    public void setWorkResult(WorkResult workResult) {
        this.workResult = workResult;
    }

    public int getDataSize() {
        return dataSize;
    }

    public void setDataSize(int dataSize) {
        this.dataSize = dataSize;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public int getMaxTotalPages() {
        return maxTotalPages;
    }

    public void setMaxTotalPages(int maxTotalPages) {
        this.maxTotalPages = maxTotalPages;
    }

    public LocalDateTime getLastSuccessDataTime() {
        return lastSuccessDataTime;
    }

    public void setLastSuccessDataTime(LocalDateTime lastSuccessDataTime) {
        this.lastSuccessDataTime = lastSuccessDataTime;
    }

    //endregion
}
