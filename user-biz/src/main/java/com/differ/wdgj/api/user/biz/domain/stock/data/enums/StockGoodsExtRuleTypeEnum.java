package com.differ.wdgj.api.user.biz.domain.stock.data.enums;

/**
 * 库存同步额外规则类别
 *
 * <AUTHOR>
 * @date 2025/6/16 13:46
 */
public enum StockGoodsExtRuleTypeEnum {
   /**
     * 固定同步数量
     */
    FIX("fixQuantity", "固定同步数量", "同步固定数量%s"),
    /**
     * 条件固定同步数量
     */
    CONDITION_FIX("conditionFixQuantity", "条件固定同步数量", "同步固定数量%s，且介于%s~%s"),
    /**
     * 条件增加数量
     */
    CONDITION_FIX_INCREASE("conditionFixIncreaseQuantity", "条件库存区间内固定增加数量", "同步增加数量%s，且介于%s~%s"),
    ;
    //region 字段
    /**
     * 编码
     */
    private final String code;
    /**
     * 描述
     */
    private final String explain;
    /**
     * 日志格式
     */
    private final String logFormatter;
    //endregion

    // region 构造器

    StockGoodsExtRuleTypeEnum(String code, String explain, String logFormatter) {
        this.code = code;
        this.explain = explain;
        this.logFormatter = logFormatter;
    }

    // endregion

    // region getter

    public String getCode() {
        return code;
    }

    public String getExplain() {
        return explain;
    }

    public String getLogFormatter() {
        return logFormatter;
    }

    // endregion
}
