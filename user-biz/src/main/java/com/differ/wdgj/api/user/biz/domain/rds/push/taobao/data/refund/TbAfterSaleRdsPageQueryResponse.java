package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;

import java.util.List;

/**
 * 淘宝售后单Rds分页查询结果
 *
 * <AUTHOR>
 * @date 2025/4/9 下午2:23
 */
public class TbAfterSaleRdsPageQueryResponse {
    /**
     * 退货退款单集合
     */
    private List<BusinessGetRefundOrderResponseOrderItem> refunds;

    /**
     * 换货单集合
     */
    private List<BusinessGetExchangeOrderResponseOrderItem> exchanges;

    //region get/set
    public List<BusinessGetRefundOrderResponseOrderItem> getRefunds() {
        return refunds;
    }

    public void setRefunds(List<BusinessGetRefundOrderResponseOrderItem> refunds) {
        this.refunds = refunds;
    }

    public List<BusinessGetExchangeOrderResponseOrderItem> getExchanges() {
        return exchanges;
    }

    public void setExchanges(List<BusinessGetExchangeOrderResponseOrderItem> exchanges) {
        this.exchanges = exchanges;
    }
    //endregion
}
