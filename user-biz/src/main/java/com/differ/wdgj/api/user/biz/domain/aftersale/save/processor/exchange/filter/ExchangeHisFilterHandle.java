package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.filter;

import com.differ.wdgj.api.user.biz.domain.aftersale.common.data.enums.AfterSaleTableTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPreFiltrationOrderHandle;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;

/**
 * 换货单归档过滤器
 *
 * <AUTHOR>
 * @date 2024/11/26 下午4:48
 */
public class ExchangeHisFilterHandle extends AbstractPreFiltrationOrderHandle<BusinessGetExchangeOrderResponseOrderItem> {
    //region 构造
    public ExchangeHisFilterHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法

    /**
     * 前置过滤
     *
     * @param orderItem 原始售后单列表
     * @return 过滤结果
     */
    @Override
    public AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> orderItem, TargetCovertOrderItem targetOrder) {
        if(AfterSaleTableTypeEnum.HISTORY.equals(orderItem.getDbOrder().getAfterSaleTableType())){
            return AfterSaleHandleResult.failed("已归档的售后单不保存");
        }
        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "换货单归档过滤器";
    }
    //endregion
}
