package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchTempDO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * erp货品库存变动记录 仓储
 * 表g_api_sysMatch_temp
 *
 * <AUTHOR>
 * @date 2024/11/12 上午11:05
 */
public interface ApiSysMatchTempMapper {
    //region 普通库存同步

    /**
     * 根据执行时间查询普通库存同步触发数据
     *
     * @param status 状态
     * @param limitSize 限制条数
     * @return [erp货品变动通知]列表
     */
    List<ApiSysMatchTempDO> queryNormalByStatus(@Param("status") Integer status, @Param("limitSize") Integer limitSize);

    /**
     * 根据主键更新状态
     *
     * @param ids    [erp货品变动通知]id列表
     * @param status 状态
     * @return 影响行数
     */
    int updateStatusByIds(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 根据主键更新触发状态成功
     *
     * @param ids [erp货品变动通知]id列表
     * @return 影响行数
     */
    int updateSuccessByIds(@Param("ids") List<Long> ids);
    //endregion

    //region 多仓库存同步
    /**
     * 根据执行时间查询普通库存同步触发数据
     *
     * @param status 状态
     * @param limitSize 限制条数
     * @return [erp货品变动通知]列表
     */
    List<ApiSysMatchTempDO> queryMultiWareByStatus(@Param("status") Integer status, @Param("limitSize") Integer limitSize);

    /**
     * 根据主键更新状态
     *
     * @param ids    [erp货品变动通知]id列表
     * @param status 状态
     * @return 影响行数
     */
    int updateMultiWareStatusByIds(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 根据主键更新触发状态成功
     *
     * @param ids [erp货品变动通知]id列表
     * @return 影响行数
     */
    int updateMultiWareSuccessByIds(@Param("ids") List<Long> ids);
    //endregion

    /**
     * 插入单条记录
     *
     * @param record 待插入的记录
     * @return 影响行数
     */
    int insert(@Param("record") ApiSysMatchTempDO record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键ID
     * @return 记录详情
     */
    ApiSysMatchTempDO selectById(@Param("id") Integer id);

    /**
     * 清理过期的系统通知数据
     *
     * @param expireDate 过期时间
     * @return 影响行数
     */
    int cleanExpireNotice(@Param("expireDate") LocalDateTime expireDate);
}
