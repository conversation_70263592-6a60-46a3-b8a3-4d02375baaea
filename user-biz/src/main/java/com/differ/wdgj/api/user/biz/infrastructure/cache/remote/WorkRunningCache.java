package com.differ.wdgj.api.user.biz.infrastructure.cache.remote;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.AbstractHashEnhanceCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.WorkTaskDTO;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.SubPageTask;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.SubTask;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作任务执行中的临时数据存储
 *
 * <AUTHOR>
 * @date 2024/9/6 下午3:35
 */
public class WorkRunningCache extends AbstractHashEnhanceCache<WorkTaskDTO> {
    //region 常量
    /**
     * 查询单页最大数量
     */
    private static final int QUERY_MAX_PAGE_SIZE = 50;
    /**
     * 完成后清空数据的延迟时间（5分钟）
     */
    private static final int FINISH_CLEAR_RELAY = 300;

    private static final String HASH_KEY_PROGRESS = "progress";
    private static final String HASH_KEY_HEART = "heart";
    private static final String HASH_KEY_SUB_TASKS = "sub_tasks";

    /**
     * WorkRunningCache缓存cacheKey前缀
     */
    public static final String API_WORK_CACHE_KEY_PREFIX = "api:work";
    //endregion

    //region 构造
    protected WorkRunningCache(String cacheKey) {
        super(cacheKey);
        // 过期时间 1 ~ 2 天随机
        this.minCacheKeyTimeOut = 3600 * 24;
        this.maxCacheKeyTimeOut = this.minCacheKeyTimeOut * 2;
    }
    //endregion

    //region 静态公共方法

    /**
     * 获取缓存实例
     *
     * @param memberName 会员名
     * @return 缓存实例
     */
    public static WorkRunningCache build(String memberName, WorkEnum workEnum, String taskId) {
        // 缓存键：{api:会员:task_progress}，缓存键中不加集群号，防止会员切换集群导致进度变化
        String cacheKey = String.format("{%s:%s}:%s:%s", API_WORK_CACHE_KEY_PREFIX, memberName, workEnum, taskId);
        return new WorkRunningCache(cacheKey);
    }
    //endregion

    //region 公共方法

    /**
     * 增量增加进度值
     *
     * @param incrementValue 进度值
     */
    public void incrementProgress(long incrementValue) {
        if (0L == incrementValue) {
            return;
        }
        this.cacher.hashIncrement(this.cacheKey, HASH_KEY_PROGRESS, incrementValue);
        // 设置过期时间
        this.setRandomExpire(this.cacheKey);
    }

    /**
     * 设置进度值
     *
     * @param progressValue 进度值
     */
    public void setProgressValue(long progressValue ,boolean finish) {
        this.cacher.hashSyncStrValue(this.cacheKey, HASH_KEY_PROGRESS, String.valueOf(progressValue));
        // 设置过期时间
        if(finish){
            // 成功后要延迟清除数据，用于完成事件的其他一些处理，比如异步操作
            this.cacher.setExpire(cacheKey, FINISH_CLEAR_RELAY);
        }else {
            this.setRandomExpire(this.cacheKey);
        }
    }

    /**
     * 查询进度值
     *
     * @return 进度值
     */
    public Integer getProgressValue() {
        String progressValue = this.cacher.hashGetStrValue(this.cacheKey, HASH_KEY_PROGRESS);
        if (progressValue == null) {
            return 0;
        }
        return Integer.valueOf(progressValue);
    }

    /**
     * 刷新心跳
     */
    public void refreshHeart() {
        this.cacher.hashSyncStrValue(this.cacheKey, HASH_KEY_HEART, String.valueOf(System.currentTimeMillis()));
        // 设置过期时间
        this.setRandomExpire(this.cacheKey);
    }

    /**
     * 判断是否有心跳
     *
     * @param minHeartInterval 心跳的最小间隔时间(毫秒)
     * @return
     */
    public boolean hasHeart(long minHeartInterval) {
        String timestamp = this.cacher.hashGetStrValue(this.cacheKey, HASH_KEY_HEART);
        if (StringUtils.isEmpty(timestamp)) {
            return false;
        }
        long lastHeart = Long.parseLong(timestamp);

        return System.currentTimeMillis() - lastHeart <= minHeartInterval;
    }

    /**
     * 设置子任务列表
     *
     * @param subTasks 子任务列表
     */
    public void setSubTasks(List<SubTask> subTasks) {
        Map<String, String> hashKeyValues = new HashMap<>();
        hashKeyValues.put(HASH_KEY_SUB_TASKS, subTasks.stream().map(t -> t.getSubTypeKey()).collect(Collectors.joining(",")));
        for (SubTask subTask : subTasks) {
            String subTypeKey = subTask.getSubTypeKey();
            hashKeyValues.put(getSubTaskKey(subTypeKey), JsonUtils.toJson(subTask));
        }
        this.cacher.hashSyncStrValues(this.cacheKey, hashKeyValues);
        // 设置过期时间
        this.setRandomExpire(this.cacheKey);
    }

    /**
     * 更新子任务数据
     *
     * @param subTask 子任务
     */
    public void updateSubTask(SubPageTask subTask) {
        Map<String, String> hashKeyValues = new HashMap<>();
        hashKeyValues.put(getSubTaskKey(subTask.getSubTypeKey()), JsonUtils.toJson(subTask));
        this.cacher.hashSyncStrValues(this.cacheKey, hashKeyValues);
        // 设置过期时间
        this.setRandomExpire(this.cacheKey);
    }

    /**
     * 更新子任务数据
     *
     * @param subTask 子任务
     */
    public void updateSubTask(SubPageTask subTask, int increProgressValue) {
        Map<String, String> hashKeyValues = new HashMap<>();
        hashKeyValues.put(getSubTaskKey(subTask.getSubTypeKey()), JsonUtils.toJson(subTask));
        this.cacher.hashSyncStrValues(this.cacheKey, hashKeyValues);
        if (increProgressValue > 0) {
            // todo:应该需要更新内存缓存进度值
            this.cacher.hashIncrement(this.cacheKey, HASH_KEY_PROGRESS, increProgressValue);
        }
        // 设置过期时间
        this.setRandomExpire(this.cacheKey);
    }


    /**
     * 获取子任务的动态状态数据
     *
     * @param subTypeKey 子任务Key
     * @return 子任务
     */
    public SubTask getSubTaskDynamicStatus(String subTypeKey, Class<? extends SubTask> clazz) {
        return this.cacher.hashGet(this.cacheKey, getSubTaskKey(subTypeKey), clazz);
    }
    //endregion

    //region 实现基类方法
    @Override
    public Class<WorkTaskDTO> getValueClazz() {
        return WorkTaskDTO.class;
    }

    @Override
    protected WorkTaskDTO loadSource(String hashField) {
        return null;
    }

    @Override
    protected Map<String, WorkTaskDTO> loadSource(List<String> hashFields) {
        return new HashMap<>(0);
    }
    //endregion

    //region 私有方法

    /**
     * 子任务信息的hash key
     *
     * @param subTypeKey 子任务Key
     * @return 子任务信息的hashKey
     */
    private String getSubTaskKey(String subTypeKey) {
        return String.format("%s_sub_task", subTypeKey);
    }
    //endregion


}
