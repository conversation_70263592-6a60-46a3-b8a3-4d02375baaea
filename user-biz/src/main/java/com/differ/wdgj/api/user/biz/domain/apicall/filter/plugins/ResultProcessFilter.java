package com.differ.wdgj.api.user.biz.domain.apicall.filter.plugins;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatResponseBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallExtendedInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.filter.ApiCallFilterChain;
import com.differ.wdgj.api.user.biz.domain.apicall.filter.ApiCallFilterParameter;
import com.differ.wdgj.api.user.biz.domain.apicall.filter.IApiCallFilter;
import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;

/**
 * API调用过滤器 - 结果处理
 *
 * <AUTHOR>
 * @date 2022-03-14 20:48
 */
@ApiCallFilterParameter(order = 1)
public class ResultProcessFilter<RequestBizData extends BasePlatRequestBizData, ResponseBizData extends BasePlatResponseBizData>
        implements IApiCallFilter<RequestBizData, ResponseBizData> {

    /**
     * 执行过滤
     *
     * @param context          上下文
     * @param requestBizData   请求业务数据
     * @param callExtendedInfo 请求扩展数据
     * @param response         响应
     * @param filterChain      过滤器链
     */
    @Override
    public void doFilter(
            ApiCallContext context,
            RequestBizData requestBizData,
            ApiCallExtendedInfo callExtendedInfo,
            ApiCallResponse<ResponseBizData> response,
            ApiCallFilterChain<RequestBizData, ResponseBizData> filterChain
    ) {

        // 执行过滤
        filterChain.doFilter(context, requestBizData, callExtendedInfo, response, filterChain);

        // 返回信息不为空
        if (StringUtils.isEmpty(response.getSubMessage())) {
            return;
        }

        // 菠萝派Id不为空
        if (StringUtils.isEmpty(response.getPolyApiRequestId()) || "0".equals(response.getPolyApiRequestId())) {
            return;
        }

        // 返回信息不包括菠萝派Id
        if (response.getSubMessage().contains(response.getPolyApiRequestId())) {
            return;
        }

        // 拼接菠萝派Id到返回信息上
        response.setSubMessage(response.getSubMessage() + "[" + response.getPolyApiRequestId() + "]");
    }
}
