package com.differ.wdgj.api.user.biz.domain.apicall.processor;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatResponseBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallExtendedInfo;

/**
 * API调用处理器接口
 *
 * <AUTHOR>
 * @date 2022-03-08 18:19
 */
public interface IApiCallProcessor<RequestBizData extends BasePlatRequestBizData, ResponseBizData extends BasePlatResponseBizData> {
    /**
     * 请求
     *
     * @param context          上下文
     * @param requestBizData   请求业务数据
     * @param responseBizType  响应业务数据类型
     * @param callExtendedInfo 请求扩展信息
     * @return 响应
     */
    ApiCallResponse<ResponseBizData> call(ApiCallContext context, RequestBizData requestBizData, Class<? extends ResponseBizData> responseBizType, ApiCallExtendedInfo callExtendedInfo);
}