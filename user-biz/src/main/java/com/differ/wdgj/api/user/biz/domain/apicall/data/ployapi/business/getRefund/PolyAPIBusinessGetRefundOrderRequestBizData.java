package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund;

import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;

import java.time.LocalDateTime;

/**
 * 退货退款单下载请求
 * {@link PolyAPITypeEnum} BUSINESS_GETREFUND
 *
 * <AUTHOR>
 * @date 2024/8/26 下午6:26
 */
public class PolyAPIBusinessGetRefundOrderRequestBizData extends BasePlatRequestBizData {
    // region 构造器

    /**
     * 构造器，默认页码为1，每页条数为10
     */
    public PolyAPIBusinessGetRefundOrderRequestBizData() {
        this.pageIndex = 1;
        this.pageSize = 10;
    }

    // endregion

    // region 变量

    /**
     * 订单退款状态
     */
    private String status;

    /**
     * 订单退款类别
     */
    private String refundType;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 截止时间
     */
    private LocalDateTime endTime;

    /**
     * 订单时间类别
     */
    private String timeType;

    /**
     * 页码
     */
    private int pageIndex;

    /**
     * 每页条数
     */
    private int pageSize;

    /**
     * 商品所在仓库编号
     */
    private String wareHouse;

    /**
     * 昵称
     */
    private String buyerNick;

    /**
     * 获取下一页订单所需的token值
     */
    private String nextToken;

    /**
     * 退款单号
     */
    private String return_sn;

    /**
     * 退款主单号（当把平台子售后单号作为菠萝派售后单号时用到这个字段）
     */
    private String mainRefundNo;

    /**
     * 平台订单号
     */
    private String platOrderNo;

    /**
     * 是否需要内调详情接口，一般用于平台之前列表接口数据不足需要调用详情接口，调用方告知不需要内调详情
     */
    private boolean notNeedDetail;

    /**
     * 退货退款单是否需要详情
     */
    private boolean hasDetail;

    /**
     * 订单请求来源
     */
    private String queryType;

    /**
     * 活动 Id
     */
    private String activityId;

    /**
     * 店铺类型
     */
    private String shopType;

    /**
     * 订单类别
     */
    private String orderType;

    /**
     * 平台店铺 Id
     */
    private String shopId;

    /**
     * 子店铺ID
     */
    private String sipShopId;

    // endregion

    //region get/set

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(LocalDateTime beginTime) {
        this.beginTime = beginTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getWareHouse() {
        return wareHouse;
    }

    public void setWareHouse(String wareHouse) {
        this.wareHouse = wareHouse;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getTimeType() {
        return timeType;
    }

    public void setTimeType(String timeType) {
        this.timeType = timeType;
    }

    public String getNextToken() {
        return nextToken;
    }

    public void setNextToken(String nextToken) {
        this.nextToken = nextToken;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public String getReturn_sn() {
        return return_sn;
    }

    public String getMainRefundNo() {
        return mainRefundNo;
    }

    public String getPlatOrderNo() {
        return platOrderNo;
    }

    public boolean isNotNeedDetail() {
        return notNeedDetail;
    }

    public boolean isHasDetail() {
        return hasDetail;
    }

    public String getActivityId() {
        return activityId;
    }

    public String getShopType() {
        return shopType;
    }

    public String getOrderType() {
        return orderType;
    }

    public String getShopId() {
        return shopId;
    }

    public String getSipShopId() {
        return sipShopId;
    }

    public void setReturn_sn(String return_sn) {
        this.return_sn = return_sn;
    }

    public void setMainRefundNo(String mainRefundNo) {
        this.mainRefundNo = mainRefundNo;
    }

    public void setPlatOrderNo(String platOrderNo) {
        this.platOrderNo = platOrderNo;
    }

    public void setNotNeedDetail(boolean notNeedDetail) {
        this.notNeedDetail = notNeedDetail;
    }

    public void setHasDetail(boolean hasDetail) {
        this.hasDetail = hasDetail;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public void setShopType(String shopType) {
        this.shopType = shopType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public void setSipShopId(String sipShopId) {
        this.sipShopId = sipShopId;
    }

    public String getQueryType() {
        return queryType;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }
    //endregion
}

