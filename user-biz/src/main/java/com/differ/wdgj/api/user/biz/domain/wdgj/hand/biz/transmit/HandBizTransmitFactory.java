package com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit;

import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data.HandBizTransmitMqDto;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data.HandBizTransmitResult;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.processor.HandCreateShopProcessor;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.processor.HandGetAfterSaleProcessor;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.processor.HandUpdateShopConfigProcessor;

/**
 * 手动业务转发处理器工厂
 *
 * <AUTHOR>
 * @date 2024/10/28 下午5:54
 */
public class HandBizTransmitFactory {
    //region 构造
    private HandBizTransmitFactory() {
    }
    //endregion

    //region 公共方法

    /**
     * 执行转发业务逻辑
     *
     * @param dto 业务对象
     * @return 执行结果
     */
    public static HandBizTransmitResult execute(HandBizTransmitMqDto dto) {
        IHandBizTransmitProcessor processor = create(dto);
        return processor.execute(dto);
    }
    //endregion

    //region 私有方法

    /**
     * 创建 手动业务转发处理器
     *
     * @param dto 业务对象
     * @return 手动业务转发处理器
     */
    private static IHandBizTransmitProcessor create(HandBizTransmitMqDto dto) {
        switch (dto.getTransmitType()) {
            case GET_AFTER_SALE:
                return new HandGetAfterSaleProcessor();
            case CREATE_SHOP:
                return new HandCreateShopProcessor();
            case UPDATE_SHOP_CONFIG:
                return new HandUpdateShopConfigProcessor();
            default:
                throw new RuntimeException("未实现的手动业务转发类型");
        }
    }
    //endregion
}
