package com.differ.wdgj.api.user.biz.infrastructure.data.event;


import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AlarmIntervalTypeEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description 线程池队列积压报警事件
 * <AUTHOR>
 * @Date 2021/11/23 14:16
 */
public class ThreadPoolBlockingQueueAlarmEvent extends AlarmIntervalEvent {

    /**
     * 报警内容格式
     */
    private static final String alarmFormat = "%s,当前队列积压:%d,已超过警戒线:%d,执行中任务数:%d,明细:%s";

    public ThreadPoolBlockingQueueAlarmEvent(String namePreFix, int blockingQueueAlarmLimit, int blockingQueueCurrentSize,Runnable[] runningTasks) {
        super(AlarmIntervalTypeEnum.THREAD_POOL, String.format("blocking-queue:%s", namePreFix), null);
        this.setAlarmText(buildAlarmText(namePreFix,  blockingQueueAlarmLimit,  blockingQueueCurrentSize, runningTasks));
    }

    /**
     * 报警内容
     * @param namePreFix
     * @param blockingQueueAlarmLimit
     * @param blockingQueueCurrentSize
     * @param runningTasks
     * @return
     */
    private String buildAlarmText(String namePreFix, int blockingQueueAlarmLimit, int blockingQueueCurrentSize,Runnable[] runningTasks) {
        return String.format(alarmFormat, namePreFix, blockingQueueCurrentSize, blockingQueueAlarmLimit, runningTasks.length, StringUtils.join(runningTasks, ";"));
    }
}
