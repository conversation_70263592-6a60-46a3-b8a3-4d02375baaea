'关系图使用要求：
'1.使用关联依赖聚合组合这4大关系时，一定要将代码体现标注上，看起来会更通俗易懂。
'2.继承类和接口的关系能区分更好，不区分也不必过于纠结（不想区分时可统一为继承类方式画图），因为plantUML中已有明显的接口和类的区别

'可访问性说明
'private -
'protect #
'package private ~
'public +
'其他参见plantuml类图说明：https://plantuml.com/zh/class-diagram

@startuml
interface SubQueueFactory <<子队列创建工厂接口>>{

}

class KafkaConsumerContainer {
    - static Map<KafkaGroup, KafkaAdapter> consumerContainer : 消费者容器
    + static KafkaAdapter getTopicGroupConsumer(String topic, KafkaProperties kafkaProperties) : 获取消费者适配器，唯一维度：地址，topic，消费组
}

class SubQueueCommonKafkaFactory {
     String getEngineQueueCode(String multiQueueCode) : 获取队列引擎对应的队列代码
     SubQueue createSubQueue(SubQueueContext subQueueContext, Class<T> dataClazz) : 创建子队列
}

class TaskTriggerRegister {
    +void register(String groupId, Action action) ： 注册任务函数，用于外部触发后执行
}

class KafkaAdapter {
    +void initListener() : 初始化监听器
    +void send(String message, QueueHeader header) : 发送消息到kafka
}

enum SubQueueEnum <<子队列枚举>> {
    SubQueueFactory factory : 队列创建工厂
}
SubQueueFactory <|.. SubQueueCommonKafkaFactory :继承

KafkaAdapter <.. KafkaSubQueue :依赖（代码体现：成员变量）

KafkaAdapter <.. SubQueueCommonKafkaFactory :依赖（代码体现：内部变量）
KafkaConsumerContainer <.. SubQueueCommonKafkaFactory :依赖（代码体现：内部变量）
KafkaSubQueue <.. SubQueueCommonKafkaFactory :依赖（代码体现：返回值）
KafkaTopicEnum  <.. SubQueueCommonKafkaFactory :依赖（代码体现：内部变量）
ConfigTemplate  <.. KafkaTopicEnum :依赖（代码体现：成员变量）
KafkaProperties  <.. ConfigTemplate :依赖（代码体现：返回值）
TaskTriggerRegister <.. KafkaSubQueue :依赖（代码体现：注册到定时任务触发）
HeaderKafkaTemplate <.. KafkaAdapter :依赖（代码体现：成员变量）

SubQueueFactory  <.. SubQueueEnum
@enduml