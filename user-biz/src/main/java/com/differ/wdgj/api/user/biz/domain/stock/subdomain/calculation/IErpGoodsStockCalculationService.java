package com.differ.wdgj.api.user.biz.domain.stock.subdomain.calculation;

import com.differ.wdgj.api.user.biz.domain.stock.data.result.ErpGoodsStockCalculationResult;

import java.util.List;

/**
 * erp商品库存计算服务
 *
 * <AUTHOR>
 * @date 2025/6/16 20:04
 */
public interface IErpGoodsStockCalculationService {
    /**
     * 计算普通商品库存
     *
     * @param shopId       店铺Id
     * @param erpGoodsId   erp商品Id
     * @param erpSpecId    erp规格Id
     * @param warehouseIds erp仓库Id列表
     * @return 计算结果
     */
    ErpGoodsStockCalculationResult calculationNormalStock(Integer shopId, Integer erpGoodsId, Integer erpSpecId, List<Integer> warehouseIds);

    /**
     * 计算组合商品库存
     *
     * @param type         同步规则
     * @param fitId        erp组合商品Id
     * @param warehouseIds erp仓库Id列表
     * @return 计算结果
     */
    ErpGoodsStockCalculationResult calculationFitStock(Integer type, Integer fitId, List<Integer> warehouseIds);
}
