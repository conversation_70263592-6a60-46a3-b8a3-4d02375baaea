package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.data;

/**
 * 用户流式工作上下文
 *
 * <AUTHOR>
 * @date 2022/9/6 14:24
 */
public abstract class UserFlowJobContext {

    /**
     * 会员名
     */
    private String memberName;

    // region getter & setter

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    // endregion
}
