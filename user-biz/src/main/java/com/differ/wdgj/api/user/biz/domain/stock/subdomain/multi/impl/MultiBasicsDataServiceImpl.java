package com.differ.wdgj.api.user.biz.domain.stock.subdomain.multi.impl;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.goods.match.utils.GoodsMatchLogUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.MappingSetTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncInvalidStoreResult;
import com.differ.wdgj.api.user.biz.domain.stock.subdomain.multi.IMultiBasicsDataService;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchLogDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchLogMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.CfgMappingSetMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 多仓基础数据服务
 * <AUTHOR>
 * @date 2024-03-19 10:32
 */
@Service
public class MultiBasicsDataServiceImpl implements IMultiBasicsDataService {
    /**
     * 删除多门店匹配信息
     * @param vipUser 会员名
     * @param operator 操作人
     * @param mappingSetType 匹配类型
     * @param invalidStoreResults 无效店铺结果列表
     */
    @Override
    public void delMultiStoreMatch(String vipUser, String operator, MappingSetTypeEnum mappingSetType, List<StockSyncInvalidStoreResult> invalidStoreResults){
        if(StringUtils.isBlank(vipUser) || CollectionUtils.isEmpty(invalidStoreResults)){
            return;
        }

        // 构建商品匹配日志
        List<ApiSysMatchLogDO> apiSysMatchLogs = new ArrayList<>();
        Set<String> invalidStoreIds = new HashSet<>();
        invalidStoreResults.forEach(apiSysMatch ->{
            String logDetail = String.format("系统自动删除无效的网点匹配，网点ID：%s", apiSysMatch.getPlatStoreId());
            ApiSysMatchLogDO apiSysMatchLog = GoodsMatchLogUtils.createLog(operator, logDetail, apiSysMatch.getSysMatch());
            apiSysMatchLogs.add(apiSysMatchLog);
            invalidStoreIds.add(apiSysMatch.getPlatStoreId());
        });


        // 拆分
        List<List<ApiSysMatchLogDO>> subApiSysMatchLogList = Lists.partition(apiSysMatchLogs, 200);

        // 批量进行数据库操作
        CfgMappingSetMapper cfgMappingSetMapper = BeanContextUtil.getBean(CfgMappingSetMapper.class);
        ApiSysMatchLogMapper apiSysMatchLogMapper = BeanContextUtil.getBean(ApiSysMatchLogMapper.class);
        DBSwitchUtil.doTransaction(vipUser, () ->{
            cfgMappingSetMapper.deleteByPlatStoreId(mappingSetType.getName(), new ArrayList<>(invalidStoreIds));
            for (List<ApiSysMatchLogDO> apiSysMatchLogList : subApiSysMatchLogList) {
                apiSysMatchLogMapper.batchAddApiSysMatchLogs(apiSysMatchLogList);
            }
            return true;
        });
    }
}
