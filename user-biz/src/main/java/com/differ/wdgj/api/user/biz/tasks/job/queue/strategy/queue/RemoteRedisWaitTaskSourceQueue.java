package com.differ.wdgj.api.user.biz.tasks.job.queue.strategy.queue;


import com.differ.wdgj.api.component.redis.MultiRedis;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.component.wait.PullWaitLine;
import com.differ.wdgj.api.component.wait.data.CommonWaitEntity;
import com.differ.wdgj.api.component.wait.data.PullWaitContext;
import com.differ.wdgj.api.component.wait.impl.BasePullWaitHandler;
import com.differ.wdgj.api.component.wait.strategy.PullSimpleStrategy;
import com.differ.wdgj.api.user.biz.tasks.job.queue.data.QueueJobData;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 以Redis排队组件为基础的负载均衡任务队列
 *
 * <AUTHOR>
 * @date 2023-12-27 10:00
 */
public class RemoteRedisWaitTaskSourceQueue<T extends QueueJobData> extends AbstractTaskSourceQueue<T> {

    /**
     * 拉取排队处理
     */
    private PullWaitLine pullWaitHandler;

    /**
     * 初始化队列配置
     */
    @Override
    protected void initQueue() {
        // 排队上下文
        MultiRedis multiRedis = BeanContextUtil.getBean(MultiRedis.class);
        PullWaitContext context = new PullWaitContext(multiRedis, this.jobCode);
        context.setExecutingExpire(this.funExecTimeout.exec());
        // 初始化拉取模式的排队处理器：设置拉取策略和排队上下文
        this.pullWaitHandler = new BasePullWaitHandler();
        this.pullWaitHandler.initPullMode(context, new PullSimpleStrategy());
    }

    /**
     * 添加任务
     *
     * @param waitUniqueId 任务排队唯一ID
     * @param taskData     任务数据
     * @return 是否成功
     */
    public boolean addTask(String waitUniqueId, String taskData) {
        checkAndRefreshExecTimeout();
        return this.pullWaitHandler.putWait(new CommonWaitEntity(waitUniqueId, taskData));
    }

    /**
     * 添加任务
     *
     * @param task        任务数据
     * @return 是否成功 true:放入排队（新增排队）, false:已存在（排队中或执行中）
     */
    @Override
    public boolean addTask(T task) {
        checkAndRefreshExecTimeout();
        return this.pullWaitHandler.putWait(new CommonWaitEntity(task.taskUniqueId(), task.serialize()));
    }

    /**
     * 添加任务
     *
     * @param tasks       任务数据
     * @return 是否成功
     */
    @Override
    public void addTasks(List<T> tasks) {
        checkAndRefreshExecTimeout();
        for (T task : tasks) {
            this.pullWaitHandler.putWait(new CommonWaitEntity(task.taskUniqueId(), task.serialize()));
        }
    }

    /**
     * 拉取下一个排队数据
     *
     * @return 返回排队的key和数据
     */
    public CommonWaitEntity pullNextData() {
        return pullWaitHandler.pullNext();
    }

    /**
     * 拉取下一个排队数据
     *
     * @return 返回排队的key和数据
     */
    @Override
    public Map<String, T> pullNext() {
        CommonWaitEntity data = pullWaitHandler.pullNext();
        if (data == null) {
            return null;
        }

        Map<String, T> next = new HashMap<>();
        T jobData = resolve(data.getDataString());
        if (jobData != null) {
            next.put(data.getWaitUniqueId(), jobData);
        }
        return next;
    }

    /**
     * 完成任务
     *
     * @param waitUniqueId 排队的任务key
     * @param task        任务数据
     */
    @Override
    public void complete(String waitUniqueId,T task) {
        pullWaitHandler.complete(waitUniqueId);
    }

    /**
     * 检查并刷新执行过期时间
     *
     */
    private void checkAndRefreshExecTimeout() {
        int execTimeout = this.funExecTimeout.exec();
        if (this.pullWaitHandler.getWaitContext().getExecutingExpire() != execTimeout) {
            this.pullWaitHandler.getWaitContext().setExecutingExpire(execTimeout);
        }
    }

    public PullWaitLine getPullWaitHandler() {
        return pullWaitHandler;
    }
}
