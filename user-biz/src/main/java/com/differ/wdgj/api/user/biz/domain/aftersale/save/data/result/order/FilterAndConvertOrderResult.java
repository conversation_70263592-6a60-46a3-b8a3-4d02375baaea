package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleProcessTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import org.apache.commons.lang3.StringUtils;

/**
 * 订单级结果-过滤/转换售后订单结果
 *
 * <AUTHOR>
 * @date 2024-05-30 14:16
 */
public class FilterAndConvertOrderResult extends BaseAfterSaleOrderResult {
    //region 属性

    /**
     * 是否需要业务重试
     */
    private boolean isNeedBizRetry;

    /**
     * 处理模式
     */
    private AfterSaleProcessTypeEnum processType;

    /**
     * 售后单目标对象
     */
    private TargetCovertOrderItem targetOrder;
    //endregion

    //region 构造

    private FilterAndConvertOrderResult(String afterSaleNo, boolean success, String message) {
        super(afterSaleNo, success, message);
    }

    //endregion

    //region 公共方法

    /**
     * 失败的结果
     *
     * @param afterSaleNo    售后单号
     * @param message        失败信息
     * @return 结果
     */
    public static FilterAndConvertOrderResult failed(String afterSaleNo, String message) {
        return new FilterAndConvertOrderResult(afterSaleNo, false, message);
    }

    /**
     * 失败的结果
     *
     * @param afterSaleNo    售后单号
     * @param message        失败信息
     * @param isNeedBizRetry 是否需要业务重试
     * @return 结果
     */
    public static FilterAndConvertOrderResult failed(String afterSaleNo, String message, boolean isNeedBizRetry) {
        FilterAndConvertOrderResult result = new FilterAndConvertOrderResult(afterSaleNo, false, message);
        result.setNeedBizRetry(isNeedBizRetry);
        return result;
    }

    /**
     * 成功的结果
     *
     * @param afterSaleNo    售后单号
     * @param processType    处理方式
     * @param targetOrder    售后单结果
     * @param isNeedBizRetry 是否需要业务重试
     * @return 结果
     */
    public static FilterAndConvertOrderResult success(String afterSaleNo, AfterSaleProcessTypeEnum processType, TargetCovertOrderItem targetOrder, boolean isNeedBizRetry) {
        FilterAndConvertOrderResult result = new FilterAndConvertOrderResult(afterSaleNo, true, StringUtils.EMPTY);
        result.setProcessType(processType);
        result.setTargetOrder(targetOrder);
        result.setNeedBizRetry(isNeedBizRetry);
        return result;
    }

    //endregion

    //region get/set

    public boolean isNeedBizRetry() {
        return isNeedBizRetry;
    }

    public void setNeedBizRetry(boolean needBizRetry) {
        isNeedBizRetry = needBizRetry;
    }

    public AfterSaleProcessTypeEnum getProcessType() {
        return processType;
    }

    public void setProcessType(AfterSaleProcessTypeEnum processType) {
        this.processType = processType;
    }

    public TargetCovertOrderItem getTargetOrder() {
        return targetOrder;
    }

    public void setTargetOrder(TargetCovertOrderItem targetOrder) {
        this.targetOrder = targetOrder;
    }

    //endregion
}
