package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.PolyAfterSaleBasicInfo;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleProcessTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.FilterAndConvertOrderResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleSaveBizType;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceRefundGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.AfterSaleGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.RefundTypeCovertOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.AbstractSaveAfterSaleOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.ReturnHandleComposite;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.*;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.batch.RefundPlatSpecialBatchHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.goods.*;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.order.RefundOrderCovertHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.order.RefundPlatSpecialOrderCovertHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.post.RefundGoodsDeleteProcessHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.post.RefundOrderChangeProcessHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.convert.post.RefundPlatSpecialPostProcessHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.filter.RefundHisFilterHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.filter.RefundPlatSpecialFilterHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.filter.RefundPolyErrorFilterHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.filter.RefundTypeFilterHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.filter.RefundPlatUpdateHisFilterHandle;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleConfigKeyUtils;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleCovertUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 保存退货退款单处理器
 *
 * <AUTHOR>
 * @date 2024-06-05 16:00
 */
public class BaseSaveRefundOrderProcessor extends AbstractSaveAfterSaleOrderProcessor<BusinessGetRefundOrderResponseOrderItem> {
    //region 变量
    /**
     * 日志
     */
    private final Logger log = LoggerFactory.getLogger(BaseSaveRefundOrderProcessor.class);

    /**
     * 插件组合
     */
    private ReturnHandleComposite<BusinessGetRefundOrderResponseOrderItem, BusinessGetRefundResponseRefundGoodInfo> returnHandleComposite;

    //endregion

    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public BaseSaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }

    //endregion

    //region 重写基类方法

    /**
     * 初始化
     *
     * @param context 上下文
     */
    @Override
    public void init(AfterSaleSaveContext context) {
        super.init(context);

        //region 插件初始化
        // 前置批量查询插件列表
        List<IPerBatchProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem>> perBatchQueryOrders = createPerBatchQueryHandles();
        // 构建前置过滤插件列表
        List<IPreFiltrationOrderHandle<BusinessGetRefundOrderResponseOrderItem>> subPreFilters = createPreFiltrationHandles();
        // 构建售后单单转换插件列表
        List<IOrderConvertHandle<BusinessGetRefundOrderResponseOrderItem>> orderConverts = createOrderConvertHandles();
        // 售后单退货商品转换插件列表
        List<IRefundGoodsConvertHandle<BusinessGetRefundOrderResponseOrderItem, BusinessGetRefundResponseRefundGoodInfo>> returnGoodsConverts = createReturnGoodsConvertsHandles();
        // 后置处理插件列表
        List<IPostProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem>> postProcesses = createPostProcessHandles();
        // 组合插件
        returnHandleComposite = new ReturnHandleComposite<>(perBatchQueryOrders, subPreFilters, orderConverts, returnGoodsConverts, postProcesses);
        //endregion
    }

    /**
     * 获取菠萝派售后单基础信息
     *
     * @param orderItem 菠萝派售后单
     * @return 菠萝派售后单基础信息
     */
    @Override
    protected PolyAfterSaleBasicInfo getPolyAfterSaleBasicInfo(BusinessGetRefundOrderResponseOrderItem orderItem) {
        return new PolyAfterSaleBasicInfo(orderItem.getRefundNo(), orderItem.getPlatOrderNo());
    }

    /**
     * 获取订单级业务类型
     *
     * @param orderItem 菠萝派售后单信息
     * @param dbOrder   数据库订单信息
     * @return 售后单号
     */
    @Override
    protected final AfterSaleSaveBizType getBizType(BusinessGetRefundOrderResponseOrderItem orderItem, DbAfterSaleOrderItem dbOrder) {
        ShopTypeEnum shopType = getShopType(orderItem);
        ApiAfterSaleTypeEnum apiAfterSaleType = getApiAfterSaleType(orderItem, dbOrder, shopType);
        return AfterSaleSaveBizType.build(shopType, apiAfterSaleType);
    }

    /**
     * 过滤/转换售后订单列表
     *
     * @param sourceAfterSaleOrders 原始售后单列表
     * @return 结果
     */
    @Override
    protected final SaveAfterSaleResult<List<FilterAndConvertOrderResult>> filterAndConvertPolyOrder(List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> sourceAfterSaleOrders) {
        if(CollectionUtils.isEmpty(sourceAfterSaleOrders)){
            return SaveAfterSaleResult.success(new ArrayList<>());
        }

        ArrayList<FilterAndConvertOrderResult> orderResults = new ArrayList<>();
        // 前置批量处理
        AfterSaleHandleResult preQueryResult = returnHandleComposite.preBatchProcess(sourceAfterSaleOrders);
        if (preQueryResult.isFailed()) {
            return SaveAfterSaleResult.failed(preQueryResult.getMessage());
        }

        // 订单级数据处理
        for (SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder : sourceAfterSaleOrders) {
            orderResults.add(convertPolyOrder(sourceOrder));
        }

        return SaveAfterSaleResult.success(orderResults);
    }

    //endregion

    //region 主流程

    /**
     * 过滤/转换订单级数据
     *
     * @param sourceOrder 原始售后单列表
     * @return 结果
     */
    private FilterAndConvertOrderResult convertPolyOrder(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder) {
        // 基础数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        String afterSaleNo = ployOrder.getRefundNo();
        try {
            TargetCovertOrderItem targetOrder = new TargetCovertOrderItem();
            // 1、前置过滤订单
            AfterSaleHandleResult perFilterResult = returnHandleComposite.preFiltrationOrder(sourceOrder, targetOrder);
            if (perFilterResult.isFailed()) {
                return FilterAndConvertOrderResult.failed(afterSaleNo, perFilterResult.getMessage(), targetOrder.isNeedBizRetry());
            }

            // 2、转换订单级信息
            AfterSaleHandleResult orderConvertResult = returnHandleComposite.orderConvert(sourceOrder, targetOrder);
            if (orderConvertResult.isFailed()) {
                return FilterAndConvertOrderResult.failed(afterSaleNo, orderConvertResult.getMessage(), targetOrder.isNeedBizRetry());
            }

            // 3、转换商品级信息
            List<ApiReturnDetailDO> targetRefundGoodsList = new ArrayList<>();
            AfterSaleHandleResult convertGoodsResult = convertPolyRefundGoodsList(sourceOrder, targetOrder, targetRefundGoodsList);
            if (convertGoodsResult.isFailed()) {
                return FilterAndConvertOrderResult.failed(afterSaleNo, orderConvertResult.getMessage(), targetOrder.isNeedBizRetry());
            }
            targetOrder.getRefundGoods().addAll(targetRefundGoodsList);

            // 4、后置处理
            AfterSaleHandleResult postProcessesResult = returnHandleComposite.postProcess(sourceOrder, targetOrder);
            if (postProcessesResult.isFailed()) {
                return FilterAndConvertOrderResult.failed(afterSaleNo, postProcessesResult.getMessage(), targetOrder.isNeedBizRetry());
            }

            // 结果整合
            AfterSaleProcessTypeEnum processType = targetOrder.getProcessType();
            boolean needBizRetry = targetOrder.isNeedBizRetry();
            return FilterAndConvertOrderResult.success(afterSaleNo, processType, targetOrder, needBizRetry);
        } catch (Exception e) {
            String logContent = String.format("【%s】【%s】售后单[%s]转换失败，失败原因:%s", context.getMemberName(), context.getShopId(), afterSaleNo, e.getMessage());
            log.error(logContent, e);
            return FilterAndConvertOrderResult.failed(afterSaleNo, logContent);
        }
    }

    /**
     * 批量转换退货商品
     *
     * @param sourceOrder           原始售后单数据
     * @param targetOrder           售后单
     * @param targetRefundGoodsList 目标售后退货商品列表
     * @return 结果
     */
    private AfterSaleHandleResult convertPolyRefundGoodsList(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder, final List<ApiReturnDetailDO> targetRefundGoodsList) {
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        DbAfterSaleOrderItem dbOrder = sourceOrder.getDbOrder();

        List<BusinessGetRefundResponseRefundGoodInfo> refundGoodsList = ployOrder.getRefundGoods();
        if (CollectionUtils.isNotEmpty(refundGoodsList)) {
            // 循环退货商品级转换商品信息
            for (BusinessGetRefundResponseRefundGoodInfo refundGoods : refundGoodsList) {
                // 基础数据
                ApiReturnDetailDO targetRefundGoods = new ApiReturnDetailDO();
                ApiTradeGoodsDO apiTradeGoods = CollectionUtils.isNotEmpty(dbOrder.getApiTradeGoodsList())
                        ? dbOrder.getApiTradeGoodsList().stream().filter(x -> isMatchApiTradeGoods(x, null, refundGoods)).findFirst().orElse(null)
                        : null;
                SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> sourceRefundsGoods = new SourceRefundGoodsItem<>(refundGoods, apiTradeGoods);
                // 转化商品
                GoodsConvertHandleResult goodsConvertResult = returnHandleComposite.refundGoodsConvert(sourceOrder, sourceRefundsGoods, targetOrder, targetRefundGoods);
                if (goodsConvertResult.isFailed()) {
                    return AfterSaleHandleResult.failed(goodsConvertResult.getMessage());
                }
                if (!goodsConvertResult.isSaveGoods()) {
                    continue;
                }
                targetRefundGoodsList.add(targetRefundGoods);
            }
        } else {
            // 部分平台没有商品级，将商品信息放到订单级(历史逻辑)
            ApiReturnDetailDO targetRefundGoods = new ApiReturnDetailDO();
            ApiTradeGoodsDO apiTradeGoods = CollectionUtils.isNotEmpty(dbOrder.getApiTradeGoodsList())
                    ? dbOrder.getApiTradeGoodsList().stream().filter(x -> isMatchApiTradeGoods(x, sourceOrder.getPloyOrder(), null)).findFirst().orElse(null)
                    : null;
            SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> sourceRefundsGoods = new SourceRefundGoodsItem<>(apiTradeGoods);
            // 转化商品
            GoodsConvertHandleResult goodsConvertResult = returnHandleComposite.refundGoodsConvert(sourceOrder, sourceRefundsGoods, targetOrder, targetRefundGoods);
            if (goodsConvertResult.isFailed()) {
                return AfterSaleHandleResult.failed(goodsConvertResult.getMessage());
            }
            if (!goodsConvertResult.isSaveGoods()) {
                return AfterSaleHandleResult.success();
            }
            targetRefundGoodsList.add(targetRefundGoods);
        }

        return AfterSaleHandleResult.success();
    }
    //endregion

    //region 子类可重写方法

    /**
     * 获取店铺类型
     * @param orderItem 菠萝派售后单信息
     * @return 店铺类型
     */
    protected ShopTypeEnum getShopType(BusinessGetRefundOrderResponseOrderItem orderItem){
        ShopTypeEnum shopTypeEnum = ShopTypeEnum.create(context.getPlat(), orderItem.getShopType());
        if(shopTypeEnum != null){
            return shopTypeEnum;
        }

        return ShopTypeEnum.DEFAULT;
    }

    /**
     * 获取api售后单类型
     *
     * @param orderItem 菠萝派售后单信息
     * @param dbOrder   数据库订单信息
     * @param shopType  店铺类型
     * @return api售后单类型
     */
    protected ApiAfterSaleTypeEnum getApiAfterSaleType(BusinessGetRefundOrderResponseOrderItem orderItem, DbAfterSaleOrderItem dbOrder, ShopTypeEnum shopType){
        if(AfterSaleConfigKeyUtils.isActionApiAfterSaleTypeConvertV2(context.getMemberName())){
            return RefundTypeCovertOperation.singleton().covertApiAfterSaleType(context, shopType, orderItem, dbOrder);
        }
        return AfterSaleCovertUtils.covertApiAfterSaleType(context, orderItem);
    }

    //region 前置批量查询插件

    /**
     * 创建前置批量查询插件
     */
    protected List<IPerBatchProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem>> createPerBatchQueryHandles() {
        List<IPerBatchProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem>> preBatchHandles = new ArrayList<>();
        // 平台级特殊处理
        RefundPlatSpecialBatchHandle refundPlatSpecialFilterHandle = new RefundPlatSpecialBatchHandle(context);
        refundPlatSpecialFilterHandle.setCoveringMethod(this::preBatchOrder);
        preBatchHandles.add(refundPlatSpecialFilterHandle);
        return preBatchHandles;
    }

    /**
     * 前置批量查询
     *
     * @param orderItems 原始售后单数据列表
     * @return 结果
     */
    protected AfterSaleHandleResult preBatchOrder(List<SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem>> orderItems) {
        // 供子类重写
        return AfterSaleHandleResult.success();
    }

    //endregion

    //region 前置过滤

    /**
     * 创建前置过滤插件
     */
    protected List<IPreFiltrationOrderHandle<BusinessGetRefundOrderResponseOrderItem>> createPreFiltrationHandles() {
        List<IPreFiltrationOrderHandle<BusinessGetRefundOrderResponseOrderItem>> preFiltrationHandles = new ArrayList<>();
        // 退货退款单菠萝派返回错误过滤器
        preFiltrationHandles.add(new RefundPolyErrorFilterHandle(context));
        // 退货退款单类别过滤器
        preFiltrationHandles.add(new RefundTypeFilterHandle(context));
        // 退货退款单归档过滤器
        preFiltrationHandles.add(new RefundHisFilterHandle(context));
        // 平台侧更新历史退货退款单过滤器
        preFiltrationHandles.add(new RefundPlatUpdateHisFilterHandle(context));
        // 平台级特殊处理
        RefundPlatSpecialFilterHandle refundPlatSpecialFilterHandle = new RefundPlatSpecialFilterHandle(context);
        refundPlatSpecialFilterHandle.setCoveringMethod(this::preFiltrationOrder);
        preFiltrationHandles.add(refundPlatSpecialFilterHandle);
        return preFiltrationHandles;
    }

    /**
     * 前置过滤订单
     *
     * @param sourceOrder 原始售后单数据
     * @return 结果
     */
    protected AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 供子类重写
        return AfterSaleHandleResult.success();
    }
    //endregion

    //region 转换订单级信息

    /**
     * 创建订单级数据转换插件
     */
    protected List<IOrderConvertHandle<BusinessGetRefundOrderResponseOrderItem>> createOrderConvertHandles() {
        List<IOrderConvertHandle<BusinessGetRefundOrderResponseOrderItem>> orderConvertHandles = new ArrayList<>();
        // 订单级基础数据转换插件
        orderConvertHandles.add(new RefundOrderCovertHandle(context));
        // 平台级特殊处理
        RefundPlatSpecialOrderCovertHandle refundPlatSpecialOrderCovertHandle = new RefundPlatSpecialOrderCovertHandle(context);
        refundPlatSpecialOrderCovertHandle.setCoveringMethod(this::orderConvert);
        orderConvertHandles.add(refundPlatSpecialOrderCovertHandle);

        return orderConvertHandles;
    }

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 供子类重写
        return AfterSaleHandleResult.success();
    }
    //endregion

    //region 转换商品级信息

    /**
     * 创建退货商品数据转换插件
     */
    protected List<IRefundGoodsConvertHandle<BusinessGetRefundOrderResponseOrderItem, BusinessGetRefundResponseRefundGoodInfo>> createReturnGoodsConvertsHandles() {
        List<IRefundGoodsConvertHandle<BusinessGetRefundOrderResponseOrderItem, BusinessGetRefundResponseRefundGoodInfo>> returnGoodsConverts = new ArrayList<>();
        // 退货商品基础数据转换插件
        returnGoodsConverts.add(new RefundGoodsCovertHandle(context));
        // 退货商品金额转换插件
        returnGoodsConverts.add(new RefundGoodsAmountCovertHandle(context));
        // 平台级特殊处理
        RefundGoodsPlatSpecialHandle refundGoodsPlatSpecialHandle = new RefundGoodsPlatSpecialHandle(context);
        refundGoodsPlatSpecialHandle.setCoveringHandle(this::refundGoodsConvert);
        returnGoodsConverts.add(refundGoodsPlatSpecialHandle);
        // 退货商品编码掩码处理
        returnGoodsConverts.add(new RefundGoodsJammingCodeHandle(context));
        // 售后商品商品匹配插件
        RefundGoodsMatchHandle refundGoodsMatchHandle = new RefundGoodsMatchHandle(context);
        refundGoodsMatchHandle.setCoveringHandle(refundGoodsMatch());
        returnGoodsConverts.add(refundGoodsMatchHandle);
        return returnGoodsConverts;
    }

    /**
     * 是否匹配原始单货品(通常情况下子类只需要考虑商品级)
     *
     * @param apiTradeGoods 原始单货品
     * @param refundOrder   售后单
     * @param refundGoods   售后单货品
     * @return 结果
     */
    protected boolean isMatchApiTradeGoods(ApiTradeGoodsDO apiTradeGoods, BusinessGetRefundOrderResponseOrderItem refundOrder, BusinessGetRefundResponseRefundGoodInfo refundGoods) {
        return AfterSaleGoodsMatchOperation.isRefundMatchApiTradeGoods(apiTradeGoods, refundOrder, refundGoods);
    }

    /**
     * 转换商品级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param sourceGoods 原始售后退货商品数据
     * @param targetOrder 目标售后单数据
     * @param refundGoods 目标售后退货商品数据
     * @return 结果
     */
    protected GoodsConvertHandleResult refundGoodsConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> sourceGoods, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods) {
        // 供子类重写
        return GoodsConvertHandleResult.success();
    }

    /**
     * 商品匹配特殊处理
     *
     * @return 结果
     */
    protected IRefundGoodsConvertHandle<BusinessGetRefundOrderResponseOrderItem, BusinessGetRefundResponseRefundGoodInfo> refundGoodsMatch() {
        return null;
    }

    //endregion

    //region 后置处理

    /**
     * 创建后置处理插件
     *
     * @return 后置处理插件列表
     */
    protected List<IPostProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem>> createPostProcessHandles() {
        List<IPostProcessOrderHandle<BusinessGetRefundOrderResponseOrderItem>> postProcesses = new ArrayList<>();
        // 退货退款单变化处理
        postProcesses.add(new RefundOrderChangeProcessHandle(context));
        // 售后商品删除处理
        postProcesses.add(new RefundGoodsDeleteProcessHandle(context));
        // 平台级特殊处理
        RefundPlatSpecialPostProcessHandle refundPlatSpecialOrderPostProcessHandle = new RefundPlatSpecialPostProcessHandle(context);
        refundPlatSpecialOrderPostProcessHandle.setCoveringMethod(this::postProcess);
        postProcesses.add(refundPlatSpecialOrderPostProcessHandle);

        return postProcesses;
    }

    /**
     * 后置处理订单信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    protected AfterSaleHandleResult postProcess(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        return AfterSaleHandleResult.success();
    }
    //endregion

    //endregion
}
