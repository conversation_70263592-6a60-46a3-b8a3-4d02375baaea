package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local;

import java.time.LocalDateTime;

/**
 * Api店铺授权数据
 *
 * <AUTHOR>
 * @date 2024-06-26 17:03
 */
public class ApiShopAuthDto {
    /**
     * 外部账号名
     */
    private String outAccount;

    /**
     * 店铺id
     */
    private Integer shopId;

    /**
     * 外部平台的店铺ID。
     */
    private String outShopId;

    /**
     * 客户店铺唯一标识(有用户的APIKey+(由本站AppKey+外部Token生成)。
     */
    private String token;

    /**
     * 代理商平台AppKey(仅用于授权型平台)，加密。
     */
    private String thirdAppKey;

    /**
     * 代理商平台密钥(仅用于授权型平台)，加密。
     */
    private String thirdAppSecret;

    /**
     * 会话编号(仅用于自用型平台) ，加密。
     */
    private String sessionKey;

    /**
     * SessionKey过期时间。
     */
    private LocalDateTime sessionKeyExpireTime;

    /**
     * SessionKey有效周期(单位：秒)。
     */
    private int sessionKeyTimeout;

    /**
     * 用于刷新失效的SessionKey  ，加密。
     */
    private String refreshToken;

    /**
     * 刷新失效的SessionKey过期时间。
     */
    private LocalDateTime refreshTokenExpireTime;

    /**
     * 授权持续时间(单位：秒)。
     */
    private int authorizationDuration;

    /**
     * 授权时间。
     */
    private LocalDateTime authorizationTime;

    /**
     * 订购到期时间
     */
    private LocalDateTime subscriptionExpireTime;

    /**
     * 自用型平台自定义参数(参数中文名称#@^@#参数英文名称#@^@#值，多个之间以*#$^$#*号分隔，如：应用key#@^@#appkey*#$^$#*token#@^@#token，值加密)。
     */
    private String customParms;

    /**
     * 其他参数。
     */
    private String otherParms;

    /**
     * 平台店铺ID
     */
    private String platShopId;

    /**
     * 平台店铺ID
     */
    private String platShopName;

    //region get/set

    public String getOutAccount() {
        return outAccount;
    }

    public void setOutAccount(String outAccount) {
        this.outAccount = outAccount;
    }

    public String getOutShopId() {
        return outShopId;
    }

    public void setOutShopId(String outShopId) {
        this.outShopId = outShopId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public String getThirdAppKey() {
        return thirdAppKey;
    }

    public void setThirdAppKey(String thirdAppKey) {
        this.thirdAppKey = thirdAppKey;
    }

    public String getThirdAppSecret() {
        return thirdAppSecret;
    }

    public void setThirdAppSecret(String thirdAppSecret) {
        this.thirdAppSecret = thirdAppSecret;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public LocalDateTime getSessionKeyExpireTime() {
        return sessionKeyExpireTime;
    }

    public void setSessionKeyExpireTime(LocalDateTime sessionKeyExpireTime) {
        this.sessionKeyExpireTime = sessionKeyExpireTime;
    }

    public int getSessionKeyTimeout() {
        return sessionKeyTimeout;
    }

    public void setSessionKeyTimeout(int sessionKeyTimeout) {
        this.sessionKeyTimeout = sessionKeyTimeout;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public LocalDateTime getRefreshTokenExpireTime() {
        return refreshTokenExpireTime;
    }

    public void setRefreshTokenExpireTime(LocalDateTime refreshTokenExpireTime) {
        this.refreshTokenExpireTime = refreshTokenExpireTime;
    }

    public int getAuthorizationDuration() {
        return authorizationDuration;
    }

    public void setAuthorizationDuration(int authorizationDuration) {
        this.authorizationDuration = authorizationDuration;
    }

    public LocalDateTime getAuthorizationTime() {
        return authorizationTime;
    }

    public void setAuthorizationTime(LocalDateTime authorizationTime) {
        this.authorizationTime = authorizationTime;
    }

    public LocalDateTime getSubscriptionExpireTime() {
        return subscriptionExpireTime;
    }

    public void setSubscriptionExpireTime(LocalDateTime subscriptionExpireTime) {
        this.subscriptionExpireTime = subscriptionExpireTime;
    }

    public String getCustomParms() {
        return customParms;
    }

    public void setCustomParms(String customParms) {
        this.customParms = customParms;
    }

    public String getOtherParms() {
        return otherParms;
    }

    public void setOtherParms(String otherParms) {
        this.otherParms = otherParms;
    }

    public String getPlatShopId() {
        return platShopId;
    }

    public void setPlatShopId(String platShopId) {
        this.platShopId = platShopId;
    }

    public String getPlatShopName() {
        return platShopName;
    }

    public void setPlatShopName(String platShopName) {
        this.platShopName = platShopName;
    }


    //endregion
}
