package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getExchange;

import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatResponseBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;

import java.util.ArrayList;
import java.util.List;

/**
 * 下载换货单返回结果
 * {@link PolyAPITypeEnum} BUSINESS_GETEXCHANGE
 *
 * <AUTHOR>
 * @date 2024/9/25 下午5:50
 */
public class PolyAPIBusinessGetExchangeOrderResponseBizData extends BasePlatResponseBizData {
    //region 构造器

    /**
     * 构造器
     */
    public PolyAPIBusinessGetExchangeOrderResponseBizData() {
        this.exchanges = new ArrayList<>();
    }

    //endregion

    /**
     * 是否有下一页
     */
    private boolean isHasNextPage;

    /**
     * 获取下一页订单所需的token值
     */
    private String nextToken;

    /**
     * 订单总数量
     */
    private int totalCount;

    /**
     * 订单集合
     */
    private List<BusinessGetExchangeOrderResponseOrderItem> exchanges;

    //region get/set
    public boolean isHasNextPage() {
        return isHasNextPage;
    }

    public void setHasNextPage(boolean hasNextPage) {
        isHasNextPage = hasNextPage;
    }

    public String getNextToken() {
        return nextToken;
    }

    public void setNextToken(String nextToken) {
        this.nextToken = nextToken;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public List<BusinessGetExchangeOrderResponseOrderItem> getExchanges() {
        return exchanges;
    }

    public void setExchanges(List<BusinessGetExchangeOrderResponseOrderItem> exchanges) {
        this.exchanges = exchanges;
    }
    //endregion
}
