package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.component;

import com.differ.wdgj.api.component.redis.converter.CacheKeyToCacheTypeConverter;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.DataCacheKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.DataCacheTypeEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 缓存键到缓存类型的转换器
 *
 * <AUTHOR>
 * @date 2020/12/14 14:52
 */
public class ApiCacheKeyToCacheTypeConverter implements CacheKeyToCacheTypeConverter {

    public ApiCacheKeyToCacheTypeConverter() {
        dataCacheKeys = DataCacheKeyEnum.class.getEnumConstants();
    }

    /**
     * 默认类型
     */
    private String defaultType = DataCacheTypeEnum.REALTIME_DATA.getCode();

    DataCacheKeyEnum[] dataCacheKeys;

    @Override
    public String defaultType() {
        return defaultType;
    }

    @Override
    public String toCacheType(String cacheKey) {
        if (StringUtils.isEmpty(cacheKey)) {
            return defaultType;
        }
        for (DataCacheKeyEnum dataCacheKey : dataCacheKeys) {
            // 根据前缀匹配
            if (this.startsWith(cacheKey, dataCacheKey.getPreFix())) {
                return dataCacheKey.getCacheType().getCode();
            }
        }
        return defaultType;
    }

    /**
     * 前缀可能会带{
     *
     * @param cacheKey
     * @param prefix
     * @return
     */
    private boolean startsWith(String cacheKey, String prefix) {
        return cacheKey.startsWith(prefix) || cacheKey.startsWith(prefix, 1);
    }

    /**
     * 后缀可能会带}
     *
     * @param cacheKey
     * @param suffix
     * @return
     */
    private boolean endsWith(String cacheKey, String suffix) {
        return cacheKey.endsWith(suffix) || cacheKey.endsWith(String.format("%s%s", suffix, "}"));
    }
}
