package com.differ.wdgj.api.user.biz.infrastructure.thread.pool.monitor;

import com.differ.wdgj.api.component.util.system.ThreadUtil;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.AlarmOperator;
import com.differ.wdgj.api.user.biz.infrastructure.alarm.notice.data.AlarmContent;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AlarmIntervalTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.CustomThread;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.ThreadPoolFactory;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 线程监控器
 *
 * <AUTHOR>
 * @date 2025/3/24 下午2:24
 */
public class ThreadPoolMonitor {
    //region 构造
    private ThreadPoolMonitor() {
    }
    //endregion

    /**
     * 记录线程池调试信息
     *
     * @param taskEnum 线程池
     */
    public static void debugThreadPoolInfo(TaskEnum taskEnum) {
        // 配置键控制
        if(!ConfigKeyUtils.isActionApi(ConfigKeyEnum.TASK_THREAD_TOOL_DEBUG, taskEnum.getNamePreFix())){
            return;
        }

        // 基础信息
        final String caption = "线程池调试信息";

        // 获取当前线程池信息
        Map<String, Object> poolInfo = ThreadPoolFactory.singleton().getPoolInfo(taskEnum);
        if(MapUtils.isNotEmpty(poolInfo)){
            String logContent = poolInfo.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining(", "));
            // 记录调试日志
            LogFactory.info(caption, logContent);
        }
        else{
            // 记录调试日志
            LogFactory.info(caption, "未查询到线程池相关信息");
        }
    }

    /**
     * 校验线程池中线程是否超时
     *
     * @param taskEnum 线程池
     */
    public static void checkThreadTimeout(TaskEnum taskEnum) {
        Set<Map.Entry<Runnable, CustomThread>> runningTasks = taskEnum.getRunningTasks();
        if (runningTasks.isEmpty()) {
            return;
        }
        //读取对应线程池配置的线程池任务执行超时时间
        String threadPoolName = taskEnum.getNamePreFix();
        int timeOutTime = Integer.parseInt(ConfigKeyUtils.getConfigBySeparatorDefault(ConfigKeyEnum.THREAD_TASK_TIMEOUT_TIME, threadPoolName, "0"));
        if (timeOutTime == 0) {
            return;
        }
        for (Map.Entry<Runnable, CustomThread> runningTask : runningTasks) {
            Runnable runnable = runningTask.getKey();
            CustomThread customThread = runningTask.getValue();
            if (runnable == null || customThread == null || customThread.getThread() == null) {
                continue;
            }
            Integer keepRunTime = customThread.getRunTime();
            if (runningTask.getValue().getRunTime() > timeOutTime) {
                String stackTraceStr = ThreadUtil.getStackTraceStr(customThread.getThread());
                //超时警报
                StringBuilder alarmText = new StringBuilder();
                alarmText.append(String.format("线程执行耗时(秒):%d，超过阈值:%d，任务:%s，存活:%s，状态:%s，堆栈:%s", keepRunTime, timeOutTime, runnable, customThread.getThread().isAlive(), customThread.getThread().getState(), stackTraceStr));
                if (Thread.State.TERMINATED.equals(customThread.getThread().getState())) {
                    boolean removed = taskEnum.getPoolExecutor().removeFinishTask(runnable);
                    alarmText.append(String.format("，已清理线程：%s", removed ? "成功" : "失败"));
                }

                AlarmContent content = AlarmContent.build(AlarmIntervalTypeEnum.THREAD_TASK_RUN_TIMEOUT, alarmText.toString());
                AlarmOperator.singleton().alarmInterval(AlarmIntervalTypeEnum.THREAD_TASK_RUN_TIMEOUT, customThread.getThread().getName(), content);
            }
        }
    }
}
