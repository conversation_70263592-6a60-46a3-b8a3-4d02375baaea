package com.differ.wdgj.api.user.biz.tasks.mq.multi.plugins.demo;

import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.MemberAble;

/**
 * 示例数据类型
 *
 * <AUTHOR>
 * @date 2024/3/14 14:25
 */
public class DemoData implements MemberAble {

    private String name;

    private String data;

    public DemoData(String name, String data) {
        this.name = name;
        this.data = data;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    /**
     * 取会员名
     *
     * @return
     */
    @Override
    public String getMember() {
        return "jackyun_dev";
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("DemoData{");
        sb.append("name='").append(name).append('\'');
        sb.append(", data='").append(data).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
