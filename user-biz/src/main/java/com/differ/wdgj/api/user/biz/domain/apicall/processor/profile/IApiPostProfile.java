package com.differ.wdgj.api.user.biz.domain.apicall.processor.profile;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatResponseBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallExtendedInfo;

import java.util.SortedMap;

/**
 * API调用请求接口
 *
 * <AUTHOR>
 * @date 2022-03-09 22:45
 */
public interface IApiPostProfile<RequestBizData extends BasePlatRequestBizData, ResponseBizData extends BasePlatResponseBizData> {

    /**
     * 创建请求报文
     *
     * @param context          上下文
     * @param requestBizData   请求业务数据
     * @param callExtendedInfo 请求扩展参数
     * @return 请求报文
     * @throws Exception 异常
     */
    SortedMap<String, String> createPostData(ApiCallContext context, RequestBizData requestBizData, ApiCallExtendedInfo callExtendedInfo) throws Exception;

    /**
     * 解析响应报文
     *
     * @param context         上下文
     * @param returnMsg       接口返回报文
     * @param responseBizType 响应业务数据类型
     * @return 响应
     */
    ApiCallResponse<ResponseBizData> resolveResponse(ApiCallContext context, String returnMsg, Class<? extends ResponseBizData> responseBizType);
}
