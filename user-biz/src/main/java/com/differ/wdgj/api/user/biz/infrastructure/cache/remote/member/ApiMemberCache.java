package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.member;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.AbstractHashReadOnlyCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.DataCacheKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.MemberUserDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.OuterApiEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 会员基础数据Redis缓存(对应表 member_users) - 只读
 *
 * <AUTHOR>
 * @date 2024/10/18 上午11:45
 */
public class ApiMemberCache extends AbstractHashReadOnlyCache<MemberUserDO> {
    //region 构造

    /**
     * 构造方法
     */
    protected ApiMemberCache() {
        super(String.format(DataCacheKeyEnum.DT_MEMBER_USER.getOriginalCode(), OuterApiEnum.WDGJ.getValue()));
    }
    //endregion

    //region 单例
    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static ApiMemberCache singleton() {
        return ApiMemberCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final ApiMemberCache instance;

        private SingletonEnum() {
            instance = new ApiMemberCache();
        }
    }
    //endregion

    //region 增强方法
    /**
     * 查询所有会员
     *
     * @return 会员列表
     */
    public List<MemberUserDO> getAll() {
        // 查询缓存
        List<MemberUserDO> memberList = new ArrayList<>();
        this.cacher.hashScanAll(cacheKey, 100, (key,value)->{
            MemberUserDO member = JsonUtils.deJson(value, MemberUserDO.class);
            memberList.add(member);
        });
        return memberList;
    }
    //endregion

    //region 实现基类方法
    @Override
    public Class<MemberUserDO> getValueClazz() {
        return MemberUserDO.class;
    }
    //endregion
}
