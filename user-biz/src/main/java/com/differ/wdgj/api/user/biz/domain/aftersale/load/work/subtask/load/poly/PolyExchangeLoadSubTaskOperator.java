package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.load.poly;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadOrdersDto;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadSubTask;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSalePageArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.LoadAfterSaleSubTaskResult;
import com.differ.wdgj.api.user.biz.domain.apicall.IApiCall;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getExchange.PolyAPIBusinessGetExchangeOrderRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getExchange.PolyAPIBusinessGetExchangeOrderResponseBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.plugins.aftersale.GetExchangeOrderApiCall;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.PageDynamicStatus;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 菠萝派换货单下载操作类
 *
 * <AUTHOR>
 * @date 2024/9/25 下午5:46
 */
public class PolyExchangeLoadSubTaskOperator extends BasePolyLoadSubTaskOperator<PolyAPIBusinessGetExchangeOrderRequestBizData, PolyAPIBusinessGetExchangeOrderResponseBizData> {
    //region 构造
    public PolyExchangeLoadSubTaskOperator(AfterSaleLoadTaskContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法

    /**
     * 获取api请求对象
     *
     * @return api请求对象
     */
    @Override
    protected IApiCall<PolyAPIBusinessGetExchangeOrderRequestBizData, PolyAPIBusinessGetExchangeOrderResponseBizData> getApiCall() {
        return new GetExchangeOrderApiCall();
    }

    /**
     * 构建菠萝派请求参数
     *
     * @param subTask 子任务
     * @return 菠萝派请求参数
     */
    @Override
    protected PolyAPIBusinessGetExchangeOrderRequestBizData buildRequest(AfterSaleLoadSubTask subTask) {
        // 基础信息
        PageDynamicStatus dynamicStatus = subTask.getDynamicStatus();
        // 构建参数
        PolyAPIBusinessGetExchangeOrderRequestBizData requestBizData = new PolyAPIBusinessGetExchangeOrderRequestBizData();
        // 时间
        requestBizData.setTimeType(dynamicStatus.getTimeType());
        requestBizData.setBeginTime(dynamicStatus.getLoadStartTime());
        requestBizData.setEndTime(dynamicStatus.getLoadEndTime());
        // 分页
        requestBizData.setNextToken(dynamicStatus.getNextToken());
        requestBizData.setPageIndex(dynamicStatus.getPageIndex());
        requestBizData.setPageSize(dynamicStatus.getPageSize());
        // 特殊业务参数
        requestBizData.setQueryType(subTask.getQueryType());
        requestBizData.setNotNeedDetail(subTask.isNotNeedDetail());
        // 平台业务参数
        requestBizData.setExchangeType(subTask.getAfterSaleType());
        requestBizData.setStatus(subTask.getStatus());
        requestBizData.setExchangeOrderNO(subTask.getAfterSaleNo());
        requestBizData.setPlatOrderNo(subTask.getPlatOrderNo());
        requestBizData.setBuyerNick(subTask.getBuyerNick());
        requestBizData.setShopId(subTask.getShopId());

        return requestBizData;
    }

    /**
     * 构建请求成功结果
     *
     * @param subTask      子任务信息
     * @param polyResponse 菠萝派返回
     * @return 请求成功结果
     */
    @Override
    protected LoadAfterSaleSubTaskResult buildRefundSuccessResult(AfterSaleLoadSubTask subTask, ApiCallResponse<PolyAPIBusinessGetExchangeOrderResponseBizData> polyResponse) {
        // 基础数据
        PolyAPIBusinessGetExchangeOrderResponseBizData exchangeResponse = polyResponse.getBizData();
        LoadAfterSaleSubTaskResult result = LoadAfterSaleSubTaskResult.successResult();
        // 基础结果信息
        result.setMessage(polyResponse.getMsg());
        result.setSubCode(polyResponse.getSubCode());
        result.setSubMessage(polyResponse.getSubMessage());
        result.setPolyApiRequestId(polyResponse.getPolyApiRequestId());
        // 下载成功的订单信息
        AfterSaleLoadOrdersDto loadOrders = new AfterSaleLoadOrdersDto();
        loadOrders.setPolyApiRequestId(polyResponse.getPolyApiRequestId());
        loadOrders.setExchanges(exchangeResponse.getExchanges());
        result.setLoadOrders(loadOrders);
        // 分页参数
        AfterSalePageArgs pageArgs = new AfterSalePageArgs();
        pageArgs.setTotalCount(exchangeResponse.getTotalCount());
        pageArgs.setHasNextPage(exchangeResponse.isHasNextPage());
        pageArgs.setNextToken(exchangeResponse.getNextToken());
        pageArgs.setLastSuccessDataTime(getLastSuccessDataTime(subTask, exchangeResponse));
        if (CollectionUtils.isNotEmpty(exchangeResponse.getExchanges())) {
            pageArgs.setDataSize(exchangeResponse.getExchanges().size());
        }
        result.setPageArgs(pageArgs);

        return result;
    }

    //endregion

    //region 私有方法
    /**
     * 获取"最后成功的数据时间"
     *
     * @param subTask          子任务信息
     * @param exchangeResponse 菠萝派返回
     * @return 最后成功的数据时间
     */
    private LocalDateTime getLastSuccessDataTime(AfterSaleLoadSubTask subTask, PolyAPIBusinessGetExchangeOrderResponseBizData exchangeResponse) {
        // 获取最小的"最后成功的数据时间"
        LocalDateTime lastSuccessExchangeTime = null;
        if (CollectionUtils.isNotEmpty(exchangeResponse.getExchanges())) {
            lastSuccessExchangeTime = exchangeResponse.getExchanges().stream().map(x -> x.getUpdateTime()).filter(Objects::nonNull).max(LocalDateTime::compareTo).orElse(subTask.getDynamicStatus().getLoadEndTime());
        }
        List<LocalDateTime> lastSuccessOrderTimeList = Arrays.asList(lastSuccessExchangeTime, subTask.getDynamicStatus().getLoadEndTime());
        return lastSuccessOrderTimeList.stream().filter(Objects::nonNull).min(LocalDateTime::compareTo).orElse(null);
    }
    //endregion
}
