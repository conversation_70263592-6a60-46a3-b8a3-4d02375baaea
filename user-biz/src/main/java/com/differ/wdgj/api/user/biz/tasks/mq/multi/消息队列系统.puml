'关系图使用要求：
'1.使用关联依赖聚合组合这4大关系时，一定要将代码体现标注上，看起来会更通俗易懂。
'2.继承类和接口的关系能区分更好，不区分也不必过于纠结（不想区分时可统一为继承类方式画图），因为plantUML中已有明显的接口和类的区别

'可访问性说明
'private -
'protect #
'package private ~
'public +
'其他参见plantuml类图说明：https://plantuml.com/zh/class-diagram

@startuml

interface SubQueue
abstract class AbstractMultiQueue

AbstractMultiQueue <|.. DemoMultiQueue  :继承

ApiMultiMQ <.. AbstractMultiQueue :依赖（代码体现：注解）

AbstractMultiQueue <--o MultiQueueContainer :聚合（代码体现：成员集合变量）

SubQueue <--* AbstractMultiQueue :组合

SubQueue <|.. JmqSubQueue  :继承
SubQueue <|.. DbSubQueue  :继承



@enduml