package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;

/**
 * 售后单处理插件-后置处理
 *
 * <AUTHOR>
 * @date 2024/7/12 下午7:05
 */
public interface IPostProcessOrderHandle<O> {
    /**
     * 售后单后置处理
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    AfterSaleHandleResult process(SourceAfterSaleOrderItem<O> sourceOrder, TargetCovertOrderItem targetOrder);
}
