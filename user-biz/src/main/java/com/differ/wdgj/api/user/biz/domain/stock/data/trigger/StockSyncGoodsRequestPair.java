package com.differ.wdgj.api.user.biz.domain.stock.data.trigger;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockRequestGoodInfo;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;

/**
 * 库存同步商品级请求对
 *
 * <AUTHOR>
 * @date 2025/5/14 19:06
 */
public class StockSyncGoodsRequestPair {
    /**
     * 匹配基础数据
     */
    private final GoodsMatchEnhance matchEnhance;

    /**
     * 商品库存信息
     */
    private final GoodsStockCalculationResult goodsStockInfo;

    /**
     * 库存同步请求信息
     */
    private final BusinessBatchSyncStockRequestGoodInfo goodsInfo;

    //region 构造
    public StockSyncGoodsRequestPair(GoodsMatchEnhance matchEnhance, GoodsStockCalculationResult goodsStockInfo, BusinessBatchSyncStockRequestGoodInfo goodsInfo) {
        this.matchEnhance = matchEnhance;
        this.goodsStockInfo = goodsStockInfo;
        this.goodsInfo = goodsInfo;
    }
    //endregion

    //region get/set
    public GoodsMatchEnhance getMatchEnhance() {
        return matchEnhance;
    }

    public BusinessBatchSyncStockRequestGoodInfo getGoodsInfo() {
        return goodsInfo;
    }

    public GoodsStockCalculationResult getGoodsStockInfo() {
        return goodsStockInfo;
    }
//endregion
}
