package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.shop;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.AbstractHashEnhanceCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.DataCacheKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.ApiAllShopDto;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.CfgShopListExtMapper;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 获取会员级所有店铺-Redis缓存
 *
 * <AUTHOR>
 * @date 2024/9/18 上午11:27
 */
public class ApiAllShopCache extends AbstractHashEnhanceCache<ApiAllShopDto> {
    //region 构造
    /**
     * 构造方法
     */
    public ApiAllShopCache() {
        super(DataCacheKeyEnum.USER_API_ALL_SHOP.getCode());
        // 过期时间 10 ~ 15 分钟随机
        this.minCacheKeyTimeOut = 600;
        this.maxCacheKeyTimeOut = 900;
    }
    //endregion

    //region 单例
    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static ApiAllShopCache singleton() {
        return ApiAllShopCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final ApiAllShopCache instance;

        private SingletonEnum() {
            instance = new ApiAllShopCache();
        }
    }
    //endregion

    //region 实现基类方法

    /**
     * 加载数据
     *
     * @param memberName 会员名
     * @return 店铺信息
     */
    @Override
    protected ApiAllShopDto loadSource(String memberName) {
        CfgShopListExtMapper shopListExtMapper = BeanContextUtil.getBean(CfgShopListExtMapper.class);
        ApiAllShopDto apiAllShopDto = new ApiAllShopDto();
        DBSwitchUtil.doDBWithUser(memberName,
                () ->{
                    List<ApiAllShopDto.ShopInfo> shopInfoList = shopListExtMapper.getAllShopDto();
                    apiAllShopDto.setShopInfoList(shopInfoList);
                });
        return apiAllShopDto;
    }

    @Override
    protected Map<String, ApiAllShopDto> loadSource(List<String> hashFields) {
        return Collections.emptyMap();
    }

    @Override
    public Class<ApiAllShopDto> getValueClazz() {
        return ApiAllShopDto.class;
    }
    //endregion

    //region 公共方法
    /**
     * 移除缓存-单个
     *
     * @param memberName  会员名
     */
    public void clearCache(String memberName) {
        // 校验入参
        if (StringUtils.isEmpty(memberName)) {
            return;
        }

        // 移除缓存
        super.removeCache(cacheKey, memberName);
    }
    //endregion
}
