package com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.impl;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.aftersale.common.subdomain.IAfterSaleInfoOperationService;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnInfoDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiReturnInfoMapper;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

/**
 * 售后单信息数据操作
 *
 * <AUTHOR>
 * @date 2024/12/10 上午11:39
 */
public class AfterSaleInfoOperationService implements IAfterSaleInfoOperationService {
    //region 常量
    /**
     * 密文数据仓储
     */
    private final ApiReturnInfoMapper apiReturnInfoMapper = BeanContextUtil.getBean(ApiReturnInfoMapper.class);
    //endregion

    /**
     * 批量查询售后单信息
     *
     * @param memberName   会员名
     * @param afterSaleIds 售后Id列表
     * @return 售后单列表
     */
    @Override
    public List<ApiReturnInfoDO> queryInfo(String memberName, List<Integer> afterSaleIds) {
        // 拆分
        List<List<Integer>> subQueryIdLists = Lists.partition(afterSaleIds, 200);

        // 批量操作
        List<ApiReturnInfoDO> apiReturnInfos = new ArrayList<>();
        for (List<Integer> subQueryIdList : subQueryIdLists) {
            DBSwitchUtil.doDBWithUser(memberName, () -> apiReturnInfos.addAll(apiReturnInfoMapper.selectByBillIds(subQueryIdList)));
        }

        return apiReturnInfos;
    }

    /**
     * 批量保存（新增/更新）售后单密文信息
     * <p>依赖主键区分新增/更新</p>
     *
     * @param memberName    会员名
     * @param returnInfos 售后单信息列表
     */
    @Override
    public void batchSaveInfo(String memberName, List<ApiReturnInfoDO> returnInfos) {
        // 区分新增/更新
        List<ApiReturnInfoDO> needInsertList = new ArrayList<>();
        List<ApiReturnInfoDO> needUpdateList = new ArrayList<>();
        returnInfos.forEach(returnInfo -> {
            if (returnInfo.getRecId() > 0) {
                needUpdateList.add(returnInfo);
            } else {
                needInsertList.add(returnInfo);
            }
        });

        // 拆分
        List<List<ApiReturnInfoDO>> subInsertLists = Lists.partition(needInsertList, 100);
        List<List<ApiReturnInfoDO>> subUpdateLists = Lists.partition(needUpdateList, 100);

        // 批量操作
        DBSwitchUtil.doDBWithUser(memberName, () -> {
            for (List<ApiReturnInfoDO> subInsertList : subInsertLists) {
                apiReturnInfoMapper.batchInsert(subInsertList);
            }
            for (List<ApiReturnInfoDO> subUpdateList : subUpdateLists) {
                apiReturnInfoMapper.batchUpdate(subUpdateList);
            }
        });
    }
}
