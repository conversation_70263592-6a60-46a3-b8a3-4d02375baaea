package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiPlatSysHisDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.BasicOperateMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库存同步日志表
 *
 * <AUTHOR>
 * @date 2024-03-11 11:47
 */
public interface ApiPlatSysHisMapper extends BasicOperateMapper<ApiPlatSysHisDO> {
    // region 新增

    /**
     * 批量新增库存同步日志
     *
     * @param apiPlatSysHisDos 存同步日志集合
     * @return 返回影响行数
     */
    int batchAddApiPlatSysHis(@Param("apiPlatSysHisDos") List<ApiPlatSysHisDO> apiPlatSysHisDos);

    // endregion

}
