package com.differ.wdgj.api.user.biz.domain.aftersale.load.utils;

import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.auto.AutoJobMemberLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.AutoJobTypeEnum;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 自动下载售后单工具类
 *
 * <AUTHOR>
 * @date 2024/11/27 下午7:16
 */
public class LoadAfterSaleJobUtils {
    //region 构造
    private LoadAfterSaleJobUtils() {
    }
    //endregion

    /**
     * 获取自动下载售后单会员
     *
     * @param autoJobType 自动任务类型
     * @return 结果
     */
    public static List<String> getAutoMembers(AutoJobTypeEnum autoJobType) {
        // 开启自动的会员
        Set<String> autoMembers = AutoJobMemberLocalCache.singleton().getCacheThenSource(autoJobType);

        // 返回结果
        return new ArrayList<>(autoMembers);
    }

    /**
     * 获取自动下载售后单会员
     *
     * @param autoJobType 自动任务类型
     * @param enableFunc  是否有效（true：有效，false：无效）
     * @return 结果
     */
    public static List<String> getAutoMembers(AutoJobTypeEnum autoJobType, Function<String, Boolean> enableFunc) {

        // 获取全部自动会员
        List<String> autoMembers = getAutoMembers(autoJobType);
        if (CollectionUtils.isEmpty(autoMembers)) {
            return autoMembers;
        }

        // 执行过滤
        return autoMembers.stream().filter(enableFunc::apply).collect(Collectors.toList());
    }
}
