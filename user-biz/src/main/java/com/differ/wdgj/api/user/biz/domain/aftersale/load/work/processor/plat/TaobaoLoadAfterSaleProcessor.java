package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadSubTask;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.LoadAfterSaleWorkResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor.BaseLoadAfterSaleProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;

/**
 * 淘宝 - 售后单下载
 *
 * <AUTHOR>
 * @date 2025/4/10 下午7:23
 */
public class TaobaoLoadAfterSaleProcessor extends BaseLoadAfterSaleProcessor {
    //region 构造
    public TaobaoLoadAfterSaleProcessor(AfterSaleLoadTaskContext context) {
        super(context);
    }
    //endregion

    /**
     * 子任务平台级特殊处理
     *
     * @param workData 工作任务数据
     * @param subTask  子任务
     * @return 特殊处理后子任务
     */
    @Override
    protected LoadAfterSaleWorkResult subTaskCreatePlatProcess(WorkData<AfterSaleLoadArgs> workData, AfterSaleLoadSubTask subTask) {
        // 基础数据
        ApiShopBaseDto apiShopBase = context.getApiShopBaseInfo();
        // 淘宝昵称
        subTask.setBuyerNick(apiShopBase.getPlatShopName());
        return LoadAfterSaleWorkResult.onSuccess();
    }
}
