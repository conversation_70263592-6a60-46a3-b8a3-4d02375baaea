package com.differ.wdgj.api.user.biz.infrastructure.data.enums;

import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 系统日志应用类别
 *
 * <AUTHOR>
 * @date 2024-04-09 11:57
 */
public enum SystemLogApplicationEnum implements ValueEnum {
    /**
     * 全局
     */
    Global(0, "全局"),

    /**
     * 任务管理器
     */
    TaskManager(1, "任务管理器"),

    /**
     * 队列任务
     */
    QueueTask(2, "队列任务"),

    /**
     * 数据缓存
     */
    YunAPITaskManager(3, "数据缓存"),

    /**
     * 数据缓存
     */
    DataProxy(4, "数据缓存"),

    /**
     * MQ管理器
     */
    MQManager(5, "MQ管理器"),

    /**
     * 客户平台服务器
     */
    OpenPlat(10, "客户平台服务器"),

    /**
     * 开放接口API
     */
    OpenAPI(11, "开放接口API"),

    /**
     * 后台管理
     */
    Admin(12, "后台管理"),

    /**
     * MVCAPI
     */
    MVCAPI(13, "MVCAPI"),

    /**
     * 菠萝派
     */
    PolyAPI(14, "菠萝派"),

    /**
     * 网店管家代理
     */
    WDGJProxyService(15, "网店管家代理"),

    /**
     * 新门店代理服务
     */
    NewMDProxyService(16, "新门店代理"),

    /**
     * WCFAPI
     */
    WCFAPI(17, "WCFAPI"),

    /**
     * 云平台
     */
    YunPlat(18, "YunPlat"),

    /**
     * 守护
     */
    DaemonServer(19, "守护"),

    ;

    /**
     * 枚举值
     */
    private Integer value;
    /**
     * 展示信息
     */
    private String displayName;

    /**
     * 构造
     * @param value 枚举值
     * @param displayName 展示信息
     */
    SystemLogApplicationEnum(Integer value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    /**
     * 获取 展示信息
     *
     * @return 展示信息
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取枚举值
     *
     * @return 枚举值
     */
    @Override
    public Integer getValue() {
        return value;
    }
}
