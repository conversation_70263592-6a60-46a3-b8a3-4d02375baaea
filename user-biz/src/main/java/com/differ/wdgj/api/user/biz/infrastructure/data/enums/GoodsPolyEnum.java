package com.differ.wdgj.api.user.biz.infrastructure.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;


/**
 * 管家商品的平台类型枚举
 * 新平台不再新增，值使用{@link PolyPlatEnum} 平台值platValue
 *
 * <AUTHOR>
 * @date 2024-03-05 16:34
 */
public enum GoodsPolyEnum  implements ValueEnum {

    // region 枚举
    APISys_TBFX(0, PolyPlatEnum.BUSINESS_Taobao),

    APISys_TB(1, PolyPlatEnum.BUSINESS_Taobao),

    APISys_JD(3, PolyPlatEnum.BUSINESS_JD),

    APISys_DD(4, PolyPlatEnum.BUSINESS_DangDang),

    APISys_YHD(5, PolyPlatEnum.BUSINESS_YiHaoDian),

    APISys_V(8, PolyPlatEnum.BUSINESS_Vancl),

    APISys_CXT(9, PolyPlatEnum.BUSINESS_Chengxintong),

    APISys_SMT(10, PolyPlatEnum.BUSINESS_AliExpress),

    APISys_MLS(11, PolyPlatEnum.BUSINESS_Meilishuo),

    APISys_CCB(12, PolyPlatEnum.BUSINESS_ShanRong),

    APISys_SNYG(13, PolyPlatEnum.BUSINESS_Suning),

    APISys_Spider(14, PolyPlatEnum.BUSINESS_OpenMall),

    APISys_KDT(15, PolyPlatEnum.BUSINESS_Youzan2),

    APISys_YGW(16, PolyPlatEnum.BUSINESS_Yougou),

    APISys_BBW(17, PolyPlatEnum.BUSINESS_Beibei),

    APISys_PXW(18, PolyPlatEnum.BUSINESS_Paixie),

    APISys_MGJ(20, PolyPlatEnum.BUSINESS_Mogujie),

    APISys_ICBC(21, PolyPlatEnum.BUSINESS_Rongyigou),

    APISys_CYZS(22, PolyPlatEnum.BUSINESS_ChuanYiZhuShou),

    APISys_eBay(23, PolyPlatEnum.BUSINESS_Ebay),

    APISys_Wish(24, PolyPlatEnum.BUSINESS_Wish),

    APISys_Miya(25, PolyPlatEnum.BUSINESS_Miya),

    APISys_FNW(26, PolyPlatEnum.BUSINESS_Feiniu),

    APISys_MD(27, PolyPlatEnum.BUSINESS_Mengdian),

    APISys_ZHE800(28, PolyPlatEnum.BUSINESS_Zhe800),

    APISys_BDMall(29, PolyPlatEnum.BUSINESS_BaiduMall),

    APISys_WMWP(30, PolyPlatEnum.BUSINESS_Weimob),

    APISys_JMYP(31, PolyPlatEnum.BUSINESS_Jumei),

    APISys_yahoo(32, PolyPlatEnum.BUSINESS_Yahoo),

    APISys_XiuPin(33, PolyPlatEnum.BUSINESS_XiuPin),

    APISys_YWK(34, PolyPlatEnum.BUSINESS_Youwuku),

    APISys_ChuCJ(35, PolyPlatEnum.BUSINESS_ChuChuJie),

    APISys_XCF(36, PolyPlatEnum.BUSINESS_Xiachufang),

    APISys_qm(37, PolyPlatEnum.BUSINESS_Qianmi),

    APISys_MXYC(38, PolyPlatEnum.BUSINESS_Hichao),

    APISys_RRD(39, PolyPlatEnum.BUSINESS_Weiba66),

    APISys_JMHT(40, PolyPlatEnum.BUSINESS_Jumeihaitao),

    APISys_TSH(41, PolyPlatEnum.BUSINESS_Teshehui),

    APISys_JPW(42, PolyPlatEnum.BUSINESS_Juanpi),

    APISys_ShopEx(43, PolyPlatEnum.BUSINESS_ShopEx),

    APISys_QBB(44, PolyPlatEnum.BUSINESS_Qinbaobao),

    APISys_PDD(45, PolyPlatEnum.BUSINESS_Yangkeduo),

    APISys_YDH(46, PolyPlatEnum.BUSINESS_Yidh),

    APISys_ChuCJPT(47, PolyPlatEnum.BUSINESS_ChuChuJiePinTuan),

    APISys_TH(48, PolyPlatEnum.BUSINESS_Tianhong),

    APISys_KB(49, PolyPlatEnum.BUSINESS_Kuba),

    APISys_weidian(50, PolyPlatEnum.BUSINESS_WeiDian),

    APISys_YLW(51, PolyPlatEnum.BUSINESS_Youle),

    APISys_KaoLa(52, PolyPlatEnum.BUSINESS_KaoLa),

    APISys_WSH(53, PolyPlatEnum.BUSINESS_Weishanghu),

    APISys_XHS(54, PolyPlatEnum.BUSINESS_XiaoHS),

    APISys_DHW(55, PolyPlatEnum.BUSINESS_Dunhuang),

    APISys_HLM(57, PolyPlatEnum.BUSINESS_Haolemai),

    APISys_DaLing(58, PolyPlatEnum.BUSINESS_Daling),

    APISys_FQL(59, PolyPlatEnum.BUSINESS_Fenqile),

    APISys_Polymall(60, PolyPlatEnum.BUSINESS_PolyMall),

    APISys_HZW(61, PolyPlatEnum.BUSINESS_HaiZiWang),

    APISys_HiGo(62, PolyPlatEnum.BUSINESS_HiGo),

    APISys_FLW(63, PolyPlatEnum.BUSINESS_FanLi),

    APISys_XMSC(64, PolyPlatEnum.BUSINESS_Xiaomi),

    APISys_MeiTun(65, PolyPlatEnum.BUSINESS_Meitun),

    APISys_WPH(67, PolyPlatEnum.BUSINESS_Vipshop),

    APISys_ChuCT(68, PolyPlatEnum.BUSINESS_ChuChuTong),

    APISys_SC(69, PolyPlatEnum.BUSINESS_Secoo),

    APISys_Lazada(70, PolyPlatEnum.BUSINESS_LaZaDa),

    APISys_SNZY(71, PolyPlatEnum.BUSINESS_Suningself),

    APISys_SNTM(72, PolyPlatEnum.BUSINESS_SuningTM),

    APISys_YMT(73, PolyPlatEnum.BUSINESS_Ymatou),

    APISys_CZC(74, PolyPlatEnum.BUSINESS_ChuiZhiCai),

    APISys_JHSC(75, PolyPlatEnum.BUSINESS_JiHe),

    APISys_DHB(76, PolyPlatEnum.BUSINESS_DingHuoBao),

    APISys_DVD(77, PolyPlatEnum.BUSINESS_Daweidian),

    APISys_Wacom(78, PolyPlatEnum.BUSINESS_Wacom),

    APISys_Amazon(101, PolyPlatEnum.BUSINESS_AmazonChina),

    APISys_AmazonG(102, PolyPlatEnum.BUSINESS_AmazonChina),

    APISys_Eyee(112, PolyPlatEnum.BUSINESS_Eyee),

    APISys_JDO2O(113, PolyPlatEnum.BUSINESS_JDO2O),

    APISys_SLDL(115, PolyPlatEnum.BUSINESS_ShunLianDongLi),

    APISys_WMWSC(116, PolyPlatEnum.BUSINESS_WMWeiShangCheng),

    APISys_MGJXD(117, PolyPlatEnum.BUSINESS_MGJXD),

    APISys_ZhiYu(118, PolyPlatEnum.BUSINESS_ZhiYu),

    APISys_Shopee(119, PolyPlatEnum.BUSINESS_Shopee),

    APISys_FXG(120, PolyPlatEnum.BUSINESS_FangXinGou),

    APISys_WPHMP(122, PolyPlatEnum.BUSINESS_WPHMP),

    APISys_HRT(123, PolyPlatEnum.BUSINESS_HRT),

    APISys_YiQianBao(124, PolyPlatEnum.BUSINESS_YiQianBao),

    APISys_YunJiPin(125, PolyPlatEnum.BUSINESS_YunJiPin),

    APISys_GGJ(126, PolyPlatEnum.BUSINESS_Gegejia),

    APISys_XDSC(127, PolyPlatEnum.BUSINESS_XDSC),

    APISys_IQY(128, PolyPlatEnum.BUSINESS_IQY),

    APISys_HPK(132, PolyPlatEnum.BUSINESS_HPK),

    APISys_YaoFangWang(146, PolyPlatEnum.BUSINESS_YaoFangWang),

    APISys_HSQ(164, PolyPlatEnum.BUSINESS_HaoShiQi),

    APISys_MJYP(191, PolyPlatEnum.BUSINESS_MJYP),

    APISys_GMB2B(208, PolyPlatEnum.BUSINESS_GMB2B),

    APISys_MengTui(217, PolyPlatEnum.BUSINESS_Mengtui),

    APISys_AKC(224, PolyPlatEnum.BUSINESS_AiKuCun),

    APISys_MDB(225, PolyPlatEnum.BUSINESS_MiDianBao),

    APISys_LB(226, PolyPlatEnum.BUSINESS_LuBan),

    APISys_XHY(229, PolyPlatEnum.BUSINESS_XiaoHeiYu),

    APISys_JDBuy(230, PolyPlatEnum.BUSINESS_JDBuy),

    APISys_MRYT(232, PolyPlatEnum.BUSINESS_MeiRiYiTao),

    APISys_GGJLV(233, PolyPlatEnum.BUSINESS_GegejiaLvyue),

    APISys_HYK(235, PolyPlatEnum.BUSINESS_HaoYiKu),

    APISys_BK(236, PolyPlatEnum.BUSINESS_BeiKe),

    APISys_MRXG(237, PolyPlatEnum.BUSINESS_MRXG),

    APISys_CaiDuoDuo(238, PolyPlatEnum.BUSINESS_CaiDuoDuo),

    APISys_HZWYJDF(240, PolyPlatEnum.BUSINESS_HZWYJDF),

    APISys_MC(241, PolyPlatEnum.BUSINESS_MC),

    APISys_YunjiPOP(242, PolyPlatEnum.BUSINESS_YunjiPOP),

    APISys_WMZHLS(243, PolyPlatEnum.BUSINESS_WMZhiHuiLingShou),

    APISys_MeiLiHui(245, PolyPlatEnum.BUSINESS_MeiLiHui),

    APISys_BangBangTang(248, PolyPlatEnum.BUSINESS_BangBangTang),

    APISys_YueYangDianPu(249, PolyPlatEnum.BUSINESS_YueYangDianPu),

    APISys_Joom(250, PolyPlatEnum.BUSINESS_Joom),

    APISys_LvMiXinLingShou(251, PolyPlatEnum.BUSINESS_LvMiXinLingShou),

    APISys_LingGou(252, PolyPlatEnum.BUSINESS_LingGou),

    APISys_YouLiang(253, PolyPlatEnum.BUSINESS_YouLiang),

    APISys_YangCong(254, PolyPlatEnum.BUSINESS_YangCong),

    APISys_YaoShiBang(256, PolyPlatEnum.BUSINESS_YaoShiBang),

    APISys_MTWM(258, PolyPlatEnum.BUSINESS_MTWM),

    APISys_SFYouXuan(259, PolyPlatEnum.BUSINESS_SFYouXuan),

    APISys_ZhenPinWang(260, PolyPlatEnum.BUSINESS_ZhenPinWang),

    APISys_YouZanLingShou(261, PolyPlatEnum.BUSINESS_YouZanLingShou),

    APISys_GongZhuGou(264, PolyPlatEnum.BUSINESS_GongZhuGou),

    APISys_RenRenDianWeiShang(266, PolyPlatEnum.BUSINESS_RenRenDianWeiShang),

    APISys_WuXiang(267, PolyPlatEnum.BUSINESS_WuXiang),

    APISys_Shopify(269, PolyPlatEnum.BUSINESS_Shopify),

    APISys_Tokopedia(272, PolyPlatEnum.BUSINESS_Tokopedia),

    APISys_PingAnBank(273, PolyPlatEnum.BUSINESS_PingAnBank),

    APISys_YunCang(275, PolyPlatEnum.BUSINESS_YunCang),

    APISys_JDIndonesia(276, PolyPlatEnum.BUSINESS_JDIndonesia),

    APISys_Shopyy(278, PolyPlatEnum.BUSINESS_Shopyy),

    APISys_ECStoreRetail(279, PolyPlatEnum.BUSINESS_ECStoreRetail),

    APISys_Nome(280, PolyPlatEnum.BUSINESS_Nome),

    APISys_GuanMai(281, PolyPlatEnum.BUSINESS_GuanMai),

    APISys_WedePot(283, PolyPlatEnum.BUSINESS_WedePot),

    APISys_BaiBao(285, PolyPlatEnum.BUSINESS_BaiBao),

    APISys_YiKe(286, PolyPlatEnum.BUSINESS_YiKe),

    APISys_AlibabaGJZ(288, PolyPlatEnum.BUSINESS_AlibabaGJZ),

    APISys_AliDaYaoFang(289, PolyPlatEnum.BUSINESS_AliDaYaoFang),

    APISys_IntraMirror(290, PolyPlatEnum.BUSINESS_IntraMirror),

    APISys_KongFuZi(291, PolyPlatEnum.BUSINESS_KongFuZi),

    APISys_DouGuoMeiShi(292, PolyPlatEnum.BUSINESS_DouGuoMeiShi),

    APISys_YunShangHuLian(293, PolyPlatEnum.BUSINESS_YunShangHuLian),

    APISys_Cdiscount(295, PolyPlatEnum.BUSINESS_Cdiscount),

    APISys_JuTa(296, PolyPlatEnum.BUSINESS_JuTa),

    APISys_HuaWeiMall(1001, PolyPlatEnum.BUSINESS_HuaWeiMall),

    APISys_MoKuai(1002, PolyPlatEnum.BUSINESS_MoKuai),

    APISys_WoMai(1005, PolyPlatEnum.BUSINESS_WoMai),

    APISys_ShunFengDaDangJia(1006, PolyPlatEnum.BUSINESS_ShunFengDaDangJia),

    APISys_XingYun(1007, PolyPlatEnum.BUSINESS_XingYun),

    APISys_KuaiShouShop(1008, PolyPlatEnum.BUSINESS_KuaiShouShop),

    APISys_TuHuYangChe(1009, PolyPlatEnum.BUSINESS_TuHuYangChe),

    APISys_EzBuy(1010, PolyPlatEnum.BUSINESS_EzBuy),

    APISys_SeeXiaoDianPu(1011, PolyPlatEnum.BUSINESS_SeeXiaoDianPu),

    APISys_LaMaPlan(1012, PolyPlatEnum.BUSINESS_LaMaPlan),

    APISys_YouHaoSuDa(1013, PolyPlatEnum.BUSINESS_YouHaoSuDa),

    APISys_HouNiao(1014, PolyPlatEnum.BUSINESS_HouNiao),

    APISys_LianHuaJingnXuan(1015, PolyPlatEnum.BUSINESS_LianHuaJingnXuan),

    APISys_JDThailand(1016, PolyPlatEnum.BUSINESS_JDThailand),

    APISys_XiTu(1017, PolyPlatEnum.BUSINESS_XiTu),

    APISys_TaiNi(1018, PolyPlatEnum.BUSINESS_TaiNi),

    APISys_TuanGouDaRen(1020, PolyPlatEnum.BUSINESS_TuanGouDaRen),

    APISys_BuLuoGuanJia(1021, PolyPlatEnum.BUSINESS_BuLuoGuanJia),

    APISys_BiYao(1023, PolyPlatEnum.BUSINESS_BiYao),

    APISys_UeeShop(1024, PolyPlatEnum.BUSINESS_UeeShop),

    APISys_TengXunGuangGao(1025, PolyPlatEnum.BUSINESS_TengXunGuangGao),

    APISys_JinNiu(1026, PolyPlatEnum.BUSINESS_JinNiu),

    APISys_XinHuaShuDian(1029, PolyPlatEnum.BUSINESS_XinHuaShuDian),

    APISys_MeiRiYouXian(1030, PolyPlatEnum.BUSINESS_MeiRiYouXian),

    APISys_TianMaoHaiWaiCangZhiGou(1031, PolyPlatEnum.BUSINESS_TianMaoHaiWaiCangZhiGou),

    APISys_1688C2M(1034, PolyPlatEnum.BUSINESS_AliBaBaC2M),

    APISys_SHEIN(1035, PolyPlatEnum.BUSINESS_SHEIN),

    APISys_DianJiang(1036, PolyPlatEnum.BUSINESS_DianJiang),

    APISys_EBaiLingShou(1038, PolyPlatEnum.BUSINESS_EBaiLingShou),

    APISys_QiShiShangCheng(1040, PolyPlatEnum.BUSINESS_QiShiShangCheng),

    APISys_NianGaoMaMa(1041, PolyPlatEnum.BUSINESS_NianGaoMaMa),

    APISys_FaWang(1042, PolyPlatEnum.BUSINESS_FaWang),

    APISys_AiKuCunHaiTao(1043, PolyPlatEnum.BUSINESS_AiKuCunHaiTao),

    APISys_KaoLaZiYing(1045, PolyPlatEnum.BUSINESS_KaoLaZiYing),

    APISys_TmallGJZY(1047, PolyPlatEnum.BUSINESS_TmallGJZY),

    APISys_YingTu(1049, PolyPlatEnum.BUSINESS_YingTu),

    APISys_MoDian(1050, PolyPlatEnum.BUSINESS_MoDian),

    APISys_BaiLian(1051, PolyPlatEnum.BUSINESS_BaiLian),

    APISys_AoMaiJia(1054, PolyPlatEnum.BUSINESS_AoMaiJia),

    APISys_XiaoHongShuZiYing(1055, PolyPlatEnum.BUSINESS_XiaoHongShuZiYing),

    APISys_KuaiTuanTuan(1056, PolyPlatEnum.BUSINESS_KuaiTuanTuan),

    APISys_QuChenShi(1058, PolyPlatEnum.BUSINESS_QuChenShi),

    APISys_XiaoEPinPin(1059, PolyPlatEnum.BUSINESS_XiaoEPinPin),

    APISys_TaoBaoTW(1061, PolyPlatEnum.BUSINESS_TaoBaoTW),

    APISys_WuBaGe(1062, PolyPlatEnum.BUSINESS_WuBaGe),

    APISys_JinRiBaoTuan(1064, PolyPlatEnum.BUSINESS_JinRiBaoTuan),

    APISys_WeiXinXiaoShangDian(1065, PolyPlatEnum.BUSINESS_WeiXinXiaoShangDian),

    APISys_MeiTuanLingShou(1067, PolyPlatEnum.BUSINESS_MeiTuanLingShou),

    APISys_LingShouTong(1068, PolyPlatEnum.BUSINESS_LingShouTong),

    APISys_QunJieLong(1069, PolyPlatEnum.BUSINESS_QunJieLong),

    APISys_LvSenShangCheng(1070, PolyPlatEnum.BUSINESS_LvSenShangCheng),

    APISys_XiaoYaTong(1071, PolyPlatEnum.BUSINESS_XiaoYaTong),

    APISys_DeWu(1072, PolyPlatEnum.BUSINESS_DeWu),

    APISys_KaMeiLa(1073, PolyPlatEnum.BUSINESS_KaMeiLa),

    APISys_HuiMaiMai(1074, PolyPlatEnum.BUSINESS_HuiMaiMai),

    APISys_MengXiangShop(1075, PolyPlatEnum.BUSINESS_MengXiangShop),

    APISys_XiTuan(1076, PolyPlatEnum.BUSINESS_XiTuan),

    APISys_DuXiaoDianPass(1077, PolyPlatEnum.BUSINESS_DuXiaoDianPass),

    APISys_XiaoMang(1078, PolyPlatEnum.BUSINESS_XiaoMang),

    APISys_LianTuan(1079, PolyPlatEnum.BUSINESS_LianTuan),

    APISys_MeiTuanTuanHaoHuo(1080, PolyPlatEnum.BUSINESS_MeiTuanTuanHaoHuo),

    APISys_JinBiLianMeng(1082, PolyPlatEnum.BUSINESS_JinBiLianMeng),

    APISys_YuOu(1083, PolyPlatEnum.BUSINESS_YuOu),

    APISys_ZhongGuoYDJFSC(1084, PolyPlatEnum.BUSINESS_ZhongGuoYDJFSC),

    APISys_AliJianKangDaYaoFang(1086, PolyPlatEnum.BUSINESS_AliJianKangDaYaoFang),

    APISys_TXHuiJu(1088, PolyPlatEnum.BUSINESS_TXHuiJu),

    APISys_GaoLeGao(1090, PolyPlatEnum.BUSINESS_GaoLeGao),

    APISys_TianGou(1092, PolyPlatEnum.BUSINESS_TianGou),

    APISys_MeiTuanYYJK(1095, PolyPlatEnum.BUSINESS_MeiTuanYYJK),

    APISys_WeiYiYun(1097, PolyPlatEnum.BUSINESS_WeiYiYun),

    APISys_DongFangFuLiWang(1099, PolyPlatEnum.BUSINESS_DongFangFuLiWang),

    APISys_JDQuanQuDao(1105, PolyPlatEnum.BUSINESS_JDQuanQuDao),

    APISys_bilibiliGongYingShang(1108, PolyPlatEnum.BUSINESS_bilibiliGongYingShang),

    APISys_YingLiuBao(1110, PolyPlatEnum.BUSINESS_YingLiuBao),

    APISys_DouDianDaiFa(1111, PolyPlatEnum.BUSINESS_DouDianDaiFa),

    APISys_WalMart(1115, PolyPlatEnum.BUSINESS_WalMart),

    APISys_HuaWeiBOP(1119, PolyPlatEnum.BUSINESS_HuaWeiBOP),

    APISys_FanKeShop(1121, PolyPlatEnum.BUSINESS_FanKeShop),

    APISys_DongXiJi(1123, PolyPlatEnum.BUSINESS_DongXiJi),

    APISys_TmallHongKong(1126, PolyPlatEnum.BUSINESS_TmallHongKong),

    APISys_YiJiuPi(1127, PolyPlatEnum.BUSINESS_YiJiuPi),

    APISys_TikTok(1128, PolyPlatEnum.BUSINESS_TikTok),

    APISys_Daraz(1130, PolyPlatEnum.BUSINESS_Daraz),

    APISys_HuaChengNongFu(1131, PolyPlatEnum.BUSINESS_HuaChengNongFu),

    APISys_DaShiXiong(1133, PolyPlatEnum.BUSINESS_DaShiXiong),

    APISys_ZhenKunHang(1135, PolyPlatEnum.BUSINESS_ZhenKunHang),

    APISys_WeiXinXiaoChengXu(1136, PolyPlatEnum.BUSINESS_WeiXinXiaoChengXu),

    APISys_BaiDuJianKang(1138, PolyPlatEnum.BUSINESS_BaiDuJianKang),

    APISys_DingDangTong(1139, PolyPlatEnum.BUSINESS_DingDangTong),

    APISys_GuangFaYinHang(1140, PolyPlatEnum.BUSINESS_GuangFaYinHang),

    APISys_MaoXiangZiYing(1141, PolyPlatEnum.BUSINESS_MaoXiangZiYing),

    APISys_TaoBaoDaiFa(1142, PolyPlatEnum.BUSINESS_TaoBaoDaiFa),

    APISys_XianYuGuanJia(1143, PolyPlatEnum.BUSINESS_XianYuGuanJia),

    APISys_YiPinCang(1147, PolyPlatEnum.BUSINESS_YiPinCang),

    APISys_DouDianGongXiao(1149, PolyPlatEnum.BUSINESS_DouDianGongXiao),

    APISys_HongXing(1152, PolyPlatEnum.BUSINESS_HongXing),

    APISys_VTN(1153, PolyPlatEnum.BUSINESS_VTN),

    APISys_ShiPingHaoXiaoDian(1155, PolyPlatEnum.BUSINESS_ShiPingHaoXiaoDian),

    APISys_WeiBoDianShang(1156, PolyPlatEnum.BUSINESS_WeiBoDianShang),

    APISys_KuaiShouDaiDa(1158, PolyPlatEnum.BUSINESS_KuaiShouDaiDa),

    APISys_XiaoETong(1160, PolyPlatEnum.BUSINESS_XiaoETong),

    APISys_HeLing(1163, PolyPlatEnum.BUSINESS_HeLing),

    APISys_NanHang(1164, PolyPlatEnum.BUSINESS_NanHang),

    APISys_Temu(1166, PolyPlatEnum.BUSINESS_Temu),

    APISys_ChuangHuo(1171, PolyPlatEnum.BUSINESS_ChuangHuo),

    APISys_DouDianSupermarket(1173, PolyPlatEnum.BUSINESS_DouDianSupermarket),

    APISys_SMTQuanTuoGuan(1174, PolyPlatEnum.BUSINESS_SMTQuanTuoGuan),

    APISys_YDH2(1177, PolyPlatEnum.BUSINESS_YDH2),

    APISys_Ozon(1179, PolyPlatEnum.BUSINESS_Ozon),

    APISys_DongFangZhenXuan(1182, PolyPlatEnum.BUSINESS_DongFangZhenXuan),

    APISys_YouZanSuiXinDing(1184, PolyPlatEnum.BUSINESS_YouZanSuiXinDing),

    APISys_TaoCaiCai(1187, PolyPlatEnum.BUSINESS_TaoCaiCai),

    APISys_HeMa(1188, PolyPlatEnum.BUSINESS_HeMa),

    APISys_ZhiFuBaoXiaoChengXu(1198, PolyPlatEnum.BUSINESS_ZhiFuBaoXiaoChengXu),

    APISys_YiZhiFu(1202, PolyPlatEnum.BUSINESS_YiZhiFu),

    APISys_DuoDuoTi(1203, PolyPlatEnum.BUSINESS_DuoDuoTi),



    ;

    // endregion

    // region 构造器

    /**
     * 构造器。
     *
     * @param value          管家商品的平台值
     * @param polyPlat   菠萝派平台
     */
    GoodsPolyEnum(int value, PolyPlatEnum polyPlat) {
        this.value = value;
        this.polyPlat = polyPlat;
    }

    // endregion

    // region 变量

    /**
     * 平台值。
     */
    private final int value;

    /**
     * 平台类型枚举
     */
    private final PolyPlatEnum polyPlat;

    // endregion

    //region 方法

    /**
     * 获取 平台类型枚举
     * @return 平台类型枚举
     */
    public PolyPlatEnum getPolyPlat(){
        return this.polyPlat;
    }

    /**
     * 获取 管家商品的平台值
     * @return 管家商品的平台值
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    //endregion

    //region 静态方法

    /**
     * 根据管家商品的平台值获取对应的枚举。
     *
     * @param value 管家商品的平台值
     * @return 对应的枚举
     */
    public static GoodsPolyEnum create(int value) {
        return EnumConvertCacheUtil.convert(value, GoodsPolyEnum.class, EnumConvertType.VALUE);
    }

    //endregion
}
