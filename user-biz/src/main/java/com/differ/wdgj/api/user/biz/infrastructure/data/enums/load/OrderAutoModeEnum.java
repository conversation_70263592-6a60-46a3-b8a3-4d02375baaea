package com.differ.wdgj.api.user.biz.infrastructure.data.enums.load;

import com.alibaba.fastjson.annotation.JSONType;
import com.differ.wdgj.api.component.util.enums.EnumCodeValueDeserializer;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 平台业务特性 - 下载订单模式
 *
 * <AUTHOR>
 * @date 2024-06-24 14:34
 */
@JSONType(deserializer = EnumCodeValueDeserializer.class)
public enum OrderAutoModeEnum implements ValueEnum {

    /**
     * 自动下载
     */
    CUSTOMIZATION_AUTO_LOAD("自动下载", 1),

    /**
     * 仅推单
     */
    AUTO_PUSH("仅推单", 2),

    /**
     * 推单且抓单
     */
    PUSH_AND_LOAD("推单且抓单", 3),

    /**
     * 关闭下载
     */
    NOT_SUPPORT("关闭下载", 4);

    private final String name;
    private final int value;

    OrderAutoModeEnum(String name, int value) {
        this.name = name;
        this.value = value;
    }


    public String getName() {
        return name;
    }

    /**
     * 获取枚举值
     *
     * @return 结果
     */
    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static OrderAutoModeEnum create(String value) {
        return EnumConvertCacheUtil.convert(value, OrderAutoModeEnum.class, EnumConvertType.VALUE);
    }
}
