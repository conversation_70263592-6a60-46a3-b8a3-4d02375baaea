package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.convert.post;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.DbAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.operation.AfterSaleGoodsMatchOperation;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPostProcessOrderHandle;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 换货单商品级删除处理插件
 *
 * <AUTHOR>
 * @date 2024/8/8 上午11:43
 */
public class ExchangeOrderGoodsDeleteHandle extends AbstractPostProcessOrderHandle<BusinessGetExchangeOrderResponseOrderItem> {
    //region 构造
    public ExchangeOrderGoodsDeleteHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    //region 实现基类方法
    /**
     * 售后单后置处理
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    public AfterSaleHandleResult processOrder(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        DbAfterSaleOrderItem dbOrder = sourceOrder.getDbOrder();
        if (dbOrder != null) {
            // 需要删除的退货商品Id
            List<Integer> deleteReturnGoodsIds = new ArrayList<>();
            // 退货/退款商品存在校验
            List<ApiReturnDetailDO> oldRefundGoodsList = dbOrder.getReturnGoods();
            if (CollectionUtils.isNotEmpty(oldRefundGoodsList)) {
                for (ApiReturnDetailDO oldReturnGoods : oldRefundGoodsList) {
                    if (targetOrder.getRefundGoods().stream().noneMatch(x -> AfterSaleGoodsMatchOperation.isMatchHistoryRefundGoods(x, oldReturnGoods))) {
                        deleteReturnGoodsIds.add(oldReturnGoods.getRecId());
                    }
                }
            }
            targetOrder.getDeleteReturnGoodsIds().addAll(deleteReturnGoodsIds);

            // 需要删除的换货商品Id
            List<Integer> deleteExchangeGoodsIds = new ArrayList<>();
            // 退货/退款商品存在校验
            List<ApiReturnDetailTwoDO> oldExchangeGoodsList = dbOrder.getExchangeGoods();
            if (CollectionUtils.isNotEmpty(oldExchangeGoodsList)) {
                for (ApiReturnDetailTwoDO oldReturnGoods : oldExchangeGoodsList) {
                    if (targetOrder.getExchangeGoods().stream().noneMatch(x -> AfterSaleGoodsMatchOperation.isMatchHistoryExchangeGoods(x, oldReturnGoods))) {
                        deleteExchangeGoodsIds.add(oldReturnGoods.getRecId());
                    }
                }
            }
            targetOrder.getDeleteExchangeGoodsIds().addAll(deleteExchangeGoodsIds);
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    public String caption() {
        return "售后商品删除处理";
    }
    //endregion
}
