package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceRefundGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.BaseSaveRefundOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundResponseRefundGoodInfo;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailDO;
import org.apache.commons.lang3.StringUtils;

/**
 * 菠萝派商城 - 保存退货退款单处理器
 *
 * <AUTHOR>
 * @date 2025/3/27 下午2:29
 */
public class PolyMallSaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    //region 构造
    /**
     * 构造
     *
     * @param context 上下文
     */
    public PolyMallSaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 转换商品级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param sourceGoods 原始售后退货商品数据
     * @param targetOrder 目标售后单数据
     * @param refundGoods 目标售后退货商品数据
     * @return 结果
     */
    @Override
    protected GoodsConvertHandleResult refundGoodsConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, SourceRefundGoodsItem<BusinessGetRefundResponseRefundGoodInfo> sourceGoods, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods) {
        // 基础信息
        BusinessGetRefundResponseRefundGoodInfo ployAfterSaleGoods = sourceGoods.getPloyRefundGoods();
        // 兼容菠萝派逻辑，原始单platSkuId有返回，售后单未返回
        if(StringUtils.isNotEmpty(ployAfterSaleGoods.getSku())){
            refundGoods.setSku(ployAfterSaleGoods.getSku());
            refundGoods.setPlatSkuId(StringUtils.EMPTY);
        }

        return GoodsConvertHandleResult.success();
    }

}
