package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiTradeGoodsDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 原始单货品仓储(g_api_tradegoods)
 *
 * <AUTHOR>
 * @date 2024/7/17 下午6:52
 */
public interface ApiTradeGoodsMapper {
    /**
     * 根据原始单号批量查询原始单商品
     *
     * @param billIds 原始单号Id
     * @return 原始单列表
     */
    List<ApiTradeGoodsDO> selectByBillIds(@Param("billIds") List<Integer> billIds);

    /**
     * 根据原始单号批量查询完成原始单商品
     *
     * @param billIds 原始单号Id
     * @return 原始单列表
     */
    List<ApiTradeGoodsDO> selectArcByBillIds(@Param("billIds") List<Integer> billIds);

    /**
     * 根据原始单号批量查询归档原始单商品
     *
     * @param billIds 原始单号Id
     * @return 原始单列表
     */
    List<ApiTradeGoodsDO> selectHisByBillIds(@Param("billIds") List<Integer> billIds);
}
