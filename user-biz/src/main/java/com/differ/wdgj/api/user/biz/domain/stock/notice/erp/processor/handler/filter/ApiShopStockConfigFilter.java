package com.differ.wdgj.api.user.biz.domain.stock.notice.erp.processor.handler.filter;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.notice.ApiSysMatchNotice;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.notice.erp.processor.handler.INoticeProcessHandle;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopAuthStatusEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;

/**
 * 店铺基础信息过滤
 *
 * <AUTHOR>
 * @date 2024/11/11 下午8:22
 */
public class ApiShopStockConfigFilter implements INoticeProcessHandle {
    /**
     * 匹配数据转换为[平台商品库存变动通知]
     *
     * @param apiSysMatch    匹配信息
     * @return 匹配处理结果
     */
    @Override
    public StockContentResult<?> process(StockSyncContext shopContext, ApiSysMatchNotice apiSysMatch) {
        // 店铺基础信息过滤
        ApiShopBaseDto shopBase = shopContext.getShopBase();
        if (shopBase == null) {
            return StockContentResult.failed("店铺信息为空");
        }
        // 店铺基础信息
        String shopName = shopBase.getShopName();
        PolyPlatEnum plat = shopBase.getPlat();

        if (ConfigKeyUtils.isActionWdgj(ConfigKeyEnum.ISACTION_CLOSESYNCSTOCKPLAT, plat.toString())) {
            return StockContentResult.failed(String.format("平台库存【%s】同步已禁用", plat.getName()));
        }
        if (!shopBase.isActived() || shopBase.isDelete()) {
            return StockContentResult.failed(String.format("店铺【%s】已停用或已删除", shopName));
        }
        if (shopBase.getAuthStatus() != ShopAuthStatusEnum.AUTHORIZED) {
            return StockContentResult.failed(String.format("店铺【%s】未授权或授权失效", shopName));
        }

        // 库存同步配置过滤
        SyncStockShopConfig syncStockConfig = shopContext.getSyncStockConfig();
        if (syncStockConfig == null) {
            return StockContentResult.failed(String.format("店铺【%s】库存同步配置为空", shopName));
        }
        if (!syncStockConfig.getIsEnableAutoSyncStock()) {
            return StockContentResult.failed(String.format("店铺【%s】未开启自动库存同步", shopName));
        }

        return StockContentResult.success();
    }
}
