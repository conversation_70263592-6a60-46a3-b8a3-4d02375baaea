package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.plat;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.refund.BaseSaveRefundOrderProcessor;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnListDO;

/**
 * 1688数字小店
 *
 * <AUTHOR>
 * @date 2024/12/25 下午3:48
 */
public class ShuZiXiaoDian1688SaveRefundOrderProcessor extends BaseSaveRefundOrderProcessor {
    //region 构造

    /**
     * 构造
     *
     * @param context 上下文
     */
    public ShuZiXiaoDian1688SaveRefundOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetRefundOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 基础数据
        BusinessGetRefundOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();

        // 退货地址
        afterSaleOrder.setReserved1(ployOrder.getAddress());

        return AfterSaleHandleResult.success();
    }
}
