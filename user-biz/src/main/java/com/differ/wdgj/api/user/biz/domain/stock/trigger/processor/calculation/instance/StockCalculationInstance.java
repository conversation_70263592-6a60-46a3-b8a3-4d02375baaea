package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.instance;

import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockDetailCountTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.GoodsStockCalculationDto;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.IStockCalculationMode;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule.StockCalculationRule;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy.IStockCalculationStrategy;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 库存计算实例
 * 组合计算模式和库存计算策略，要求计算模式和库存计算策略可以通过外部子类注入
 * <a href="https://s.jkyun.biz/iU5VJIM">【管家java】开发设计 - 库存同步发起 - 库存量计算</a>
 *
 * <AUTHOR>
 * @date 2024-12-26 10:00
 */
public class StockCalculationInstance {
    //region 常量
    /**
     * 全局上下文
     */
    protected final StockSyncContext context;

    /**
     * 计算模式
     */
    private final IStockCalculationMode calculationMode;

    /**
     * 计算策略
     */
    private final IStockCalculationStrategy calculationStrategy;

    /**
     * 日志标题
     */
    protected final String Caption = "库存计算实例";
    //endregion

    //region 构造方法

    /**
     * 构造方法
     *
     * @param context             上下文
     * @param calculationMode     计算模式
     * @param calculationStrategy 计算策略
     */
    public StockCalculationInstance(StockSyncContext context, IStockCalculationMode calculationMode, IStockCalculationStrategy calculationStrategy) {
        if (context == null) {
            throw new IllegalArgumentException("上下文不能为空");
        }
        if (calculationMode == null) {
            throw new IllegalArgumentException("计算模式不能为空");
        }
        if (calculationStrategy == null) {
            throw new IllegalArgumentException("计算策略不能为空");
        }

        this.context = context;
        this.calculationMode = calculationMode;
        this.calculationStrategy = calculationStrategy;
    }

    //endregion

    //region 公共方法
    /**
     * 计算同步数量
     *
     * @param rule 计算规则
     * @return 计算结果
     */
    public GoodsStockCalculationResult calculateSyncQuantity(StockCalculationRule rule) {
        try {
            // 基础校验
            StockContentResult<?> verifyResult = verifyCalculateParam(rule);
            if (verifyResult.isFailed()) {
                return GoodsStockCalculationResult.failed(verifyResult.getMessage());
            }

            // 使用计算模式获取实际库存数量
            GoodsStockCalculationDto calculationDto = buildCalculateParam(rule);
            GoodsStockCalculationResult actualStockResult = calculationMode.getActualStock(calculationDto);
            if (actualStockResult.isFailed()) {
                return GoodsStockCalculationResult.failed("获取实际库存失败：" + actualStockResult.getMessage());
            }

            // 使用计算策略计算同步数量
            BigDecimal realStockCount = actualStockResult.getStockCount();
            GoodsStockCalculationResult syncQuantityResult = calculationStrategy.calculateSyncQuantity(rule, realStockCount);
            if (syncQuantityResult.isFailed()) {
                return GoodsStockCalculationResult.failed("计算同步数量失败：" + syncQuantityResult.getMessage());
            }

            // 结果整合
            BigDecimal stockCount = syncQuantityResult.getStockCount();
            Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap = new HashMap<>();
            stockDetailMap.putAll(actualStockResult.getStockDetailMap());
            stockDetailMap.putAll(syncQuantityResult.getStockDetailMap());

            return GoodsStockCalculationResult.success(stockCount, realStockCount, stockDetailMap);

        } catch (Exception e) {
            String message = String.format("【%s】计算同步数量异常，原因：%s",
                    context.getVipUser(), e.getMessage());
            LogFactory.error(Caption, message, e);
            return GoodsStockCalculationResult.failed(message);
        }
    }

    //endregion

    //region 私有方法

    /**
     * 计算规则校验
     *
     * @param rule 规则
     * @return 校验结果
     */
    private StockContentResult<?> verifyCalculateParam(StockCalculationRule rule) {
        if (rule == null) {
            return StockContentResult.failed("库存同步计算规则为空");
        }
        if (rule.getWarehouseCodes() == null || rule.getWarehouseCodes().isEmpty()) {
            return StockContentResult.failed("未正确配置仓库信息");
        }

        return StockContentResult.success();
    }

    /**
     * 构建计算参数
     *
     * @param rule 计算规则
     * @return 计算参数
     */
    private GoodsStockCalculationDto buildCalculateParam(StockCalculationRule rule) {
        GoodsStockCalculationDto calculateParam = new GoodsStockCalculationDto();
        calculateParam.setErpWarehouseIds(rule.getWarehouseCodes());
        calculateParam.setErpGoodsId(rule.getErpGoodsId());
        calculateParam.setErpSpecId(rule.getErpSpecId());
        calculateParam.setGoodsType(rule.getGoodsType());
        calculateParam.setShopId(rule.getShopId());
        return calculateParam;
    }

    //endregion

    //region get方法

    public IStockCalculationMode getCalculationMode() {
        return calculationMode;
    }

    public IStockCalculationStrategy getCalculationStrategy() {
        return calculationStrategy;
    }

    //endregion
}
