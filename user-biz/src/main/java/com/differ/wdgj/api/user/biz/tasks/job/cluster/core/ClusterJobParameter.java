package com.differ.wdgj.api.user.biz.tasks.job.cluster.core;

import com.differ.wdgj.api.user.biz.tasks.job.cluster.strategy.ClusterJobExecStrategy;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Description 集群任务参数
 * <AUTHOR>
 * @Date 2023/11/21 13:58
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ClusterJobParameter {

    /**
     * 选举策略
     *
     * @return 分片策略
     */
    Class<? extends ClusterJobExecStrategy> jobExecStrategy();
}
