package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.WdgjGoodsTypeEnum;

import java.time.LocalDateTime;

/**
 * 商品匹配数据 - 数据库实体
 *
 * <AUTHOR>
 * @date 2024-03-01 10:46
 */
public class ApiSysMatchDO {

    /**
    * ID
    */
    private int id;

    /**
     * 管家店铺ID
     */
    private int shopId;

    /**
     * 平台ID 对应枚举
     */
    private Integer bTBGoods;

    // region 库存同步相关字段

    /**
     * 是否需要同步，0：不同步，1：待同步，99同步中
     */
    private Integer isSys;

    /**
     * 触发标识 0:无  1:入库审核；默认0
     */
    private int synFlag;

    /**
     * 用于标识是否可以更新IsSys字段
     */
    private Byte bGetStock;

    /**
     * 同步数量
     */
    private Integer sysCount;

    /**
     * 同步结果
     */
    private String sysLog;

    /**
     * 库存同步的更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModify;

    // endregion

    //region 管家商品信息

    /**
     * 管家货品ID
     */
    private Integer goodsID;

    /**
     * 管家规格ID,-2表示批次商品
     */
    private Integer specID;

    /**
     * 是否组合装
     * {@link WdgjGoodsTypeEnum}
     */
    private Integer goodsType;

    //endregion

    //region 平台商品信息

    /**
    * 平台货品ID
    */
    private String numiid;

    /**
    * 平台规格ID
    */
    private String skuID;

    /**
    * 平台商品名称
    */
    private String tBName;

    /**
    * 平台规格名称
    */
    private String tBSku;

    /**
    * 平台商家主编码
    */
    private String tBOuterID;

    /**
    * 平台商家子编码
    */
    private String skuOuterID;

    /**
     * 同步类型（出售中，仓库中）
     */
    private String sysGoodsType;

    /**
     * 子店铺ID
     */
    private String subShopID;

    /**
    * 达令，亚马逊专用,订单发货类别,'DEFAULT' or sendtype='AMAZON_CN'
    */
    private String sendType;

    /**
    * 仓库代码
    */
    private String whseCode;

    /**
    * 常态合作编码
    */
    private String cooperationNo;

    /**
    * 仓库编码标识 0：全国逻辑仓或7大仓；1：省仓 默认为全国逻辑仓或7大仓
    */
    private int warehouseFlag;

    /**
    * 商品上下架状态(默认=0，已上架商品=1，已下架商品=2)
    */
    private Integer shelfState;

    //endregion

    //region 匹配级库存同步规则

    /**
     * 是否同步固定数量  0：不同步 1：同步固定数量 2：区间内同步固定数量
     */
    private int bFixNum;

    /**
     * 固定数量
     */
    private Integer fixNum;

    /**
     * 是否增加同步数量（库存同步规则中的特定同步规则中的）
     */
    private Boolean bVirNum;

    /**
     * >=（库存同步规则中的特定同步规则中的）
     */
    private Integer virNumBase;

    /**
     * 增加多少（库存同步规则中的特定同步规则中的）
     */
    private Integer virNumInc;

    /**
     * 库存同步规则中的是否停用(!=0表示不同步库存，包括手动和自动)
     */
    private Boolean bstop;

    /**
     * 是否同步数量百分比
     */
    private Boolean bSingletb;

    /**
     * 同步百分比
     */
    private Integer singleNumPer;

    /**
     * <（库存同步规则中的特定同步规则中的）
     */
    private Integer virNumTop;

    /**
     * 库存同步规则是否停用(!=0表示不启用库存同步规则)
     */
    private Boolean bruleStop;

    /**
     * 库存同步规则同步指定仓库
     */
    private String ruleWarehouse;

    //endregion

    /**
    * 扩展表信息
    */
    private ApiSysMatchExtDO extEntity;

    //region get/set
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public Integer getbTBGoods() {
        return bTBGoods;
    }

    public void setbTBGoods(Integer bTBGoods) {
        this.bTBGoods = bTBGoods;
    }

    public Integer getIsSys() {
        return isSys;
    }

    public void setIsSys(Integer isSys) {
        this.isSys = isSys;
    }

    public int getSynFlag() {
        return synFlag;
    }

    public void setSynFlag(int synFlag) {
        this.synFlag = synFlag;
    }

    public Byte getbGetStock() {
        return bGetStock;
    }

    public void setbGetStock(Byte bGetStock) {
        this.bGetStock = bGetStock;
    }

    public Integer getSysCount() {
        return sysCount;
    }

    public void setSysCount(Integer sysCount) {
        this.sysCount = sysCount;
    }

    public String getSysLog() {
        return sysLog;
    }

    public void setSysLog(String sysLog) {
        this.sysLog = sysLog;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getLastModify() {
        return lastModify;
    }

    public void setLastModify(LocalDateTime lastModify) {
        this.lastModify = lastModify;
    }

    public Integer getGoodsID() {
        return goodsID;
    }

    public void setGoodsID(Integer goodsID) {
        this.goodsID = goodsID;
    }

    public Integer getSpecID() {
        return specID;
    }

    public void setSpecID(Integer specID) {
        this.specID = specID;
    }

    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    public String getNumiid() {
        return numiid;
    }

    public void setNumiid(String numiid) {
        this.numiid = numiid;
    }

    public String getSkuID() {
        return skuID;
    }

    public void setSkuID(String skuID) {
        this.skuID = skuID;
    }

    public String gettBName() {
        return tBName;
    }

    public void settBName(String tBName) {
        this.tBName = tBName;
    }

    public String gettBSku() {
        return tBSku;
    }

    public void settBSku(String tBSku) {
        this.tBSku = tBSku;
    }

    public String gettBOuterID() {
        return tBOuterID;
    }

    public void settBOuterID(String tBOuterID) {
        this.tBOuterID = tBOuterID;
    }

    public String getSkuOuterID() {
        return skuOuterID;
    }

    public void setSkuOuterID(String skuOuterID) {
        this.skuOuterID = skuOuterID;
    }

    public String getSysGoodsType() {
        return sysGoodsType;
    }

    public void setSysGoodsType(String sysGoodsType) {
        this.sysGoodsType = sysGoodsType;
    }

    public String getSubShopID() {
        return subShopID;
    }

    public void setSubShopID(String subShopID) {
        this.subShopID = subShopID;
    }

    public String getSendType() {
        return sendType;
    }

    public void setSendType(String sendType) {
        this.sendType = sendType;
    }

    public String getWhseCode() {
        return whseCode;
    }

    public void setWhseCode(String whseCode) {
        this.whseCode = whseCode;
    }

    public String getCooperationNo() {
        return cooperationNo;
    }

    public void setCooperationNo(String cooperationNo) {
        this.cooperationNo = cooperationNo;
    }

    public int getWarehouseFlag() {
        return warehouseFlag;
    }

    public void setWarehouseFlag(int warehouseFlag) {
        this.warehouseFlag = warehouseFlag;
    }

    public Integer getShelfState() {
        return shelfState;
    }

    public void setShelfState(Integer shelfState) {
        this.shelfState = shelfState;
    }

    public int getbFixNum() {
        return bFixNum;
    }

    public void setbFixNum(int bFixNum) {
        this.bFixNum = bFixNum;
    }

    public Integer getFixNum() {
        return fixNum;
    }

    public void setFixNum(Integer fixNum) {
        this.fixNum = fixNum;
    }

    public Boolean getbVirNum() {
        return bVirNum;
    }

    public void setbVirNum(Boolean bVirNum) {
        this.bVirNum = bVirNum;
    }

    public Integer getVirNumBase() {
        return virNumBase;
    }

    public void setVirNumBase(Integer virNumBase) {
        this.virNumBase = virNumBase;
    }

    public Integer getVirNumInc() {
        return virNumInc;
    }

    public void setVirNumInc(Integer virNumInc) {
        this.virNumInc = virNumInc;
    }

    public Boolean getBstop() {
        return bstop;
    }

    public void setBstop(Boolean bstop) {
        this.bstop = bstop;
    }

    public Boolean getbSingletb() {
        return bSingletb;
    }

    public void setbSingletb(Boolean bSingletb) {
        this.bSingletb = bSingletb;
    }

    public Integer getSingleNumPer() {
        return singleNumPer;
    }

    public void setSingleNumPer(Integer singleNumPer) {
        this.singleNumPer = singleNumPer;
    }

    public Integer getVirNumTop() {
        return virNumTop;
    }

    public void setVirNumTop(Integer virNumTop) {
        this.virNumTop = virNumTop;
    }

    public Boolean getBruleStop() {
        return bruleStop;
    }

    public void setBruleStop(Boolean bruleStop) {
        this.bruleStop = bruleStop;
    }

    public String getRuleWarehouse() {
        return ruleWarehouse;
    }

    public void setRuleWarehouse(String ruleWarehouse) {
        this.ruleWarehouse = ruleWarehouse;
    }

    public ApiSysMatchExtDO getExtEntity() {
        return extEntity;
    }

    public void setExtEntity(ApiSysMatchExtDO extEntity) {
        this.extEntity = extEntity;
    }
    //endregion
}
