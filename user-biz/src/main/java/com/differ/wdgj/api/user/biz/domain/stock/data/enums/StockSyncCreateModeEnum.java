package com.differ.wdgj.api.user.biz.domain.stock.data.enums;

import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 库存同步触发模式
 *
 * <AUTHOR>
 * @date 2024/11/12 下午6:39
 */
public enum StockSyncCreateModeEnum implements ValueEnum {
    /**
     * 订单递交
     */
    ORDER_POST(0, "订单递交"),

    /**
     * 订单出库
     */
    ORDER_STOCK_OUT(1, "订单出库");

    private final int value;
    private final String description;

    StockSyncCreateModeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
