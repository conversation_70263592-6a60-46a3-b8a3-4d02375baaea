package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.jmq;

import com.differ.jackyun.framework.component.jmq.core.QueueEnabled;
import com.differ.jackyun.framework.component.jmq.core.annotation.JChild;
import com.differ.jackyun.framework.component.jmq.core.consumer.retry.ConsumerFailCallback;
import com.differ.jackyun.framework.component.jmq.rabbit.annotation.JMQRabbit;
import com.differ.wdgj.api.user.biz.tasks.mq.jmq.core.ApiJMQConsumerFailCallback;
import com.differ.wdgj.api.user.biz.tasks.mq.jmq.core.ApiMqEnabled;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * API多队列注解
 *
 * <AUTHOR>
 * @date 2024-03-19 13:52
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
@Inherited
@JMQRabbit
@JChild
public @interface JmqAdapterAnnotation {

    /**
     * 队列标识
     *
     * @return 队列标识
     */
    String code() default "";

    /**
     * 发送站点
     *
     * @return 站点集合
     */
    String[] sitesToSend() default {};

    /**
     * 发送站点
     *
     * @return 站点集合
     */
    String[] sitesToReceive() default {};

    /**
     * 消费者/生产者是否会被创建
     *
     * @return enable
     */
    Class<? extends QueueEnabled> queueEnabled() default ApiMqEnabled.class;

    /**
     * JMQ消息消费重试达到最大次数回调处理
     *
     * @return ConsumerFailCallback
     */
    Class<? extends ConsumerFailCallback> consumerFailCallback() default ApiJMQConsumerFailCallback.class;

}
