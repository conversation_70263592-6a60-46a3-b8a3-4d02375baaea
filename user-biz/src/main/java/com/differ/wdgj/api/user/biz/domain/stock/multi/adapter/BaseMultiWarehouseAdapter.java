package com.differ.wdgj.api.user.biz.domain.stock.multi.adapter;

import com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch.data.CommonWarehouseMatchDTO;
import com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch.data.enums.WarehouseMatchBizTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockMultiWhsModeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.multi.StockMultiWhsContext;
import com.differ.wdgj.api.user.biz.domain.stock.multi.adapter.core.IMultiWarehouseAdapter;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop.ApiShopWhsMatchLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.SyncStockContent;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.ApiShopWhsMatchDto;
import com.differ.wdgj.api.user.biz.infrastructure.utils.plat.SyncStockBizFeatureUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *  平台多仓映射适配器 基础逻辑
 *
 * <AUTHOR>
 * @date 2024/12/23 下午5:46
 */
public class BaseMultiWarehouseAdapter implements IMultiWarehouseAdapter {
    //region 常量
    /**
     * 上下文
     */
    protected StockMultiWhsContext context;
    //endregion

    //region 构造
    public BaseMultiWarehouseAdapter(StockMultiWhsContext context) {
        this.context = context;
    }
    //endregion

    //region 实现接口

    /**
     * 通过匹配判断是否支持多仓库存同步
     *
     * @param goodsMatch 匹配数据
     * @return 是否支持
     */
    @Override
    public StockMultiWhsModeEnum getMode(ApiSysMatchDO goodsMatch) {
        SyncStockContent feature = SyncStockBizFeatureUtils.getFeature(context.getShopBase(), StringUtils.EMPTY);
        if (feature == null) {
            // 默认仅支持普通库存同步
            return StockMultiWhsModeEnum.ONLY_NORMAL;
        }
        return feature.getMultiWhsMode();
    }

    /**
     * 基于匹配信息获取仓库匹配数据
     *
     * @param goodsMatch 匹配数据
     * @return 通用仓库匹配数据列表
     */
    @Override
    public Set<CommonWarehouseMatchDTO> getWhsMatches(ApiSysMatchDO goodsMatch) {
        // 基础数据
        String memberName = context.getMemberName();
        Integer shopId = context.getShopId();

        // 查询店铺仓库匹配数据
        ApiShopWhsMatchDto whsMatchAllDto = ApiShopWhsMatchLocalCache.singleton().getWhsMatch(memberName, shopId);
        if (whsMatchAllDto == null) {
            return Collections.emptySet();
        }

        Set<CommonWarehouseMatchDTO> whsMatches = whsMatchAllDto.getWhsMatches();
        // 库存同步体系下，不支持默认仓库（通常用于下载订单中不返回仓库信息的订单）
        return whsMatches.stream().filter(x -> x.getBizType() != WarehouseMatchBizTypeEnum.DEFAULT_MATCH).collect(Collectors.toSet());
    }

    /**
     * 基于erp仓库id获取仓库匹配数据
     *
     * @param goodsMatch      匹配数据
     * @param erpWarehouseIds erp仓库Id
     * @return 通用仓库匹配数据列表
     */
    @Override
    public Set<CommonWarehouseMatchDTO> getWhsMatchesByErp(ApiSysMatchDO goodsMatch, Set<Integer> erpWarehouseIds) {
        // 基础数据校验
        if (CollectionUtils.isEmpty(erpWarehouseIds)) {
            return Collections.emptySet();
        }
        // 获取仓库匹配数据
        Set<CommonWarehouseMatchDTO> whsMatches = getWhsMatches(goodsMatch);
        if (CollectionUtils.isEmpty(whsMatches)) {
            return Collections.emptySet();
        }
        // 基于erp仓库id过滤
        return whsMatches.stream().filter(x -> erpWarehouseIds.stream().anyMatch(y -> x.getErpWarehouseIds().contains(y))).collect(Collectors.toSet());
    }

    /**
     * 基于平台仓库标识获取仓库匹配信息
     *
     * @param goodsMatch   匹配数据
     * @param platWareSign 平台仓库标识
     * @return 通用仓库匹配数据列表
     */
    @Override
    public Set<CommonWarehouseMatchDTO> getWhsMatchesByPlat(ApiSysMatchDO goodsMatch, String platWareSign) {
        // 基础数据校验
        if (StringUtils.isEmpty(platWareSign)) {
            return Collections.emptySet();
        }
        // 获取仓库匹配数据
        Set<CommonWarehouseMatchDTO> whsMatches = getWhsMatches(goodsMatch);
        if (CollectionUtils.isEmpty(whsMatches)) {
            return Collections.emptySet();
        }
        // 平台仓库标识过滤
        return whsMatches.stream().filter(x -> StringUtils.equals(x.getPlatWarehouseSign(), platWareSign)).collect(Collectors.toSet());
    }
    //endregion

}
