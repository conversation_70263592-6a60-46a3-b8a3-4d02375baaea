package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchExtResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchResult;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiPlatSysHisDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchExtDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiPlatSysHisMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchExtMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 商品匹配相关操作
 *
 * <AUTHOR>
 * @date 2024-03-05 15:48
 */
public class StockGoodsMatchOperation {

    // region 单例

    /**
     * 私有构造
     */
    private StockGoodsMatchOperation() {  }

    /**
     * 单例
     *
     * @return 单例
     */
    public static StockGoodsMatchOperation singleton() {
        return StockGoodsMatchOperation.SingletonEnum.SINGLETON.instance;
    }

    /**
     * 单例枚举
     */
    private enum SingletonEnum {

        /**
         * 单例
         */
        SINGLETON;

        private final StockGoodsMatchOperation instance;

        SingletonEnum() {
            instance = new StockGoodsMatchOperation();
        }
    }

    //endregion

    // region 常量

    /**
     * 商品匹配仓储
     */
    private final ApiSysMatchMapper apiSysMatchMapper = BeanContextUtil.getBean(ApiSysMatchMapper.class);

    /**
     * 商品匹配扩展仓储
     */
    private final ApiSysMatchExtMapper apiSysMatchExtMapper = BeanContextUtil.getBean(ApiSysMatchExtMapper.class);

    /**
     * 库存同步日志仓储
     */
    private final ApiPlatSysHisMapper apiPlatSysHisMapper = BeanContextUtil.getBean(ApiPlatSysHisMapper.class);

    // endregion

    // region 公共方法

    /**
     * 查询匹配
     *
     * @param vipUser     会员名
     * @param apiSysMatchIds 匹配表主键
     * @return 结果
     */
    public List<ApiSysMatchDO> queryMatch(String vipUser, List<Integer> apiSysMatchIds){
        return DBSwitchUtil.doDBWithUser(vipUser, () -> apiSysMatchMapper.selectWithExtraByIds(apiSysMatchIds));
    }

    /**
     * 批量插入库存同步日志
     * @param vipUser 会员名
     * @param stockSyncLogs 库存同步日志列表
     */
    public void batchInsertStockSyncLog(String vipUser, List<ApiPlatSysHisDO> stockSyncLogs){
        if(StringUtils.isBlank(vipUser) || CollectionUtils.isEmpty(stockSyncLogs)){
            return;
        }

        // 拆分
        List<List<ApiPlatSysHisDO>> subList = Lists.partition(stockSyncLogs, 200);

        // 分组插入
        DBSwitchUtil.doDBWithUser(vipUser, () ->{
            for (List<ApiPlatSysHisDO> list : subList) {
                this.apiPlatSysHisMapper.batchAddApiPlatSysHis(list);
            }
        });
    }

    /**
     * 批量保存库存同步匹配结果
     * @param vipUser 会员名
     * @param apiSysMatchResults 库存同步结果匹配列表
     */
    public void batchSaveSyncStockResult(String vipUser, Set<StockSyncApiSysMatchResult> apiSysMatchResults){
        if(StringUtils.isBlank(vipUser) || CollectionUtils.isEmpty(apiSysMatchResults)){
            return;
        }

        // 拆分
        List<StockSyncApiSysMatchResult> apiSysMatchResultList = new ArrayList<>(apiSysMatchResults);
        List<List<StockSyncApiSysMatchResult>> subList = Lists.partition(apiSysMatchResultList, 200);

        // 分组保存
        DBSwitchUtil.doDBWithUser(vipUser, () ->{
            for (List<StockSyncApiSysMatchResult> list : subList) {
                this.apiSysMatchMapper.updateSyncStockResult(list);
            }
        });
    }

    /**
     * 批量保存库存同步匹配扩展结果
     * @param vipUser 会员名
     * @param apiSysMatchResults 库存同步结果匹配扩展列表
     */
    public void batchSaveSyncStockExtResult(String vipUser, Set<StockSyncApiSysMatchExtResult> apiSysMatchResults){
        if(StringUtils.isBlank(vipUser) || CollectionUtils.isEmpty(apiSysMatchResults)){
            return;
        }

        // 拆分
        List<ApiSysMatchExtDO> apiSysMatchResultList = new ArrayList<>();
        for (StockSyncApiSysMatchExtResult extResult: apiSysMatchResults) {
            if(extResult != null){
                // 构建待更新匹配数据
                ApiSysMatchExtDO apiSysMatchExt = new ApiSysMatchExtDO();
                apiSysMatchExt.setId(extResult.getApiSysMatchId());
                apiSysMatchExt.setIncreFlag(extResult.getIncreFlag());
                apiSysMatchExt.setRestrictedMode(extResult.getRestrictedMode());
                apiSysMatchExt.setNextResetSyncTime(extResult.getNextResetSyncTime());
                apiSysMatchExt.setPlatGoodsType(extResult.getPlatGoodsType());
                apiSysMatchResultList.add(apiSysMatchExt);
            }
        }

        List<List<ApiSysMatchExtDO>> subList = Lists.partition(apiSysMatchResultList, 200);

        // 分组保存
        DBSwitchUtil.doDBWithUser(vipUser, () ->{
            for (List<ApiSysMatchExtDO> list : subList) {
                this.apiSysMatchExtMapper.addOrUpdateGoodsMatchExtra(list);
            }
        });
    }

    // endregion
}
