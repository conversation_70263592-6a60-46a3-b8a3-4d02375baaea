package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.GCfgSysDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.BasicOperateMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024/3/1 14:07
 */
public interface GCfgSysMapper extends BasicOperateMapper<GCfgSysDO> {

    String selectByConfigKey(String cfgkey);

    String getSysAutoId(@Param("tableName") String tableName, @Param("fieldName") String fieldName);

}
