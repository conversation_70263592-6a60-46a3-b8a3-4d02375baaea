package com.differ.wdgj.api.user.biz.infrastructure.utils.shop;

import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockNumRuleDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ShopConfigStockSyncRuleEnum;

/**
 * 库存同步店铺配置工具类
 *
 * <AUTHOR>
 * @date 2024/11/1 下午2:03
 */
public class SyncStockShopConfigUtils {
    //region 常量
    /**
     * 业务类型
     */
    private static final ApiShopConfigBizTypes TYPE = ApiShopConfigBizTypes.SYNC_STOCK;
    //endregion

    //region 构造
    private SyncStockShopConfigUtils() {
    }
    //endregion

    //region 获取配置

    /**
     * 根据店铺id获取库存同步店铺配置
     *
     * @param outAccount 外部会员名
     * @param shopId  外部店铺id
     * @return 店铺配置
     */
    public static SyncStockShopConfig singleByShopId(String outAccount, int shopId) {
        return (SyncStockShopConfig) ShopConfigUtils.getBizConfig(outAccount, TYPE, shopId);
    }

    //endregion

    /**
     * 解析库存同步店铺配置计算规则
     *
     * @param config 库存同步店铺配置
     * @return 计算规则
     */
    public static ShopConfigStockSyncRuleEnum getShopConfigStockSyncRule(SyncStockShopConfig config) {
        if (config == null || config.getSyncNumRule() == null) {
            return null;
        }

        SyncStockNumRuleDto syncNumRule = config.getSyncNumRule();

        // 实际库存-未付款数量-订购量-待发货量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_ONE;
        }

        // 实际库存-订购量-待发货量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWO;
        }

        // 实际库存
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_THREE;
        }

        // 实际库存-未付款数量+采购在途数量-订购量-待发货量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_FOUR;
        }

        // 实际库存+采购在途数量-订购量-待发货量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_FIVE;
        }

        // 实际库存-未付款数量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_SIX;
        }

        // 实际库存+采购在途数量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_SEVEN;
        }

        // 实际库存-订购量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_EIGHT;
        }

        // 实际库存-待发货量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_NINE;
        }

        // 实际库存+采购在途数量-订购量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TEN;
        }

        // 实际库存-未付款数量-订购量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_ELEVEN;
        }

        // 实际库存+采购在途数量-待发货量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWELVE;
        }

        // 实际库存-未付款数量-待发货量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_THIRTEEN;
        }

        // 实际库存-未付款数量+采购在途数量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_FOURTEEN;
        }

        // 实际库存-未付款数量+采购在途数量-待发货量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_FIFTEEN;
        }

        // 实际库存-未付款数量+采购在途数量-订购量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && !syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_SIXTEEN;
        }

        // 实际库存+调入在途数量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_SEVENTEEN;
        }

        // 实际库存-未付款数量+调入在途数量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_EIGHTEEN;
        }

        // 实际库存+采购在途数量+调入在途数量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_NINETEEN;
        }

        // 实际库存+调入在途数量-订购量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWENTY;
        }

        // 实际库存+调入在途数量-待发货量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWENTYONE;
        }

        // 实际库存+调入在途数量-订购量-待发货量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWENTYTWO;
        }

        // 实际库存+采购在途数量+调入在途数量-订购量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWENTYTHREE;
        }

        // 实际库存-未付款数量+调入在途数量-订购量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWENTYFOUR;
        }

        // 实际库存+采购在途数量+调入在途数量-待发货量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWENTYFIVE;
        }

        // 实际库存-未付款数量+调入在途数量-待发货量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWENTYSIX;
        }

        // 实际库存-未付款数量+采购在途数量+调入在途数量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWENTYSEVEN;
        }

        // 实际库存+采购在途数量+调入在途数量-订购量-待发货量
        if (syncNumRule.getIsActualStock() && !syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWENTYEIGHT;
        }

        // 实际库存-未付款数量+调入在途数量-订购量-待发货量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && !syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_TWENTYNINE;
        }

        // 实际库存-未付款数量+采购在途数量+调入在途数量-待发货量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && !syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_THIRTY;
        }

        // 实际库存-未付款数量+采购在途数量+调入在途数量-订购量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && !syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_THIRTYONE;
        }

        // 实际库存-未付款数量+采购在途数量+调入在途数量-订购量-待发货量
        if (syncNumRule.getIsActualStock() && syncNumRule.getIsUnPayNum() && syncNumRule.getIsOrderQuantity() && syncNumRule.getIsWaitSend() && syncNumRule.getIsPurchaseOnWayNum() && syncNumRule.getIsTransferOnWayNum()) {
            return ShopConfigStockSyncRuleEnum.RULE_THIRTYTWO;
        }

        // 如果没有匹配到任何规则，返回null
        return null;
    }
}
