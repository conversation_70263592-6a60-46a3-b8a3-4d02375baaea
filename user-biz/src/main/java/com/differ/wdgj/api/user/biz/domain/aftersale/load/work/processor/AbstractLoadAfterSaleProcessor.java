package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadSubTask;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.LoadAfterSaleWorkResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.business.page.PageWorkBusinessProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;

/**
 * 售后单下载工作任务的业务处理抽象
 *
 * <AUTHOR>
 * @date 2024/9/11 下午2:23
 */
public abstract class AbstractLoadAfterSaleProcessor implements PageWorkBusinessProcessor<WorkData<AfterSaleLoadArgs>, AfterSaleLoadSubTask, LoadAfterSaleWorkResult> {
    // region 变量

    /**
     * 上下文
     */
    protected AfterSaleLoadTaskContext context;

    // endregion

    // region 构造器

    AbstractLoadAfterSaleProcessor(AfterSaleLoadTaskContext context) {
        this.context = context;
    }

    // endregion

    //region 重写基类方法-业务目前无需关注
    /**
     * 业务报警（一般企业微信）
     *
     * @param subTask 子任务
     * @param alarmMsg 报警信息
     */
    @Override
    public final void onAlarm(AfterSaleLoadSubTask subTask, String alarmMsg) {

    }

    /**
     * 初始化结果
     *
     * @return 下载商品工作任务结果对象
     */
    @Override
    public final LoadAfterSaleWorkResult initResult() {
        return LoadAfterSaleWorkResult.onSuccess();
    }
    //endregion
}
