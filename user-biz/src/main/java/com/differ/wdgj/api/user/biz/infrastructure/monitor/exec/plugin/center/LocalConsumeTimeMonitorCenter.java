package com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.plugin.center;

import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.AbstractExecuteQueueMonitorCenter;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.TimeoutQueue;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.data.ExecuteEvent;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * @Description 本地执行耗时监控中心, 相关定时任务:LocalExecuteMonitorJob，设计：https://s.jkyun.biz/PtwivXO 执行监控基础组件
 * <AUTHOR>
 * @Date 2023/12/14 17:14
 */
public class LocalConsumeTimeMonitorCenter extends AbstractExecuteQueueMonitorCenter {

    private static final Logger LOG = LoggerFactory.getLogger(LocalConsumeTimeMonitorCenter.class);

    /**
     * 当完成一个任务链时，执行处理
     *
     * @param queue
     * @param success
     */
    @Override
    protected void doFinish(TimeoutQueue queue, boolean success) {
        writeLog(queue, success ? "成功" : "失败");
    }

    /**
     * 是否超时，用于外部定时任务检测
     * 注意：当超时的时候，如果不想清除队列中的数据，业务处理中不要使用queue.getQueue().poll(),应使用queue.getQueue().toArray()处理数据
     *
     * @param queue             执行队列
     * @param maxTimeoutSeconds 超时的阈值
     */
    @Override
    protected void doTimeout(TimeoutQueue queue, int maxTimeoutSeconds) {
        writeLog(queue, "超时");
    }

    /**
     * 耗时日志
     * <p>
     * 日志文本示例：
     * [耗时跟踪]-[订单下载]-[结果-失败]总耗时:9491毫秒,起止时间:2023-12-21T15:45:36.402~2023-12-21T15:45:45.893
     * [跟踪数量]被记录:4,被忽略:3
     * [耗时明细]
     * 初始化:0毫秒
     * 下载首页:3100毫秒
     * 保存入库:616毫秒
     *
     * @param queue
     * @param result
     * @return 日志
     */
    private void writeLog(TimeoutQueue queue, String result) {
        // 判断日志开关，是否需要写
        String caption = queue.getCaption();
        if (!monitorEnable(caption)) {
            return;
        }

        // 不超过最小值的不记录耗时日志，非spring测试代码：int timeoutSeconds = 1;
        int timeoutSeconds = NumberUtils.toInt(ConfigKeyUtils.getConfigValue(ConfigKeyEnum.EXEC_MONITOR_LOCAL_LOG_TIME_MIN), 1);
        if (!queue.checkTimeout(timeoutSeconds, false)) {
            return;
        }

        // 总记录数，提前记录，防止取数据后减少
        int size = queue.getSize();

        // 首条记录不能为空
        ConcurrentLinkedQueue<ExecuteEvent> queueEvent = queue.getQueue();
        ExecuteEvent executeEvent = queueEvent.poll();
        if (executeEvent == null) {
            return;
        }

        StringBuilder logContent = new StringBuilder();
        LocalDateTime firstTime = queue.getFirstTime();
        LocalDateTime finalTime = queue.getFinalTime();
        LocalDateTime lastTime = firstTime;

        // 总耗时信息
        logContent.append("[").append(queue.getCaption()).append("]-[耗时跟踪]-[结果-").append(result)
                .append("]总耗时:").append(Duration.between(firstTime, finalTime).toMillis())
                .append("毫秒,起止时间:").append(firstTime).append("~").append(finalTime)
                .append("\n[跟踪数量]被记录:").append(size)
                .append(",被忽略:").append(queue.getIgnoreCount())
                .append("\n[耗时明细]\n");

        // 明细信息
        while (executeEvent != null) {
            logContent.append(executeEvent.getEventData()).append(":").append(Duration.between(lastTime, executeEvent.getEventTime()).toMillis()).append("毫秒\n");
            lastTime = executeEvent.getEventTime();
            executeEvent = queueEvent.poll();
        }

        writeLogAdapter(caption, logContent.toString());
    }

    /**
     * 是否需要写日志
     *
     * @param caption
     * @return
     */
    @Override
    protected boolean monitorEnable(String caption) {
        // 使用日志适配器，测试时要修改，非spring测试代码 不能用LogAdapter
        return LogFactory.isInfoEnabled(caption);
    }

    /**
     * 适配器执行写日志
     *
     * @param caption
     * @param logContent
     * @return
     */
    protected void writeLogAdapter(String caption, String logContent) {
        // 使用日志适配器，测试时要修改，非spring测试代码 不能用LogAdapter，
        LogFactory.get(caption).info(logContent);
    }
}
