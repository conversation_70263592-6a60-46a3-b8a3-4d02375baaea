package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

/**
 * 仓库匹配数据
 * 表g_api_cfg_warehouselist
 *
 * <AUTHOR>
 * @date 2024/12/19 下午7:17
 */
public class ApiCfgWarehouseListDO {
    /**
     * 管家店铺Id
     */
    private int shopId;

    /**
     * 管家仓库ID
     */
    private int wareHouseId;

    /**
     * 平台仓库名称
     */
    private String platWarehouseName;

    /**
     * 平台仓库编码
     */
    private String platWarehouseCode;

    /**
     * 备用字段 1
     */
    private String reserved1;

    /**
     * 备用字段 2
     */
    private String reserved2;

    /**
     * 备用字段 3
     */
    private String reserved3;

    /**
     * 备用字段 4
     */
    private String reserved4;

    /**
     * 备用字段 5
     */
    private String reserved5;

    //region get/set
    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public int getWareHouseId() {
        return wareHouseId;
    }

    public void setWareHouseId(int wareHouseId) {
        this.wareHouseId = wareHouseId;
    }

    public String getPlatWarehouseName() {
        return platWarehouseName;
    }

    public void setPlatWarehouseName(String platWarehouseName) {
        this.platWarehouseName = platWarehouseName;
    }

    public String getPlatWarehouseCode() {
        return platWarehouseCode;
    }

    public void setPlatWarehouseCode(String platWarehouseCode) {
        this.platWarehouseCode = platWarehouseCode;
    }

    public String getReserved1() {
        return reserved1;
    }

    public void setReserved1(String reserved1) {
        this.reserved1 = reserved1;
    }

    public String getReserved2() {
        return reserved2;
    }

    public void setReserved2(String reserved2) {
        this.reserved2 = reserved2;
    }

    public String getReserved3() {
        return reserved3;
    }

    public void setReserved3(String reserved3) {
        this.reserved3 = reserved3;
    }

    public String getReserved4() {
        return reserved4;
    }

    public void setReserved4(String reserved4) {
        this.reserved4 = reserved4;
    }

    public String getReserved5() {
        return reserved5;
    }

    public void setReserved5(String reserved5) {
        this.reserved5 = reserved5;
    }
    //endregion
}
