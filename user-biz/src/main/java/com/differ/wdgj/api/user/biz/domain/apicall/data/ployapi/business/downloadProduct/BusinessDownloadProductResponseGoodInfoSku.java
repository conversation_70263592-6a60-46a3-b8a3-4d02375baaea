package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.downloadProduct;

import java.math.BigDecimal;

/**
 * 菠萝派下载商品 规格级返回对象
 *
 * <AUTHOR>
 * @date 2024-03-22 9:49
 */
public class BusinessDownloadProductResponseGoodInfoSku {
    /**
    * 规格ID
    */
    private int goodsSkuId;

    /**
    * 平台规格ID
    */
    private String skuId;

    /**
    * 规格外部商家编码
    */
    private String skuOuterId;

    /**
    * 规格价格
    */
    private BigDecimal skuPrice;

    /**
    * 规格数量
    */
    private int skuQuantity;

    /**
    * 规格名称
    */
    private String skuName;

    /**
    * 规格名称（菠萝派尚未定义SkuName2）
    */
    private String skuName2;

    /**
    * 规格属性
    */
    private String skuProperty;

    /**
    * 规格图片URL
    */
    private String skuPictureUrl;

    //region get/set
    public int getGoodsSkuId() {
        return goodsSkuId;
    }

    public void setGoodsSkuId(int goodsSkuId) {
        this.goodsSkuId = goodsSkuId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getSkuOuterId() {
        return skuOuterId;
    }

    public void setSkuOuterId(String skuOuterId) {
        this.skuOuterId = skuOuterId;
    }

    public BigDecimal getSkuPrice() {
        return skuPrice;
    }

    public void setSkuPrice(BigDecimal skuPrice) {
        this.skuPrice = skuPrice;
    }

    public int getSkuQuantity() {
        return skuQuantity;
    }

    public void setSkuQuantity(int skuQuantity) {
        this.skuQuantity = skuQuantity;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getSkuName2() {
        return skuName2;
    }

    public void setSkuName2(String skuName2) {
        this.skuName2 = skuName2;
    }

    public String getSkuProperty() {
        return skuProperty;
    }

    public void setSkuProperty(String skuProperty) {
        this.skuProperty = skuProperty;
    }

    public String getSkuPictureUrl() {
        return skuPictureUrl;
    }

    public void setSkuPictureUrl(String skuPictureUrl) {
        this.skuPictureUrl = skuPictureUrl;
    }
    //endregion
}
