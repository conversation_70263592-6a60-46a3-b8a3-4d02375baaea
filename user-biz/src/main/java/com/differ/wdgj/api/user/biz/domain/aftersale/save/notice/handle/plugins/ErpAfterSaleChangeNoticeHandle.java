package com.differ.wdgj.api.user.biz.domain.aftersale.save.notice.handle.plugins;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleOutNoticeTypeEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.handle.TargetErpAfterSaleChangeItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.notice.handle.AbstractAfterSaleNotice;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.utils.AfterSaleLogUtils;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnLogDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiReturnLogMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.WdgjStoredProcedureMapper;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 售后单变更通知erp插件
 *
 * <AUTHOR>
 * @date 2024-07-05 11:57
 */
public class ErpAfterSaleChangeNoticeHandle extends AbstractAfterSaleNotice {
    //region 常量
    /**
     * 售后单日志仓储
     */
    private final WdgjStoredProcedureMapper wdgjStoredProcedureMapper = BeanContextUtil.getBean(WdgjStoredProcedureMapper.class);

    /**
     * 售后单日志仓储
     */
    private final ApiReturnLogMapper afterSaleLogMapper = BeanContextUtil.getBean(ApiReturnLogMapper.class);

    /**
     * 需要通知的售后单变化
     */
    private final Set<TargetErpAfterSaleChangeItem> afterSaleChangeTypes;

    /**
     * 售后单id
     */
    private final int billId;
    //endregion

    //region 构造
    /**
     * 构造
     *
     * @param context              上下文
     * @param afterSaleNo          售后单号
     * @param billId               售后单id
     * @param afterSaleChangeTypes 需要通知的售后单变化
     */
    public ErpAfterSaleChangeNoticeHandle(AfterSaleSaveContext context, String afterSaleNo, int billId, Set<TargetErpAfterSaleChangeItem> afterSaleChangeTypes) {
        super(context, afterSaleNo);
        this.afterSaleChangeTypes = afterSaleChangeTypes;
        this.billId = billId;
    }
    //endregion

    /**
     * 获取通知类别
     *
     * @return 通知类别
     */
    @Override
    public AfterSaleOutNoticeTypeEnum getNoticeType() {
        return AfterSaleOutNoticeTypeEnum.ERP_AFTER_SALE_CHANGE;
    }

    /**
     * 处理通知
     */
    @Override
    protected void process() {
        // 不为空通知
        if (CollectionUtils.isEmpty(afterSaleChangeTypes)) {
            return;
        }

        // 构建通知描述
        String noticeTypeValues = afterSaleChangeTypes.stream().map(x -> x.getChangeType().getValue().toString()).collect(Collectors.joining(","));
        String noticeTypeNames = afterSaleChangeTypes.stream().map(x -> x.getChangeInfo()).collect(Collectors.joining(";"));

        // 构建售后单日志
        String operator = AfterSaleLogUtils.getOperatorFromContext(context);
        ApiReturnLogDO afterSaleLog = AfterSaleLogUtils.covertLog(billId, operator, String.format("售后单变化通知ERP成功, 变更内容：%s。", noticeTypeNames));

        // 调用云端存储过程，未报错视为成功
        DBSwitchUtil.doDBWithUser(context.getMemberName(), () ->{
            wdgjStoredProcedureMapper.afterSaleChangeNotice(billId, noticeTypeValues);
            afterSaleLogMapper.batchInsert(Collections.singletonList(afterSaleLog));
        });

    }
}
