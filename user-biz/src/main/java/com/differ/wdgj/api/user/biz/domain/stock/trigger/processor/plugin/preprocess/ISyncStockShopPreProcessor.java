package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plugin.preprocess;

import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plugin.ISyncStockPlugin;

/**
 * 库存同步-业务处理-店铺级前置处理接口
 *
 * <AUTHOR>
 * @date 2024-05-22 16:20
 */
public interface ISyncStockShopPreProcessor extends ISyncStockPlugin {

    /**
     * 库存同步前置处理
     *
     * @param context    上下文
     * @param goodsMatch 商品匹配数据
     * @return 处理结果
     */
    StockContentResult<?> preProcess(StockSyncContext context, GoodsMatchEnhance goodsMatch);

    /**
     * 是否支持当前商品匹配
     *
     * @param context    上下文
     * @param goodsMatch 商品匹配数据
     * @return 是否支持
     */
    boolean supports(StockSyncContext context, GoodsMatchEnhance goodsMatch);
}
