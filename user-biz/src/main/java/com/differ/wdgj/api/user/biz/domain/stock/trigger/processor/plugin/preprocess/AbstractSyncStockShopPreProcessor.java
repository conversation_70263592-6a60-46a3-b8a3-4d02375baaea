package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.plugin.preprocess;

import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 库存同步前置处理抽象基类
 *
 * <AUTHOR>
 * @date 2024-05-22 16:25
 */
public abstract class AbstractSyncStockShopPreProcessor implements ISyncStockShopPreProcessor {

    protected static final Logger log = LoggerFactory.getLogger(AbstractSyncStockShopPreProcessor.class);

    // region 接口实现

    /**
     * 库存同步前置处理
     *
     * @param context    上下文
     * @param goodsMatch 商品匹配数据
     * @return 处理结果
     */
    @Override
    public StockContentResult<?> preProcess(StockSyncContext context, GoodsMatchEnhance goodsMatch) {
        try {
            // 检查是否支持当前商品匹配
            if (!this.supports(context, goodsMatch)) {
                return StockContentResult.success();
            }

            // 执行前置处理
            return this.doPreProcess(context, goodsMatch);

        } catch (Exception e) {
            String message = String.format("【%s】库存同步前置处理失败-%s，原因：%s",
                    context.getVipUser(), caption(), e.getMessage());
            log.error(message, e);
            return StockContentResult.failed(message);
        }
    }

    /**
     * 获取插件名称
     *
     * @return 插件名称
     */
    @Override
    public String getName() {
        return caption();
    }

    /**
     * 是否支持当前商品匹配
     * 默认支持所有商品匹配，子类可以重写此方法进行筛选
     *
     * @param context    上下文
     * @param goodsMatch 商品匹配数据
     * @return 是否支持
     */
    @Override
    public boolean supports(StockSyncContext context, GoodsMatchEnhance goodsMatch) {
        return true;
    }

    // endregion

    // region 供子类重写

    /**
     * 执行前置处理
     *
     * @param context    上下文
     * @param goodsMatch 商品匹配数据
     * @return 处理结果
     */
    protected abstract StockContentResult<?> doPreProcess(StockSyncContext context, GoodsMatchEnhance goodsMatch);

    /**
     * 处理器标题
     *
     * @return 标题
     */
    protected abstract String caption();

    // endregion
}
