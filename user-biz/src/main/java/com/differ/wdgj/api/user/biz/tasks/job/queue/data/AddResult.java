package com.differ.wdgj.api.user.biz.tasks.job.queue.data;

/**
 * 均衡队列任务的添加结果
 *
 * <AUTHOR>
 * @date 2024/4/17 19:37
 */
public class AddResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 失败原因
     */
    private String message;

    /**
     * 成功结果
     *
     * @return
     */
    public static AddResult successResult() {
        return new AddResult(true, "");
    }

    /**
     * 成功结果
     *
     * @return
     */
    public static AddResult failResult(String message) {
        return new AddResult(false, message);
    }

    /**
     * 任务添加结果
     * @param success
     * @param message
     */
    private AddResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    /**
     * 是否成功
     *
     * @return
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * 失败原因
     *
     * @return
     */
    public String getMessage() {
        return message;
    }
}
