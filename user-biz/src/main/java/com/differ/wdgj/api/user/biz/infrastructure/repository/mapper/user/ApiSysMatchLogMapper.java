package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchLogDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.BasicOperateMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-11 13:19
 */
public interface ApiSysMatchLogMapper  extends BasicOperateMapper<ApiSysMatchLogDO> {

    // region 新增

    /**
     * 批量新增商品匹配日志
     *
     * @param ApiSysMatchLogs 商品匹配日志集合
     * @return 返回影响行数
     */
    int batchAddApiSysMatchLogs(@Param("ApiSysMatchLogs") List<ApiSysMatchLogDO> ApiSysMatchLogs);

    // endregion

}
