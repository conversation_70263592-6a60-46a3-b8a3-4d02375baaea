package com.differ.wdgj.api.user.biz.domain.apicall.data.core;

import java.io.Serializable;


/**
 * 业务请求数据返回基类
 *
 * <AUTHOR>
 * @since 2024-08-26 9:10:01
 */
public abstract class BasePlatResponseBizData implements Serializable {
    /**
     * 菠萝派日志Id。
     */
    private String polyApiRequestId;
    /**
     * 是否成功
     */
    private Boolean isSuccess;
    /**
     * 返回消息
     */
    private String msg;
    /**
     * 返回码
     */
    private String code;
    /**
     * 返回子级消息
     */
    private String subMessage;
    /**
     * 返回子码
     */
    private String subCode;

    //region get/set
    public String getPolyApiRequestId() {
        return polyApiRequestId;
    }

    public void setPolyApiRequestId(String polyApiRequestId) {
        this.polyApiRequestId = polyApiRequestId;
    }

    public Boolean getSuccess() {
        return isSuccess;
    }

    public void setSuccess(Boolean success) {
        isSuccess = success;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSubMessage() {
        return subMessage;
    }

    public void setSubMessage(String subMessage) {
        this.subMessage = subMessage;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }
    //endregion
}
