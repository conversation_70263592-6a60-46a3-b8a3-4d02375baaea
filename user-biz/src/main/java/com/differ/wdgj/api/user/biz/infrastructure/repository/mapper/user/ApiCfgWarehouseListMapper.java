package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiCfgWarehouseListDO;

import java.util.List;

/**
 * 仓库匹配仓储
 *
 * <AUTHOR>
 * @date 2024/12/19 下午7:21
 */
public interface ApiCfgWarehouseListMapper {
    /**
     * 根据shopId查询仓库匹配信息
     *
     * @param shopId 店铺Id
     * @return 仓库匹配信息列表
     */
    List<ApiCfgWarehouseListDO> selectByShopId(int shopId);
}
