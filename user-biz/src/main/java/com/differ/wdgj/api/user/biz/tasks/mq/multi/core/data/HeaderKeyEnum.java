package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.data;

import java.util.ArrayList;
import java.util.List;

/**
 * 多队列头信息key
 *
 * <AUTHOR>
 * @date 2024/4/3 17:28
 */
public enum HeaderKeyEnum {
    /**
     * 吉客号
     */
    JACK_NO("jack_no"),
    /**
     * 日志ID
     */
    LOGGER_SN("loggerSN"),
    /**
     * 消息处理类型
     */
    HANDLER_CODE("custom_handler_code"),
    /**
     * 重试次数
     */
    RETRY_COUNT("custom_retry_count"),
    /**
     * 创建时间
     */
    CREATE_TIME("custom_create_time"),
    /**
     * 消息延迟级别
     */
    DELAY_GRADE("custom_delay_grade"),
    /**
     * 业务数据平台
     */
    DATA_PLAT("custom_data_plat"),
    /**
     * 业务数据类型
     */
    DATA_TYPE("custom_data_type"),
    /**
     * 消息数据库主键ID，只有当数据库子队列时才有赋值
     */
    DB_ID("custom_db_id"),
    /**
     * 店铺ID
     */
    DATE_SHOP_ID("custom_data_shop_id"),
    ;

    /**
     * key
     */
    private String key;

    HeaderKeyEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public static List<String> getKeys() {
        List<String> allKeys = new ArrayList<>();
        for (HeaderKeyEnum headerKeyEnum : HeaderKeyEnum.values()) {
            allKeys.add(headerKeyEnum.key);
        }
        return allKeys;
    }
}
