package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.core;

import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncBuildRequestResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncDoRequestResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncGoodsRequestPair;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncMatchExecuteResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncMatchOutResultDetail;

import java.util.List;

/**
 * 库存同步-业务处理接口
 *
 * <AUTHOR>
 * @date 2024-02-23 15:10
 */
public interface ISyncStockProcessor {
    /**
     * 构建库存同步请求
     *
     * @param goodsMatches 商品匹配列表
     * @return 库存同步请求信息
     */
    StockSyncBuildRequestResult buildSyncStockRequests(List<GoodsMatchEnhance> goodsMatches);

    /**
     * 发起菠萝派库存同步
     *
     * @param requestGoodsItems 请求商品列表
     * @return 请求结果
     */
    StockSyncDoRequestResult doRequest(List<StockSyncGoodsRequestPair> requestGoodsItems);

    /**
     * 3、保存库存同步结果
     *
     * @param executeResults 库存同步执行结果
     * @return 结果
     */
    List<StockSyncMatchOutResultDetail> saveSyncStockResult(List<StockSyncMatchExecuteResult> executeResults);
}
