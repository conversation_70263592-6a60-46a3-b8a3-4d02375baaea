package com.differ.wdgj.api.user.biz.infrastructure.dbsafe.query;

import java.util.List;

/**
 * 数据库安全操作-增量分页查询
 *
 * <AUTHOR>
 * @date 2024-11-27 16:39
 */
public interface IDbSafeIncrementPageQuery<T> {

    /**
     * 获取增量 ID
     *
     * @param entity 实体
     * @return 增量 ID
     */
    Long getIncrementId(T entity);

    /**
     * 分页查询
     *
     * @param pageSize    分页大小
     * @param incrementId 增量 ID
     * @return 查询结果
     */
    List<T> pageQuery(Integer pageSize, Long incrementId);

    /**
     * 执行业务
     *
     * @param entities 实体
     * @return 是否继续执行
     */
    boolean execBiz(List<T> entities);
}