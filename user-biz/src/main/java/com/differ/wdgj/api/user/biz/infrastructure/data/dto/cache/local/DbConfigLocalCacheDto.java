package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local;

/**
 * 内存缓存传输对象 - 配置键
 *
 * <AUTHOR>
 * @date 2024-03-12 10:30
 */
public class DbConfigLocalCacheDto {
    /**
     * 获取或设置 配置键。
     */
    private String configKey;

    /**
     * 获取或设置 配置版本号。
     */
    private String version;

    /**
     * 获取或设置 ConfigTypes枚举值之一[Api=1;]。
     */
    private int configType;

    /**
     * 获取或设置 配置值。
     */
    private String value;


    //region get/set
    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public int getConfigType() {
        return configType;
    }

    public void setConfigType(int configType) {
        this.configType = configType;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
    //endregion
}
