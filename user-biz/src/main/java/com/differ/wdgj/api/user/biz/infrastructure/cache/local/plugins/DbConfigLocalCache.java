package com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins;

import com.differ.wdgj.api.user.biz.infrastructure.cache.local.core.AbstractLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.DbConfigCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.GloDbConfigDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.DbConfigKey;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.DbConfigLocalCacheDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ConfigKeyTypeEnum;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.BeanUtils;

import java.util.concurrent.TimeUnit;

/**
 * 内存缓存：配置键
 *
 * <AUTHOR>
 * @date 2024-03-11 19:44
 */
public class DbConfigLocalCache extends AbstractLocalCache<DbConfigKey, DbConfigLocalCacheDto> {

    //region 单例

    private DbConfigLocalCache(){
        // 缓存有效时间-分钟
        this.expire = RandomUtils.nextInt(1, 2);
        this.timeUnit = TimeUnit.MINUTES;
    }

    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static DbConfigLocalCache singleton() {
        return DbConfigLocalCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final DbConfigLocalCache instance;

        private SingletonEnum() {
            instance = new DbConfigLocalCache();
        }
    }

    //endregion

    //region 常量

    /**
     * 配置键版本号
     * 没有实际逻辑，直接写死
     */
    private final String version = "V1";

    //endregion

    //region 重写基类方法

    /**
     * 当缓存不存在时，会调用此函数来加载数据源
     *
     * @param key 配置键key
     * @return 配置键内存缓存实体
     */
    @Override
    protected DbConfigLocalCacheDto loadSource(DbConfigKey key) {
        GloDbConfigDO dbConfigDO= DbConfigCache.singleton().getData(key.getConfigKey(), key.getConfigType(), key.getVersion());
        DbConfigLocalCacheDto dbConfigLocalCache = new DbConfigLocalCacheDto();
        if(dbConfigDO != null){
            BeanUtils.copyProperties(dbConfigDO, dbConfigLocalCache);
        }
        else{
            dbConfigLocalCache.setConfigKey(key.getConfigKey().getName());
            dbConfigLocalCache.setConfigType(key.getConfigType().getValue());
            dbConfigLocalCache.setVersion(key.getVersion());
            dbConfigLocalCache.setValue(key.getConfigKey().getDefaultValue());
        }

        return dbConfigLocalCache;
    }

    //endregion

    //region 增强方法

    /**
     * 获取配置键
     * @param configKey 配置键
     * @param configType 配置键分类
     * @return 配置键缓存实体
     */
    public String getConfigValue(ConfigKeyEnum configKey, ConfigKeyTypeEnum configType){
        DbConfigLocalCacheDto localCache = getData(configKey, configType);
        if(localCache == null){
            return "";
        }
        return localCache.getValue();
    }

    /**
     * 获取配置键
     * @param configKey 配置键
     * @param configType 配置键分类
     * @return 配置键缓存实体
     */
    public DbConfigLocalCacheDto getData(ConfigKeyEnum configKey, ConfigKeyTypeEnum configType){
        return getData(configKey, configType, version);
    }

    /**
     * 异步获取配置键
     * 存在循环调用逻辑时使用，例如log
     * @param configKey 配置键
     * @param configType 配置键分类
     * @return 配置键缓存实体
     */
    public String asyncGetConfigValue(ConfigKeyEnum configKey, ConfigKeyTypeEnum configType){
        DbConfigLocalCacheDto localCache = asyncGetData(configKey, configType);
        if(localCache == null){
            return "";
        }
        return localCache.getValue();
    }

    /**
     * 异步获取配置键
     * 存在循环调用逻辑时使用，例如log
     * @param configKey 配置键
     * @param configType 配置键分类
     * @return 配置键缓存实体
     */
    public DbConfigLocalCacheDto asyncGetData(ConfigKeyEnum configKey, ConfigKeyTypeEnum configType){

        DbConfigKey localCacheKey = new DbConfigKey();
        localCacheKey.setConfigKey(configKey);
        localCacheKey.setConfigType(configType);
        localCacheKey.setVersion(version);

        return getCacheAsyncSource(localCacheKey);
    }

    /**
     * 获取配置键
     * @param configKey 配置键
     * @param configType 配置键分类
     * @param version 版本号
     * @return 配置键缓存实体
     */
    public DbConfigLocalCacheDto getData(ConfigKeyEnum configKey, ConfigKeyTypeEnum configType, String version){
        DbConfigKey localCacheKey = new DbConfigKey();
        localCacheKey.setConfigKey(configKey);
        localCacheKey.setConfigType(configType);
        localCacheKey.setVersion(version);
        return getCacheThenSource(localCacheKey);
    }

    //endregion
}
