package com.differ.wdgj.api.user.biz.infrastructure.utils.shop;

import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop.ApiShopConfigLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopConfigLocalDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;

/**
 * 会员店铺配置工具类
 *
 * <AUTHOR>
 * @date 2024-06-26 13:51
 */
public class ShopConfigUtils {
    //region 构造
    private ShopConfigUtils() {}
    //endregion

    /**
     * 获取店铺配置
     *
     * @param outAccount 外部会员名
     * @param bizType    业务类型
     * @param shopId     店铺id
     * @return 店铺配置
     */
    public static Object getBizConfig(String outAccount, ApiShopConfigBizTypes bizType, int shopId) {
        ApiShopConfigLocalDto config = ApiShopConfigLocalCache.singleton().getConfig(outAccount, bizType, shopId);
        if(config != null){
            return config.getConfigValue();
        }
        return null;
    }

}
