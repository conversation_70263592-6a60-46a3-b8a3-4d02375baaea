package com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 手动下载售后单业务参数
 *
 * <AUTHOR>
 * @date 2024/10/28 下午6:27
 */
public class HandGetAfterSaleRequest {
    /**
     * 是否选择时间下载。
     */
    private boolean isDownLoadByTime;

    /**
     * 开始时间
     */
    private LocalDateTime startTm;

    /**
     * 结束时间
     */
    private LocalDateTime endTm;

    /**
     * 店铺类型
     */
    private String shopType;

    /**
     * 售后单类型
     */
    private ApiAfterSaleTypeEnum refundType;

    /**
     * 售后单号
     */
    private List<String> afterSaleNos;

    //region get/set

    public boolean getIsDownLoadByTime() {
        return isDownLoadByTime;
    }

    public void setIsDownLoadByTime(boolean downLoadByTime) {
        isDownLoadByTime = downLoadByTime;
    }

    public LocalDateTime getStartTm() {
        return startTm;
    }

    public void setStartTm(LocalDateTime startTm) {
        this.startTm = startTm;
    }

    public LocalDateTime getEndTm() {
        return endTm;
    }

    public void setEndTm(LocalDateTime endTm) {
        this.endTm = endTm;
    }

    public String getShopType() {
        return shopType;
    }

    public void setShopType(String shopType) {
        this.shopType = shopType;
    }

    public ApiAfterSaleTypeEnum getRefundType() {
        return refundType;
    }

    public void setRefundType(ApiAfterSaleTypeEnum refundType) {
        this.refundType = refundType;
    }

    public List<String> getAfterSaleNos() {
        return afterSaleNos;
    }

    public void setAfterSaleNos(List<String> afterSaleNos) {
        this.afterSaleNos = afterSaleNos;
    }

    //endregion
}
