package com.differ.wdgj.api.user.biz.infrastructure.repository.core;

import com.differ.wdgj.api.component.multidb.DataSourceContextHolder;
import com.differ.wdgj.api.component.multidb.annotation.DataSourceAutoSwitcher;
import com.differ.wdgj.api.component.util.functional.zero.Action;
import com.differ.wdgj.api.component.util.functional.zero.Function;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;


/**
 * 数据库自动切换标记的切面，注解： {@link DataSourceAutoSwitcher}
 *
 * <AUTHOR>
 * @date 2020-07-10 10:38
 */
@Aspect
@Component
@Order(1)
public class DBSwitchUtil {

    private static final Logger LOG = LoggerFactory.getLogger(DBSwitchUtil.class);
    /**
     * 执行数据库操作
     *
     * @param user 会员名
     * @param fun lambada表达式
     */
    public static void doDBWithUser(String user, Action fun) {
        try {
            SwitchDbContext context = new SwitchDbContext(SwitcherDBTypeEnum.WDGJ, user);
            DataSourceContextHolder.set(context);
            fun.exec();
        } finally {
            //清除上下文资源
            DataSourceContextHolder.remove();
        }
    }

    /**
     * 执行数据库操作
     *
     * @param user 会员名
     * @param fun lambada表达式
     */
    public static <R> R doDBWithUser(String user, Function<R> fun) {
        try {
            SwitchDbContext context = new SwitchDbContext(SwitcherDBTypeEnum.WDGJ, user);
            DataSourceContextHolder.set(context);
            return fun.exec();
        } finally {
            //清除上下文资源
            DataSourceContextHolder.remove();
        }
    }
    /**
     * 执行数据库操作
     *
     * @param user 会员名
     * @param fun lambada表达式
     */
    public static void doDBWithRds(String user, Action fun) {
        try {
            SwitchDbContext context = new SwitchDbContext(SwitcherDBTypeEnum.RDS_PUSH, user);
            DataSourceContextHolder.set(context);
            fun.exec();
        } finally {
            //清除上下文资源
            DataSourceContextHolder.remove();
        }
    }

    /**
     * 执行数据库操作
     *
     * @param user 会员名
     * @param fun lambada表达式
     */
    public static <R> R doDBWithRds(String user, Function<R> fun) {
        try {
            SwitchDbContext context = new SwitchDbContext(SwitcherDBTypeEnum.RDS_PUSH, user);
            DataSourceContextHolder.set(context);
            return fun.exec();
        } finally {
            //清除上下文资源
            DataSourceContextHolder.remove();
        }
    }

    /**
     * 执行数据库操作
     *
     * @param context
     * @param fun
     */
    public static void doDBWithContext(SwitchDbContext context, Action fun) {
        try {
            DataSourceContextHolder.set(context);
            fun.exec();
        } finally {
            //清除上下文资源
            DataSourceContextHolder.remove();
        }
    }

    /**
     * 执行数据库操作
     *
     * @param context
     * @param fun
     */
    public static <R> R doDBWithContext(SwitchDbContext context, Function<R> fun) {
        try {
            DataSourceContextHolder.set(context);
            return fun.exec();
        } finally {
            //清除上下文资源
            DataSourceContextHolder.remove();
        }
    }

    /**
     *  执行数据库事务操作
     *  事务回滚条件：
     *    1. 方法执行过程中抛出异常
     *    2. 方法执行过程中返回false
     * @param user 会员名
     * @param fun
     * @return
     */
    public static boolean doTransaction(String user, Function<Boolean> fun){
        return doTransaction(user, fun, false);
    }

    /**
     *  执行数据库事务操作
     *  事务回滚条件：
     *    1. 方法执行过程中抛出异常
     *    2. 方法执行过程中返回false
     * @param user 会员名
     * @param fun
     * @return
     */
    public static boolean doTransaction(String user, Function<Boolean> fun, boolean isThrowException){
        SwitchDbContext context = new SwitchDbContext(SwitcherDBTypeEnum.WDGJ, user);
        return doTransaction(context, fun, isThrowException);
    }

    /**
     *  执行数据库事务操作
     *  事务回滚条件：
     *    1. 方法执行过程中抛出异常
     *    2. 方法执行过程中返回false
     * @param context
     * @param fun
     * @return
     */
    public static boolean doTransaction(SwitchDbContext context, Function<Boolean> fun){
        return doTransaction(context, fun, false);
    }

    /**
     *  执行数据库事务操作
     *  事务回滚条件：
     *    1. 方法执行过程中抛出异常
     *    2. 方法执行过程中返回false
     * @param context
     * @param fun
     * @param isThrowException 异常回滚后是否继续抛出异常
     * @return
     */
    public static boolean doTransaction(SwitchDbContext context, Function<Boolean> fun, boolean isThrowException) {
        try {
            // 事务执行前必须绑定上下文，否则事务会找不到连接
            DataSourceContextHolder.set(context);
            return BeanContextUtil.getBean(TransactionTemplate.class).execute((status) -> {
                try {
                    Boolean commit = fun.exec();
                    if (!Boolean.TRUE.equals(commit)) {
                        status.setRollbackOnly();
                        return false;
                    }
                } catch (Throwable t) {
                    LOG.error("sql事务异常回滚", t);
                    status.setRollbackOnly();
                    // 如果执行异常并且需要抛出异常时抛出异常
                    if (isThrowException) {
                        throw t;
                    }
                    return false;
                }
                return true;
            });
        }finally {
            //清除上下文资源
            DataSourceContextHolder.remove();
        }
    }
}
