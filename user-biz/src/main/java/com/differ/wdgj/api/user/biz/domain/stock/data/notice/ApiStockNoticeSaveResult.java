package com.differ.wdgj.api.user.biz.domain.stock.data.notice;

/**
 * api匹配库存变动通知转换结果
 *
 * <AUTHOR>
 * @date 2024/11/12 下午6:06
 */
public class ApiStockNoticeSaveResult {
    /**
     * 转换"api匹配库存变动通知"数量
     */
    private Integer apiStockNoticeCount;

    //region 静态方法
    /**
     * 静态构造方法
     *
     * @param apiStockNoticeCount 转换"api匹配库存变动通知"数量
     * @return 结果
     */
    public static ApiStockNoticeSaveResult of(Integer apiStockNoticeCount) {
        ApiStockNoticeSaveResult result = new ApiStockNoticeSaveResult();
        result.setApiStockNoticeCount(apiStockNoticeCount);
        return result;
    }
    //endregion

    //region get/set
    public Integer getApiStockNoticeCount() {
        return apiStockNoticeCount;
    }

    public void setApiStockNoticeCount(Integer apiStockNoticeCount) {
        this.apiStockNoticeCount = apiStockNoticeCount;
    }

    //endregion
}
