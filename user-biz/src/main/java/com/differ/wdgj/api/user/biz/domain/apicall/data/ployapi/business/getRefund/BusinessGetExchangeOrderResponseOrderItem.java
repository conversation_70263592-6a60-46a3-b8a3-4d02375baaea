package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund;

import com.alibaba.fastjson.annotation.JSONField;
import com.differ.wdgj.api.component.util.json.core.StringDateTimeDeserializer;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyExchangeStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 菠萝派下载换货单（Differ.JH.Business.GetExchange 节点exchanges）
 *
 * <AUTHOR>
 * @date 2024/8/7 上午11:18
 */
public class BusinessGetExchangeOrderResponseOrderItem {
    /**
     * 买家发货物流单号
     */
    private String buyerLogisticNo;

    /**
     * 卖家发货物流单号
     */
    private String sellerLogisticNo;

    /**
     * 物流单号
     */
    private String logisticNo;

    /**
     * 订单号
     */
    private String platOrderNo;

    /**
     * 子订单号
     */
    private String platSubOrderNo;

    /**
     * 换货单
     */
    private String exchangeOrderNo;

    /**
     * 退款金额（抖音等平台换货单最终不发货，走退货退款）
     */
    private BigDecimal refundAmount;

    /**
     * 换货状态
     * {@link PolyExchangeStatusEnum}
     */
    private String orderStatus;

    /**
     * 换货申请理由
     */
    private String reason;

    /**
     * 换货理由说明
     */
    private String desc;

    /**
     * 换货单创建时间
     */
    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime createTime;

    /**
     * 换货单修改时间
     */
    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime updateTime;

    /**
     * 推送库创建时间
     */
    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime rdsCreateTime;

    /**
     * 推送库更新时间
     */
    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime rdsModifyTime;

    /**
     * 买家昵称
     */
    private String buyerNick;

    /**
     * 卖家昵称
     */
    private String sellerNick;

    /**
     * 支付费用
     */
    private BigDecimal payment;

    /**
     * 买家发货物流公司名称
     */
    private String buyerLogisticName;

    /**
     * 卖家发货物流公司名称
     */
    private String sellerLogisticName;

    /**
     * 买家联系方式
     */
    private String buyerPhone;

    /**
     * 买家姓名
     */
    private String buyerName;

    /**
     * 买家换货地址
     */
    private String buyerAddress;

    /**
     * 买家收件省份
     */
    private String province;

    /**
     * 买家收件城市
     */
    private String city;

    /**
     * 买家收件区县
     */
    private String area;

    /**
     * 买家收件镇/街道
     */
    private String town;

    /**
     * 买家收货详细地址
     */
    private String address;

    /**
     * 卖家收件地址
     */
    private String sellerAddress;

    /**
     * 卖家收退货的地址id
     */
    private String sellerReceiveAddressId;

    /**
     * oaid
     */
    private String oaid;

    /**
     * 新版换货标识
     */
    private String NewExchangeRepair;

    /**
     * 是否成功
     */
    private String isSuccess;

    /**
     * 菠萝派返回子编码
     */
    private String subCode;

    /**
     * 菠萝派返回子信息
     */
    private String subMessage;

    /**
     * 买家退回商品信息集合
     */
    private List<BusinessGetExchangeResponseRefundGoodInfo> refundGoods;

    /**
     * 买家换出商品信息集合
     */
    private List<BusinessGetExchangeResponseExchangeGoodInfo> exchangeGoods;


    //region get/set
    public String getBuyerLogisticNo() {
        return buyerLogisticNo;
    }

    public void setBuyerLogisticNo(String buyerLogisticNo) {
        this.buyerLogisticNo = buyerLogisticNo;
    }

    public String getSellerLogisticNo() {
        return sellerLogisticNo;
    }

    public void setSellerLogisticNo(String sellerLogisticNo) {
        this.sellerLogisticNo = sellerLogisticNo;
    }

    public String getLogisticNo() {
        return logisticNo;
    }

    public void setLogisticNo(String logisticNo) {
        this.logisticNo = logisticNo;
    }

    public String getPlatOrderNo() {
        return platOrderNo;
    }

    public void setPlatOrderNo(String platOrderNo) {
        this.platOrderNo = platOrderNo;
    }

    public String getPlatSubOrderNo() {
        return platSubOrderNo;
    }

    public void setPlatSubOrderNo(String platSubOrderNo) {
        this.platSubOrderNo = platSubOrderNo;
    }

    public String getExchangeOrderNo() {
        return exchangeOrderNo;
    }

    public void setExchangeOrderNo(String exchangeOrderNo) {
        this.exchangeOrderNo = exchangeOrderNo;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public BigDecimal getPayment() {
        return payment;
    }

    public void setPayment(BigDecimal payment) {
        this.payment = payment;
    }

    public String getBuyerLogisticName() {
        return buyerLogisticName;
    }

    public void setBuyerLogisticName(String buyerLogisticName) {
        this.buyerLogisticName = buyerLogisticName;
    }

    public String getSellerLogisticName() {
        return sellerLogisticName;
    }

    public void setSellerLogisticName(String sellerLogisticName) {
        this.sellerLogisticName = sellerLogisticName;
    }

    public String getBuyerPhone() {
        return buyerPhone;
    }

    public void setBuyerPhone(String buyerPhone) {
        this.buyerPhone = buyerPhone;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getBuyerAddress() {
        return buyerAddress;
    }

    public void setBuyerAddress(String buyerAddress) {
        this.buyerAddress = buyerAddress;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getSellerAddress() {
        return sellerAddress;
    }

    public void setSellerAddress(String sellerAddress) {
        this.sellerAddress = sellerAddress;
    }

    public String getSellerReceiveAddressId() {
        return sellerReceiveAddressId;
    }

    public void setSellerReceiveAddressId(String sellerReceiveAddressId) {
        this.sellerReceiveAddressId = sellerReceiveAddressId;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public List<BusinessGetExchangeResponseRefundGoodInfo> getRefundGoods() {
        return refundGoods;
    }

    public void setRefundGoods(List<BusinessGetExchangeResponseRefundGoodInfo> refundGoods) {
        this.refundGoods = refundGoods;
    }

    public List<BusinessGetExchangeResponseExchangeGoodInfo> getExchangeGoods() {
        return exchangeGoods;
    }

    public void setExchangeGoods(List<BusinessGetExchangeResponseExchangeGoodInfo> exchangeGoods) {
        this.exchangeGoods = exchangeGoods;
    }

    public String getNewExchangeRepair() {
        return NewExchangeRepair;
    }

    public void setNewExchangeRepair(String newExchangeRepair) {
        NewExchangeRepair = newExchangeRepair;
    }

    public LocalDateTime getRdsCreateTime() {
        return rdsCreateTime;
    }

    public void setRdsCreateTime(LocalDateTime rdsCreateTime) {
        this.rdsCreateTime = rdsCreateTime;
    }

    public LocalDateTime getRdsModifyTime() {
        return rdsModifyTime;
    }

    public void setRdsModifyTime(LocalDateTime rdsModifyTime) {
        this.rdsModifyTime = rdsModifyTime;
    }

    public String getIsSuccess() { return isSuccess; }

    public void setIsSuccess(String isSuccess) {
        this.isSuccess = isSuccess;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public String getSubMessage() {
        return subMessage;
    }

    public void setSubMessage(String subMessage) {
        this.subMessage = subMessage;
    }
//endregion
}
