package com.differ.wdgj.api.user.biz.domain.order.common.data.enums;

/**
 * 订单表类型
 *
 * <AUTHOR>
 * @date 2024/11/26 下午2:59
 */
public enum OrderTableTypeEnum {
    /**
     * 基础表
     */
    NORMAL(0, "普通"),
    /**
     * 完成表
     */
    ARC(1, "完成"),
    /**
     * 归档表
     */
    HISTORY(2, "归档"),

    ;
    /**
     * 值
     */
    private int value;

    /**
     * 描述
     */
    private String description;

    OrderTableTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
