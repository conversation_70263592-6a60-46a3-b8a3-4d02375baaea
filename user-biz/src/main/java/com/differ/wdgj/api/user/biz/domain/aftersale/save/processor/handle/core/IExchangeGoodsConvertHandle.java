package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.GoodsConvertHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceExchangeGoodsItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetCovertOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiReturnDetailTwoDO;

/**
 * 售后单处理插件-换货商品级数据转换
 *
 * <AUTHOR>
 * @date 2024/8/7 上午11:59
 */
public interface IExchangeGoodsConvertHandle<O, E> {
    /**
     * 转换商品级信息
     *
     * @param orderItem     原始售后单数据
     * @param goodsItem     原始售后退货商品数据
     * @param targetOrder   目标售后单数据
     * @param exchangeGoods 目标售后换货商品数据
     * @return 结果
     */
    GoodsConvertHandleResult convert(SourceAfterSaleOrderItem<O> orderItem, SourceExchangeGoodsItem<E> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods);
}
