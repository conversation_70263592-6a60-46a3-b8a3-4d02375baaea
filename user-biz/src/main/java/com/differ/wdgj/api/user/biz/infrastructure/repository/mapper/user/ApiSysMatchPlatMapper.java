package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.domain.stock.data.notice.PlatStockNoticeSign;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchPlatDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 平台商品库存变动记录 仓储
 * 表g_api_sysMatch_plat
 *
 * <AUTHOR>
 * @date 2024/11/15 下午3:47
 */
public interface ApiSysMatchPlatMapper {
    //region 查询
    /**
     * 查询普通业务待执行的数据是否超过限制
     *
     * @param limitMax 最大条数
     * @return id
     */
    Long getLimitNormalId(@Param("limitMax") Integer limitMax);

    /**
     * 查询多仓业务待执行的数据是否超过限制
     *
     * @param limitMax 最大条数
     * @return id
     */
    Long getLimitMultiWareId(@Param("limitMax") Integer limitMax);

    /**
     * 通过匹配Id和多仓标识查询平台库存触发数据
     *
     * @param matchSignList guid和多仓标识集合
     * @return 库存通知集合
     */
    List<ApiSysMatchPlatDO> queryByGuidAndMultiSign(@Param("matchSignList") List<PlatStockNoticeSign> matchSignList);

    /**
     * 根据ApiSysMatchId查询记录
     *
     * @param apiSysMatchId ApiSysMatch主键ID
     * @return 记录列表
     */
    List<ApiSysMatchPlatDO> selectByApiSysMatchId(Integer apiSysMatchId);
    //endregion

    //region 新增
    /**
     * 新增[平台商品库存变动记录]
     *
     * @param platNotice 平台商品库存变动记录列表
     * @return 影响行数
     */
    Integer insert(@Param("platNotice") ApiSysMatchPlatDO platNotice);
    /**
     * 批量新增[平台商品库存变动记录]
     *
     * @param platNotices 平台商品库存变动记录列表
     * @return 影响行数
     */
    Integer insertBatch(@Param("platNotices") List<ApiSysMatchPlatDO> platNotices);
    //endregion

    //region 更新
    /**
     * 根据Id修改status和变动原因
     *
     * @param status          状态
     * @param changeReasonMap 变动原因map
     * @return 成功or失败
     */
    Boolean updateStatusAndChangeReasonsById(@Param("status") Integer status,
                                                    @Param("changeReasonMap") Map<Integer, List<ApiSysMatchPlatDO>> changeReasonMap);
    //endregion
}
