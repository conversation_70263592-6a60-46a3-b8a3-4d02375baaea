package com.differ.wdgj.api.user.biz.infrastructure.data.enums.member;

import com.differ.wdgj.api.component.util.enums.ValueEnum;

/**
 * 用户类别
 *
 * <AUTHOR>
 * @date 2024/10/18 下午1:42
 */
public enum MemberUserTypeEnum implements ValueEnum {
    /**
     * 管家客户 (0)
     */
    WDGJ_MEMBER(0,"管家客户"),

    /**
     * 开发者 (5)
     */
    DEVELOPER(5,"开发者"),

    /**
     * 系统管理员 (10)
     */
    SYSTEM_ADMINISTRATOR(10,"系统管理员"),

    /**
     * 超级系统管理员 (11)
     */
    SUPER_SYSTEM_ADMINISTRATOR(11,"超级系统管理员"),

    ;

    //region 常量
    /**
     * 枚举值
     */
    private final int value;

    /**
     * 描述
     */
    private final String description;
    //endregion

    //region 构造
    MemberUserTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }
    //endregion

    //region 公共方法
    @Override
    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
    //endregion
}
