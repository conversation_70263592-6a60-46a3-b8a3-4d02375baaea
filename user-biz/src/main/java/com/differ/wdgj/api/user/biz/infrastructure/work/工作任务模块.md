# 工作任务模块

## 模块结构
```
work/
├── business/              # 业务处理
│   └── page/             # 分页处理
│       └── PageWorkBusinessProcessor.java  # 分页业务处理器
├── data/                 # 数据对象
│   ├── WorkData.java     # 工作任务数据
│   ├── WorkContext.java  # 工作上下文
│   └── WorkResult.java   # 工作结果
├── factory/              # 工厂模块
│   ├── WorkFactory.java  # 抽象工厂接口
│   └── AbstractPageWorkFactory.java   # 分页工厂抽象类
├── interrupt/            # 中断处理
│   ├── check/           # 中断检查
│   │   └── WorkInterruptChecker.java  # 中断检查器
│   └── heart/           # 心跳处理
│       └── WorkHeart.java # 心跳接口
├── mutex/               # 互斥处理
│   ├── WorkMutex.java           # 互斥接口
│   ├── DefaultWorkMutex.java    # 默认互斥实现
│   └── DefaultNoWorkMutex.java  # 无互斥实现
├── operate/              # 数据操作
│   ├── WorkDataOperate.java           # 工作任务操作接口
│   ├── WorkDetailOperate.java         # 工作任务详情操作
│   └── WorkSubTaskOperate.java        # 子任务操作
├── split/               # 任务拆分
│   └── strategy/        # 拆分策略
│       ├── TaskSplitStrategy.java          # 拆分策略接口
│       ├── FixTaskSplitStrategy.java       # 固定时间拆分策略
│       ├── DynamicTaskSplitStrategy.java   # 动态时间拆分策略
│       └── NoTaskSplitStrategy.java        # 无拆分策略
├── template/             # 模板模块
│   ├── WorkExecTemplate.java          # 执行模板接口
│   ├── AbstractWorkRunTemplate.java    # 抽象运行模板
│   ├── BaseWorkRunTemplate.java       # 基础运行模板
│   └── PageWorkRunTemplate.java       # 分页运行模板
└── WorkFacade.java      # 工作任务门面类
```

## 核心数据结构

### 1. 工作任务数据
```java
public class WorkData<T extends WorkArgs> {
    private String memberName;          // 会员名
    private Integer shopId;             // 店铺ID
    private WorkEnum workType;          // 任务类型
    private TriggerTypeEnum triggerType;// 触发类型
    private String creator;             // 创建人
    private T args;                     // 任务参数
}
```

### 2. 工作任务DO
```java
public class ApiWorkTaskDO {
    private Long id;                    // 主键
    private String taskId;              // 任务ID
    private Byte triggerType;           // 触发方式
    private Integer shopId;             // 店铺ID
    private Integer taskType;           // 任务类型
    private LocalDateTime gmtExec;      // 执行开始时间
    private LocalDateTime gmtFinish;    // 完成时间
    private byte taskStatus;            // 状态
    private String jsonResult;          // 描述
    private String taskInfo;            // 任务信息
    private String creator;             // 创建人
}
```

### 3. 业务处理器接口
```java
public interface PageWorkBusinessProcessor<T extends WorkData<?>, S extends SubPageTask, R extends WorkResult> {
    List<S> createSubTask(T workData);  // 创建子任务
    RunPageResult doLoad(S subTask);    // 业务处理
    void onAlarm(S subTask, String alarmMsg);  // 业务报警
    R initResult();                     // 初始化结果
    R merge(WorkResult... taskResultDataList);  // 合并结果
}
```

## 业务流程节点

### 1. 工作任务执行流程
1. 创建工作任务
   - 构建`WorkData`和`WorkContext`
   - 通过`WorkFacade`创建任务

2. 任务初始化
   - 开启工作心跳
   - 更新任务状态
   - 初始化进度

3. 子任务处理
   - 创建子任务
   - 执行所有子任务
   - 更新子任务状态

4. 任务完成
   - 更新任务状态
   - 完成进度更新
   - 关闭工作心跳

### 2. 工厂创建流程
1. 根据任务类型选择工厂
   - `LoadGoodsWorkFactory`: 商品下载
   - `MatchGoodsWorkFactory`: 商品匹配

2. 工厂创建组件
   - 创建执行模板
   - 创建数据操作类
   - 创建心跳监控
   - 创建中断检测器

## 基础数据结构

### 1. 工厂接口
```java
public interface WorkFactory<T extends WorkData<?>> {
    WorkExecTemplate<T> createTemplate();        // 创建模板
    WorkDataOperate createDataOperate();         // 创建数据操作类
    WorkHeart createWorkHeart(DataOperateContext context);  // 创建心跳
    WorkInterruptChecker createWorkInterrupt();  // 创建中断检测器
    WorkMutex createWorkMutex();                 // 创建互斥处理器
    WorkEventHandler createEventHandler();        // 创建事件处理器
}
```

### 2. 数据操作接口
```java
public interface WorkDataOperate {
    CreateResult create(WorkData<?> workData);   // 创建工作任务
    boolean existsUnComplete(WorkData<?> workData);  // 检查未完成任务
    List<String> listToCheckWork(String member, WorkEnum workEnum);  // 查询待检测任务
    void workComplete(String member, String taskId, WorkResult workResult);  // 完成任务
}
```

### 3. 详情操作接口
```java
public interface WorkDetailOperate<S extends SubTask> {
    void setProgress(int value);        // 设置进度
    void increProgress(int value);      // 增量更新进度
    void updateSubTask(S subTask);      // 更新子任务
    void initToExecute(String member, String taskId);  // 初始化执行状态
    void complete(String member, String taskId, WorkResult workResult);  // 完成任务
}
```