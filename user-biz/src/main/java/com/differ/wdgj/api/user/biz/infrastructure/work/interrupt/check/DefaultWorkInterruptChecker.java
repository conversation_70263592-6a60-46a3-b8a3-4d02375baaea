package com.differ.wdgj.api.user.biz.infrastructure.work.interrupt.check;

import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.MemberRunnable;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.DataOperateContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.factory.WorkFactory;
import com.differ.wdgj.api.user.biz.infrastructure.work.interrupt.heart.WorkHeart;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkDataOperate;
import com.differ.wdgj.api.user.biz.infrastructure.work.template.WorkExecTemplate;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 默认的中断检测器
 *
 * <AUTHOR>
 * @date 2024/7/9 17:16
 */
public class DefaultWorkInterruptChecker implements WorkInterruptChecker {
    //region 常量
    /**
     * 工作任务类型
     */
    private final WorkEnum workEnum;
    //endregion

    //region 构造
    public DefaultWorkInterruptChecker(WorkEnum workEnum) {
        this.workEnum = workEnum;
    }
    //endregion

    /**
     * 检测工作任务是否被中断,并将中断任务加入执行线程池
     *
     * @param member 会员名
     */
    @Override
    public void checkInterruptAndHandle(String member) {
        // 获取工作任务工厂
        WorkFactory workFactory = workEnum.getWorkFactory();
        WorkDataOperate dataOperate = workFactory.createDataOperate();
        // 查询要检测的工作任务
        List<String> lstTaskIds = dataOperate.listToCheckWork(member, this.workEnum);
        if (CollectionUtils.isEmpty(lstTaskIds)) {
            return;
        }
        for (String taskId : lstTaskIds) {
            //  检测工作任务中断并处理
            checkTaskInterruptAndHandle(member, taskId);
        }
    }

    /**
     * 检测某工作任务是否被中断,并将中断任务加入执行线程池
     *
     * @param member 会员名
     */
    @Override
    public void checkTaskInterruptAndHandle(String member, String taskId) {
        // 获取工作任务工厂
        WorkFactory workFactory = this.workEnum.getWorkFactory();
        // 创建心跳器检测
        DataOperateContext operateContext = DataOperateContext.of(member, this.workEnum, taskId);
        WorkHeart workHeart = workFactory.createWorkHeart(operateContext);
        WorkExecTemplate execTemplate = workFactory.createTemplate();
        if (!workHeart.isRunning()) {
            // 任务已中断，丢入任务执行线程,线程池要再判断是否存在执行中的任务
            this.workEnum.getTaskEnum().execute(new MemberRunnable(member) {
                @Override
                public void run() {
                    execTemplate.run(member, taskId);
                }
            });
        }
    }
}
