package com.differ.wdgj.api.user.biz.infrastructure.auto.plugins;

import com.differ.wdgj.api.user.biz.infrastructure.auto.AbstractAutoJobMemberResolver;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.auto.AutoLoadAfterShopLocalCache;

/**
 * 自动下载售后单任务会员解析器
 *
 * <AUTHOR>
 * @date 2024/11/27 下午7:04
 */
public class AutoLoadAfterSaleMemberResolver extends AbstractAutoJobMemberResolver {
    // region 重写基类方法

    /**
     * 是否开启自动
     *
     * @param memberName 会员名
     * @return 结果
     */
    @Override
    public boolean enableAuto(String memberName) {
        return AutoLoadAfterShopLocalCache.singleton().enableAutoTask(memberName);
    }

    // endregion
}
