package com.differ.wdgj.api.user.biz.infrastructure.monitor.exec;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogContextIdUtil;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.ExecuteQueueMonitorCenter;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.data.ExecuteEvent;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.data.ExecuteStatus;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.core.data.ModuleCodeConstant;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.plugin.center.LocalChainLogMonitorCenter;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.plugin.center.LocalConsumeTimeMonitorCenter;
import com.differ.wdgj.api.user.biz.infrastructure.monitor.exec.plugin.center.LocalExecuteTimeoutAlarmMonitorCenter;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.CustomRejectRunnable;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core.reject.TryRequeueRunsPolicy;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * @Description 执行队列监控管理类型, 相关定时任务:LocalExecuteMonitorJob 设计：https://s.jkyun.biz/PtwivXO 执行监控基础组件
 * <AUTHOR>
 * @Date 2023/12/14 16:30
 */
public enum ExecuteMonitorEnum {

    CONSUME_TIME(ModuleCodeConstant.COMMON, "consume_time", LocalConsumeTimeMonitorCenter.class),

    CHAIN_LOG(ModuleCodeConstant.COMMON, "chain_log", LocalChainLogMonitorCenter.class),

    LOAD_ORDER_TIMEOUT(ModuleCodeConstant.ORDER, "order_load_timeout", LocalExecuteTimeoutAlarmMonitorCenter.class),

    ;
    /**
     * 业务模块
     */
    private String moduleCode;

    /**
     * 队列业务类型
     */
    private String code;

    /**
     * 监控队列
     */
    private Class<? extends ExecuteQueueMonitorCenter> queueClass;

    /**
     * 监控队列
     */
    private String queueBean;


    /**
     * 监控队列
     */
    private ExecuteQueueMonitorCenter queue;

    /**
     * 监控队列
     */
    private TaskEnum taskEnum;

    ExecuteMonitorEnum(String moduleCode, String code, Class<? extends ExecuteQueueMonitorCenter> queueClass) {
        this.moduleCode = moduleCode;
        this.code = code;
        this.queueClass = queueClass;
        this.taskEnum = TaskEnum.API_EXEC_MONITOR_QUEUE;
    }

    ExecuteMonitorEnum(String moduleCode, String code, String queueBean) {
        this.moduleCode = moduleCode;
        this.code = code;
        this.queueBean = queueBean;
        this.taskEnum = TaskEnum.API_EXEC_MONITOR_QUEUE;
    }

    public String getCode() {
        return code;
    }

    /**
     * 业务跟踪队列监控中心
     *
     * @return
     */
    private ExecuteQueueMonitorCenter getQueue() {
        if (queue == null) {
            if (StringUtils.isNotBlank(queueBean)) {
                queue = BeanContextUtil.getBean(queueBean, ExecuteQueueMonitorCenter.class);
            } else if (queueClass != null) {
                try {
                    queue = queueClass.newInstance();
                    queue.initCode(this.code);
                } catch (Throwable e) {
                    throw new RuntimeException("执行监控队列创建失败", e);
                }
            }
        }
        return queue;
    }

    /**
     * 添加监控父数据（首次添加）
     *
     * @param eventData 跟踪事件内容，为空时不添加事件
     * @return 队列组的唯一流水号，用于后续添加子数据
     */
    public Long addFirst(String eventData) {
        return addFirst(this.code, eventData);
    }

    /**
     * 添加监控父数据（首次添加）
     *
     * @param caption   业务标题或关键字，用于区分不同的业务日志（默认使用code作为标题）
     * @param eventData 跟踪事件内容，为空时不添加事件
     * @return 队列组的唯一流水号，用于后续添加子数据
     */
    public Long addFirst(String caption, String eventData) {
        if (StringUtils.isBlank(caption)) {
            // 未设置标题时，使用业务代码
            return this.getQueue().initWithCaption(this.code, eventData);
        }
        return this.getQueue().initWithCaption(caption, eventData);
    }

    /**
     * 添加监控父数据（首次添加）
     *
     * @param caption      业务标题或关键字，用于区分不同的业务日志（默认使用code作为标题）
     * @param funEventData 跟踪事件内容，为空时不添加事件
     * @return 队列组的唯一流水号，用于后续添加子数据
     */
    public Long addFirst(String caption, Supplier<StringBuilder> funEventData) {
        if (StringUtils.isBlank(caption)) {
            // 未设置标题时，使用业务代码
            return this.getQueue().initWithCaption(this.code, funEventData);
        }
        return this.getQueue().initWithCaption(caption, funEventData);
    }

    /**
     * 添加未完成状态的监控子数据
     *
     * @param parentUniqueNo
     * @param eventData
     */
    public void addChild(Long parentUniqueNo, String eventData) {
        putChild(parentUniqueNo, eventData, ExecuteStatus.DOING);
    }

    /**
     * 添加完成状态的子监控数据
     *
     * @param parentUniqueNo
     * @param eventData
     * @param finishSuccess
     */
    public void addChild(Long parentUniqueNo, String eventData, boolean finishSuccess) {
        putChild(parentUniqueNo, eventData, finishSuccess ? ExecuteStatus.SUCCESS : ExecuteStatus.FAIL);
    }

    /**
     * 添加未完成状态的监控子数据
     *
     * @param parentUniqueNo
     * @param funEventData
     */
    public void addChild(Long parentUniqueNo, Supplier<StringBuilder> funEventData) {
        if (parentUniqueNo == null) {
            return;
        }
        putChild(parentUniqueNo, funEventData.get().toString(), ExecuteStatus.DOING);
    }

    /**
     * 添加完成状态的子监控数据
     *
     * @param parentUniqueNo
     * @param funEventData
     * @param finishSuccess
     */
    public void addChild(Long parentUniqueNo, Supplier<StringBuilder> funEventData, boolean finishSuccess) {
        if (parentUniqueNo == null) {
            return;
        }
        putChild(parentUniqueNo, funEventData.get().toString(), finishSuccess ? ExecuteStatus.SUCCESS : ExecuteStatus.FAIL);
    }

    /**
     * 添加子监控数据
     *
     * @param parentUniqueNo
     * @param eventData
     * @param status
     */
    private void putChild(Long parentUniqueNo, String eventData, ExecuteStatus status) {
        if (parentUniqueNo == null) {
            return;
        }
        ExecuteEvent event = new ExecuteEvent();
        event.setUniqueNo(parentUniqueNo);
        event.setEventData(eventData);
        event.setStatus(status);
        event.setEventTime(LocalDateTime.now());
        asyncExecute(event);
    }

    /**
     * 检测超时
     */
    public int checkTimeoutAndGetTotalEvent() {
        return this.getQueue().checkTimeoutAndGetTotalCount();
    }

    /**
     * 是否使用本地任务监控
     *
     * @return
     */
    public boolean useJobMonitor() {
        return this.getQueue().useJobMonitor();
    }

    /**
     * 创建唯一键
     *
     * @return
     */
    private String createUniqueNo() {
        return String.format("%s_%s_%d", this.moduleCode, this.code, LogContextIdUtil.initNewLogId());
    }

    /**
     * 线程池异步执行（自定义拒绝策略）
     *
     * @param event
     */
    private void asyncExecute(ExecuteEvent event) {
        // 自定义拒绝策略
        Function<Object, RejectedExecutionHandler> funReject = (arg) -> {
            ExecuteEvent eventReject = (ExecuteEvent) arg;
            if (eventReject == null) {
                return null;
            }
            if (eventReject.getStatus() == ExecuteStatus.DOING) {
                // 非完成的直接拒绝，相对不重要
                return new ThreadPoolExecutor.AbortPolicy();
            } else {
                // 完成时，尝试重新入队
                return new TryRequeueRunsPolicy();
            }
        };
        // 线程池异步执行
        this.taskEnum.execute(new CustomRejectRunnable(event, funReject) {
            @Override
            public void run() {
                ExecuteEvent e = (ExecuteEvent) getArgv();
                if (e != null) {
                    getQueue().put(e);
                }
            }
        });

    }
}
