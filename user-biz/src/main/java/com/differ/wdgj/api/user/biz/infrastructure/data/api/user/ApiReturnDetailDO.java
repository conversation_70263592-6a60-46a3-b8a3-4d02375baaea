package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

import java.math.BigDecimal;

/**
 * 退货退款商品(g_api_return_detail)
 *
 * <AUTHOR>
 * @date 2024-06-27 19:50
 */
public class ApiReturnDetailDO {
    /**
     * 主键。
     */
    private Integer recId;

    /**
     * 售后单Id(g_api_return_list)。
     */
    private Integer billId;

    /**
     * erp货品id。
     */
    private Integer goodsId;

    /**
     * erp规格id。
     */
    private Integer specId;

    /**
     * 是否为组合装。
     */
    private int bFit;

    /**
     * 商品外部商家编码。
     */
    private String outerId;

    /**
     * 商品数量。
     */
    private BigDecimal goodsCount;

    /**
     * 商品价格。
     */
    private BigDecimal price;

    /**
     * 商家备注。
     */
    private String remark;

    /**
     * 平台商品id
     */
    private String platGoodsId;

    /**
     * 平台规格id
     */
    private String platSkuId;

    /**
     * 平台商品名称。
     */
    private String goodsTitle;

    /**
     * 平台规格名称
     */
    private String sku;

    /**
     * JIT PO单号。
     */
    private String jitPONO;

    /**
     * 子订单号
     */
    private String oid;

    /**
     * 扩展字段
     */
    private String extendedField;

    /**
     * 扩展字段
     */
    private Boolean bSend;

    //region get/set
    public Integer getRecId() {
        return recId;
    }

    public void setRecId(Integer recId) {
        this.recId = recId;
    }

    public Integer getBillId() {
        return billId;
    }

    public void setBillId(Integer billId) {
        this.billId = billId;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public int getbFit() {
        return bFit;
    }

    public void setbFit(int bFit) {
        this.bFit = bFit;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public BigDecimal getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(BigDecimal goodsCount) {
        this.goodsCount = goodsCount;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPlatGoodsId() {
        return platGoodsId;
    }

    public void setPlatGoodsId(String platGoodsId) {
        this.platGoodsId = platGoodsId;
    }

    public String getPlatSkuId() {
        return platSkuId;
    }

    public void setPlatSkuId(String platSkuId) {
        this.platSkuId = platSkuId;
    }

    public String getGoodsTitle() {
        return goodsTitle;
    }

    public void setGoodsTitle(String goodsTitle) {
        this.goodsTitle = goodsTitle;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getJitPONO() {
        return jitPONO;
    }

    public void setJitPONO(String jitPONO) {
        this.jitPONO = jitPONO;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getExtendedField() {
        return extendedField;
    }

    public void setExtendedField(String extendedField) {
        this.extendedField = extendedField;
    }

    public Boolean getbSend() {
        return bSend;
    }

    public void setbSend(Boolean bSend) {
        this.bSend = bSend;
    }
    //endregion
}
