package com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch;

import com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch.data.CommonWarehouseMatchDTO;

import java.util.Set;

/**
 * 仓库匹配数据适配器
 *
 * <AUTHOR>
 * @date 2024/12/18 下午4:42
 */
public interface IWarehouseMatchAdapter {
    /**
     * 获取店铺所有仓库匹配数据
     *
     * @param memberName 会员名
     * @param shopId     店铺id
     * @return 仓库匹配列表
     */
    Set<CommonWarehouseMatchDTO> getListByShop(String memberName, int shopId);
}
