package com.differ.wdgj.api.user.biz.infrastructure.work.split.strategy;

import com.differ.wdgj.api.user.biz.infrastructure.work.data.PageDynamicStatus;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.SubPageTask;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.TaskSplitStrategyContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.RunStatusEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * 固定拆分方式
 *
 * <AUTHOR>
 * @date 2024/7/12 13:53
 */
public class FixTaskSplitStrategy implements TaskSplitStrategy<SubPageTask> {

    // region 构造器

    public FixTaskSplitStrategy(TaskSplitStrategyContext context) {
        this.context = context;
    }


    // endregion

    // region 变量

    /**
     * 任务拆分策略上下文
     */
    private final TaskSplitStrategyContext context;

    // endregion

    /**
     * 拆分下一个任务
     *
     * @param lastSubTask 上次的子任务
     * @param first       首次拆
     * @return 返回拆分子任务
     */
    @Override
    public SubPageTask splitNext(SubPageTask lastSubTask, boolean first) {
        // 如果上下文为空 或者最大查询时间不大于0 表示不需要按照固定拆分
        if (!this.isAllowSplit(lastSubTask)) {
            if (first) {
                return new NoTaskSplitStrategy(this.context).splitNext(lastSubTask, true);
            } else {
                return null;
            }
        } else {
            // 上次拆分子任务的动态拆分信息
            PageDynamicStatus lastPageDynamicStatus = lastSubTask.getDynamicStatus();

            // 本次拆分子任务的动态拆分信息
            PageDynamicStatus newPageDynamicStatus = new PageDynamicStatus();

            // 重置页码、运行状态
            newPageDynamicStatus.setPageIndex(1);
            newPageDynamicStatus.setPageSize(lastSubTask.getPageSize());
            newPageDynamicStatus.setRunStatus(RunStatusEnum.RUNNING);
            newPageDynamicStatus.setTimeType(lastSubTask.getSourceTimeType());
            if (first) {
                // 如果是首次拆分，需要将请求时间类型=输入时间类型，请求开始时间=输入的开始时间，请求结束时间=请求开始时间+最大查询时间限制
                newPageDynamicStatus.setLoadStartTime(lastSubTask.getTaskSourceStartTime());
            } else {
                // 非首次，需要将上次的请求结束时间当成这次的请求开始时间
                newPageDynamicStatus.setLoadStartTime(lastPageDynamicStatus.getLoadEndTime());
            }
            // 请求结束时间=请求开始时间+最大查询时间
            newPageDynamicStatus.setLoadEndTime(newPageDynamicStatus.getLoadStartTime().plusMinutes(context.getMaxQueryRange()));
            // 如果开始时间等于输入的结束时间 表示已经查询结束 不需要继续进行查询
            if (newPageDynamicStatus.getLoadStartTime().compareTo(lastSubTask.getTaskSourceEndTime()) >= 0) {
                return null;
            }
            // 如果查询的结束时间 在输入的结束时间之后，需要将结束时间直接等于输入的结束时间
            if (newPageDynamicStatus.getLoadEndTime().isAfter(lastSubTask.getTaskSourceEndTime())) {
                newPageDynamicStatus.setLoadEndTime(lastSubTask.getTaskSourceEndTime());
            }
            lastSubTask.setDynamicStatus(newPageDynamicStatus);
            return lastSubTask;
        }
    }

    /**
     * 是否允许拆分
     *
     * @param lastSubTask 上次子任务信息
     * @return 返回结果
     */
    private boolean isAllowSplit(SubPageTask lastSubTask) {
        // 如果上下文为空 或者最大查询时间不大于0 表示不需要按照固定拆分
        boolean ret = null != this.context && this.context.getMaxQueryRange() > 0;

        // 如果上序结果为true，需要接着判断输入的时间信息
        if (ret) {
            // 输入的源时间类型、源时间是否为空 表示不需要拆分
            ret = StringUtils.isNotBlank(lastSubTask.getSourceTimeType()) && null != lastSubTask.getTaskSourceStartTime() && null != lastSubTask.getTaskSourceEndTime();
        }
        return ret;
    }
}
