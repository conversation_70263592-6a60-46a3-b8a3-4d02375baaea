package com.differ.wdgj.api.user.biz.domain.aftersale.save.data.out;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.load.OrderTriggerTypeEnum;

import java.time.LocalDateTime;

/**
 * 外部入参 - 保存售后订单入参
 *
 * <AUTHOR>
 * @date 2024-06-27 19:24
 */
public class SaveAfterSaleParam {
    //region 属性
    /**
     * 会员名。
     */
    private String memberName;

    /**
     * 外部店铺id
     * 会员库g_cfg_shopList
     */
    private Integer outShopId;

    /**
     * 订单触发方式。
     */
    private OrderTriggerTypeEnum orderTriggerType;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 菠萝派请求id。
     */
    private String polyApiRequestId;

    /**
     * 下载时间。
     */
    private LocalDateTime loadTime;
    //endregion

    //region get/set

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public Integer getOutShopId() {
        return outShopId;
    }

    public void setOutShopId(Integer outShopId) {
        this.outShopId = outShopId;
    }

    public OrderTriggerTypeEnum getOrderTriggerType() {
        return orderTriggerType;
    }

    public void setOrderTriggerType(OrderTriggerTypeEnum orderTriggerType) {
        this.orderTriggerType = orderTriggerType;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getPolyApiRequestId() {
        return polyApiRequestId;
    }

    public void setPolyApiRequestId(String polyApiRequestId) {
        this.polyApiRequestId = polyApiRequestId;
    }

    public LocalDateTime getLoadTime() {
        return loadTime;
    }

    public void setLoadTime(LocalDateTime loadTime) {
        this.loadTime = loadTime;
    }
    //endregion
}
