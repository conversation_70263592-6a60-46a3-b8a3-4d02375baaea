@startuml
' 定义接口
interface IMultiWarehouseAdapter {
    + getMode(goodsMatch: ApiSysMatchDO): StockMultiWhsModeEnum
    + getWhsMatches(goodsMatch: ApiSysMatchDO): Set<CommonWarehouseMatchDTO>
    + getWhsMatchesByErp(goodsMatch: ApiSysMatchDO, erpWarehouseIds: Set<Integer>): Set<CommonWarehouseMatchDTO>
    + getWhsMatchesByPlat(goodsMatch: ApiSysMatchDO, platWareSign: String): Set<CommonWarehouseMatchDTO>
}

' 定义抽象类
class BaseMultiWarehouseAdapter {
    # context: StockMultiWhsContext
    + BaseMultiWarehouseAdapter(context: StockMultiWhsContext)
    + getMode(goodsMatch: ApiSysMatchDO): StockMultiWhsModeEnum
    + getWhsMatches(goodsMatch: ApiSysMatchDO): Set<CommonWarehouseMatchDTO>
    + getWhsMatchesByErp(goodsMatch: ApiSysMatchDO, erpWarehouseIds: Set<Integer>): Set<CommonWarehouseMatchDTO>
    + getWhsMatchesByPlat(goodsMatch: ApiSysMatchDO, platWareSign: String): Set<CommonWarehouseMatchDTO>
}

' 定义工厂类
class MultiWarehouseAdapterFactory {
    - MultiWarehouseAdapterFactory()
    + createInstance(memberName: String, shopId: int): IMultiWarehouseAdapter
    + createInstance(memberName: String, apiShopBase: ApiShopBaseDto, stockConfig: SyncStockShopConfig): IMultiWarehouseAdapter
}

' 定义具体实现类
class AliExpressMultiWarehouseAdapter {
    + AliExpressMultiWarehouseAdapter(context: StockMultiWhsContext)
    + getMode(goodsMatch: ApiSysMatchDO): StockMultiWhsModeEnum
}

' 定义关系
IMultiWarehouseAdapter <|.. BaseMultiWarehouseAdapter
BaseMultiWarehouseAdapter <|-- AliExpressMultiWarehouseAdapter
MultiWarehouseAdapterFactory ..> IMultiWarehouseAdapter

' 添加注释
note right of IMultiWarehouseAdapter
  仓库匹配适配器接口
  定义了多仓库存同步的核心方法
end note

note right of BaseMultiWarehouseAdapter
  基础多仓适配器实现类
  提供了通用的多仓匹配逻辑
end note

note right of MultiWarehouseAdapterFactory
  多仓适配器工厂类
  负责创建不同平台的多仓适配器实例
end note

note right of AliExpressMultiWarehouseAdapter
  速卖通平台多仓适配器
  针对速卖通平台的特殊实现
end note

' 添加箭头说明
legend right
  <|.. 表示实现关系
  <|-- 表示继承关系
  ..> 表示依赖关系
end legend
@enduml