package com.differ.wdgj.api.user.biz.tasks.job.single.core;


import com.differ.jackyun.framework.component.basic.interceptor.CommonContextHolder;
import com.differ.jackyun.framework.component.utils.id.IdWorkerUtil;
import com.differ.wdgj.api.component.task.single.core.AbstractSingleBeanJob;
import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import org.quartz.JobExecutionContext;

/**
 * @Description 单体任务基类
 * <AUTHOR>
 * @Date 2021/1/8 14:23
 */
public abstract class BaseSingleJob extends AbstractSingleBeanJob {

    /**
     * 日志标题
     */
    protected String logCaption = "单体任务";

    /**
     * 是否支持在本站点运行
     *
     * @param sitesToRun 可运行的站点集合，由注解参数设置
     * @return
     */
    @Override
    public boolean runOnSite(String[] sitesToRun) {
        return SystemAppConfig.get().runOnSite(sitesToRun);
    }

    /**
     * 业务处理
     *
     * @param context
     */
    @Override
    protected void doWork(JobExecutionContext context) {
        // 重置日志ID
        CommonContextHolder.setLoggerSn(IdWorkerUtil.getId());
        // 执行任务
        this.doWork();
    }
}
