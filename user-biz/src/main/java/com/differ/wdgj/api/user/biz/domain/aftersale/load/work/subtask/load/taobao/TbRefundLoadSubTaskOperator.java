package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.load.taobao;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.RdsTbRefundDomain;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.RdsTbRefundOutRequest;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.TbAfterSaleRdsPageQueryResponse;

/**
 * 淘宝退货退款单下载操作类
 *
 * <AUTHOR>
 * @date 2025/4/9 下午3:18
 */
public class TbRefundLoadSubTaskOperator extends BaseTbLoadSubTaskOperator{
    //region 构造
    public TbRefundLoadSubTaskOperator(AfterSaleLoadTaskContext context) {
        super(context);
    }
    //endregion

    /**
     * 获取售后单总数
     *
     * @param request 请求参数
     * @return 售后单总数
     */
    @Override
    protected int getAfterSaleCount(RdsTbRefundOutRequest request) {
        RdsTbRefundDomain rdsTbRefundDomain = new RdsTbRefundDomain(context.getMemberName());
        return rdsTbRefundDomain.queryPolyRefundOrderCount(request);
    }

    /**
     * 获取售后单列表
     *
     * @param request 请求参数
     * @return 售后单列表
     */
    @Override
    protected TbAfterSaleRdsPageQueryResponse getAfterSaleList(RdsTbRefundOutRequest request) {
        RdsTbRefundDomain rdsTbRefundDomain = new RdsTbRefundDomain(context.getMemberName());
        return rdsTbRefundDomain.queryPolyRefundOrder(request);
    }
}
