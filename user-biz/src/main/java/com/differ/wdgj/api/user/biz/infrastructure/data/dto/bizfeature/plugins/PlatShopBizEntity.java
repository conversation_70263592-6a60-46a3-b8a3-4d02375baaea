package com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins;

import java.util.List;

/**
 * 平台业务特性二级业务
 *
 * <AUTHOR>
 * @date 2024/9/19 下午6:35
 */
public class PlatShopBizEntity {
    /**
     * API 业务值
     */
    private String bizValue;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 菠萝派业务值
     */
    private String polyValue;

    /**
     * 下载订单原始状态
     */
    private List<PlatSourceOrderTypeEntity> sourceOrderTypeList;

    public String getBizValue() {
        return bizValue;
    }

    public void setBizValue(String bizValue) {
        this.bizValue = bizValue;
    }

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public String getPolyValue() {
        return polyValue;
    }

    public void setPolyValue(String polyValue) {
        this.polyValue = polyValue;
    }

    public List<PlatSourceOrderTypeEntity> getSourceOrderTypeList() {
        return sourceOrderTypeList;
    }

    public void setSourceOrderTypeList(List<PlatSourceOrderTypeEntity> sourceOrderTypeList) {
        this.sourceOrderTypeList = sourceOrderTypeList;
    }
}
