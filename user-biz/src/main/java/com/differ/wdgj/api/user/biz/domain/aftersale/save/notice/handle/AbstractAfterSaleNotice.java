package com.differ.wdgj.api.user.biz.domain.aftersale.save.notice.handle;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSaleOutNoticeTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 外部消息通知插件-抽象类
 *
 * <AUTHOR>
 * @date 2024-07-05 13:15
 */
public abstract class AbstractAfterSaleNotice implements IAfterSaleNotice {
    //region 常量

    /**
     * 日志
     */
    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 保存定的上下文
     */
    protected final AfterSaleSaveContext context;

    /**
     * 订单号
     */
    protected final String afterSaleNo;

    //endregion

    //region 构造
    /**
     * 构造
     * @param context 上下文
     * @param afterSaleNo 售后单号
     */
    protected AbstractAfterSaleNotice(AfterSaleSaveContext context, String afterSaleNo) {
        this.context = context;
        this.afterSaleNo = afterSaleNo;
    }
    //endregion

    //region 实现接口方法

    /**
     * 获取售后单号
     *
     * @return 售后单号
     */
    public String getAfterSaleNo(){
        return afterSaleNo;
    }

    /**
     * 处理订单失败时，是否继续通知事件
     *
     * @return false:处理订单失败时，丢弃事件
     */
    public boolean noticeOnProcessOrderFail(){
        return false;
    }

    /**
     * 处理通知
     */
    @Override
    public void processNotice() {
        // 初始化
        AfterSaleOutNoticeTypeEnum noticeType = getNoticeType();
        try {
            process();
        } catch (Exception e) {
            // 错误日志
            String logContent = String.format("【%s】【%s】售后单外部消息通知失败，售后单号：%s, 消息类别：%s", context.getMemberName(), context.getShopId(), afterSaleNo, noticeType.getName());
            log.error(logContent, e);
        }
    }
    //endregion

    //region 供子类重写
    /**
     * 处理通知
     */
    protected abstract void process();
    //endregion
}
