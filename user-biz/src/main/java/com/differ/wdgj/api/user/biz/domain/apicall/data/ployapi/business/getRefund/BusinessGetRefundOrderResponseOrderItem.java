package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund;

import com.alibaba.fastjson.annotation.JSONField;
import com.differ.wdgj.api.component.util.json.core.StringDateTimeDeserializer;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundGoodsStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.order.PolyOrderStatusEnum;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.TbRefundLogisticsInterceptDto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 菠萝派下载退货退款单
 *
 * <AUTHOR>
 * @date 2024-06-06 10:32
 */
public class BusinessGetRefundOrderResponseOrderItem {

    /**
     * 退款单号
     */
    private String refundNo;

    /**
     * 平台订单号
     */
    private String platOrderNo;

    /**
     * 订单总金额(不包含优惠)
     */
    private BigDecimal totalAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 买家昵称
     */
    private String buyerNick;

    /**
     * 买家昵称ID
     */
    private String buyerOpenUid;

    /**
     * 退款申请时间
     */
    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime createTime;

    /**
     * 退款更新时间
     */
    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime updateTime;

    /**
     * 推送库创建时间
     */
    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime rdsCreateTime;

    /**
     * 推送库更新时间
     */
    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime rdsModifyTime;

    /**
     * 平台子订单号
     */
    private String subPlatOrderNo;

    /**
     * 售后单类型
     * @see PolyRefundTypeEnum
     */
    private String refundType;

    /**
     * 售后单类型描述
     */
    private String refundTypeDesc;

    /**
     * 订单状态
     * {@link PolyOrderStatusEnum}
     */
    private String orderStatus;

    /**
     * 订单状态描述
     */
    private String orderStatusDesc;

    /**
     * 退款状态
     */
    private String refundStatus;

    /**
     * 退款状态描述
     */
    private String refundStatusDesc;

    /**
     * 商品状态
     * {@link PolyRefundGoodsStatusEnum}
     */
    private String goodsStatus;

    /**
     * 商品状态描述
     */
    private String goodsStatusDesc;

    /**
     * 买家是否需要退货
     */
    private Boolean hasGoodsReturn;

    /**
     * 退款原因
     */
    private String reason;

    /**
     * 退款描述
     */
    private String desc;

    /**
     * 物流公司名称
     */
    private String logisticName;

    /**
     * 退货运单号
     */
    private String logisticNo;

    /**
     * 物流公司编码
     */
    private String logisticCode;

    /**
     * 逆向销退物流单号（天猫国际直营回告单号）
     */
    private String bizOrderCode;

    /**
     * 子店铺id
     */
    private String shopId;

    /**
     * 店铺类型
     */
    private String shopType;

    /**
     * 子单号集合
     */
    private List<String> subOrderId;

    /**
     * 子单号-退款商品数量映射
     */
    private Map<String, String> subOrderIdMap;

    /**
     * 买家详细地址
     */
    private String address;

    //region 卖家收退货地址

    /**
     * 卖家收退货的地址id
     */
    private String sellerReceiveAddressId;

    /**
     * 卖家收退货的地址省份
     */
    private String sellerReceiveProvince;

    /**
     * 卖家收退货的地址城市
     */
    private String sellerReceiveCity;

    /**
     * 卖家收退货的地址区域
     */
    private String sellerReceiveArea;

    /**
     * 卖家收退货的地址镇/街道
     */
    private String sellerReceiveTown;

    /**
     * 卖家收退货的地址
     */
    private String sellerAddress;

    /**
     * 卖家昵称
     */
    private String sellerNick;

    /**
     * 特殊售后类型
     */
    private String specialRefundType;

    //endregion

    // region 订单级商品信息

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品购买数量
     */
    private int productNum;
    /**
     * 平台商品ID
     */
    private String platProductId;

    /**
     * 商品SKU信息
     */
    private String sku;

    /**
     * 商家编码
     */
    private String outerId;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 仓库编码
     */
    private String whsecode;

    /**
     * 仓库描述
     */
    private String whsecodedesc;

    // endregion

    //region 扩展（非菠萝派字段）
    /**
     * 退款单完整信息（JSON结构，存储聚合返回的完整信息，应用：天猫国际直营管家回告平台）
     */
    private String refundWholeInfo;

    /**
     * 淘宝专业 - 是否为主要
     */
    private String isPrime;

    /**
     * 淘宝专业 - 退款阶段
     */
    private String refundPhase;

    /**
     * 淘宝专用 - 退款版本
     */
    private String refundVersion;

    /**
     * 淘宝专用 - 物流拦截信息
     */
    private TbRefundLogisticsInterceptDto tbRefundLogisticsIntercept;

    /**
     * 是否成功
     */
    private String isSuccess;

    /**
     * 菠萝派返回子编码
     */
    private String subCode;

    /**
     * 菠萝派返回子信息
     */
    private String subMessage;


    //endregion

    //region 商品级信息

    /**
     * 退换商品明细
     */
    private List<BusinessGetRefundResponseRefundGoodInfo> refundGoods;

    //endregion

    //region get/set

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo;
    }

    public String getPlatOrderNo() {
        return platOrderNo;
    }

    public void setPlatOrderNo(String platOrderNo) {
        this.platOrderNo = platOrderNo;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getBuyerOpenUid() {
        return buyerOpenUid;
    }

    public void setBuyerOpenUid(String buyerOpenUid) {
        this.buyerOpenUid = buyerOpenUid;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getSubPlatOrderNo() {
        return subPlatOrderNo;
    }

    public void setSubPlatOrderNo(String subPlatOrderNo) {
        this.subPlatOrderNo = subPlatOrderNo;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public String getRefundTypeDesc() {
        return refundTypeDesc;
    }

    public void setRefundTypeDesc(String refundTypeDesc) {
        this.refundTypeDesc = refundTypeDesc;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getOrderStatusDesc() {
        return orderStatusDesc;
    }

    public void setOrderStatusDesc(String orderStatusDesc) {
        this.orderStatusDesc = orderStatusDesc;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getRefundStatusDesc() {
        return refundStatusDesc;
    }

    public void setRefundStatusDesc(String refundStatusDesc) {
        this.refundStatusDesc = refundStatusDesc;
    }

    public String getGoodsStatus() {
        return goodsStatus;
    }

    public void setGoodsStatus(String goodsStatus) {
        this.goodsStatus = goodsStatus;
    }

    public String getGoodsStatusDesc() {
        return goodsStatusDesc;
    }

    public void setGoodsStatusDesc(String goodsStatusDesc) {
        this.goodsStatusDesc = goodsStatusDesc;
    }

    public Boolean getHasGoodsReturn() {
        return hasGoodsReturn;
    }

    public void setHasGoodsReturn(Boolean hasGoodsReturn) {
        this.hasGoodsReturn = hasGoodsReturn;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getLogisticName() {
        return logisticName;
    }

    public void setLogisticName(String logisticName) {
        this.logisticName = logisticName;
    }

    public String getLogisticNo() {
        return logisticNo;
    }

    public void setLogisticNo(String logisticNo) {
        this.logisticNo = logisticNo;
    }

    public String getLogisticCode() {
        return logisticCode;
    }

    public void setLogisticCode(String logisticCode) {
        this.logisticCode = logisticCode;
    }

    public String getBizOrderCode() {
        return bizOrderCode;
    }

    public void setBizOrderCode(String bizOrderCode) {
        this.bizOrderCode = bizOrderCode;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getShopType() {
        return shopType;
    }

    public void setShopType(String shopType) {
        this.shopType = shopType;
    }

    public List<String> getSubOrderId() {
        return subOrderId;
    }

    public void setSubOrderId(List<String> subOrderId) {
        this.subOrderId = subOrderId;
    }

    public Map<String, String> getSubOrderIdMap() {
        return subOrderIdMap;
    }

    public void setSubOrderIdMap(Map<String, String> subOrderIdMap) {
        this.subOrderIdMap = subOrderIdMap;
    }

    public String getSellerReceiveAddressId() {
        return sellerReceiveAddressId;
    }

    public void setSellerReceiveAddressId(String sellerReceiveAddressId) {
        this.sellerReceiveAddressId = sellerReceiveAddressId;
    }

    public String getSellerReceiveProvince() {
        return sellerReceiveProvince;
    }

    public void setSellerReceiveProvince(String sellerReceiveProvince) {
        this.sellerReceiveProvince = sellerReceiveProvince;
    }

    public String getSellerReceiveCity() {
        return sellerReceiveCity;
    }

    public void setSellerReceiveCity(String sellerReceiveCity) {
        this.sellerReceiveCity = sellerReceiveCity;
    }

    public String getSellerReceiveArea() {
        return sellerReceiveArea;
    }

    public void setSellerReceiveArea(String sellerReceiveArea) {
        this.sellerReceiveArea = sellerReceiveArea;
    }

    public String getSellerReceiveTown() {
        return sellerReceiveTown;
    }

    public void setSellerReceiveTown(String sellerReceiveTown) {
        this.sellerReceiveTown = sellerReceiveTown;
    }

    public String getSellerAddress() {
        return sellerAddress;
    }

    public void setSellerAddress(String sellerAddress) {
        this.sellerAddress = sellerAddress;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public int getProductNum() {
        return productNum;
    }

    public void setProductNum(int productNum) {
        this.productNum = productNum;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getWhsecode() {
        return whsecode;
    }

    public void setWhsecode(String whsecode) {
        this.whsecode = whsecode;
    }

    public String getWhsecodedesc() {
        return whsecodedesc;
    }

    public void setWhsecodedesc(String whsecodedesc) {
        this.whsecodedesc = whsecodedesc;
    }

    public List<BusinessGetRefundResponseRefundGoodInfo> getRefundGoods() {
        return refundGoods;
    }

    public void setRefundGoods(List<BusinessGetRefundResponseRefundGoodInfo> refundGoods) {
        this.refundGoods = refundGoods;
    }

    public String getPlatProductId() {
        return platProductId;
    }

    public void setPlatProductId(String platProductId) {
        this.platProductId = platProductId;
    }

    public String getRefundWholeInfo() {
        return refundWholeInfo;
    }

    public void setRefundWholeInfo(String refundWholeInfo) {
        this.refundWholeInfo = refundWholeInfo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getIsPrime() {
        return isPrime;
    }

    public void setIsPrime(String isPrime) {
        this.isPrime = isPrime;
    }

    public String getRefundPhase() {
        return refundPhase;
    }

    public void setRefundPhase(String refundPhase) {
        this.refundPhase = refundPhase;
    }

    public String getRefundVersion() {
        return refundVersion;
    }

    public void setRefundVersion(String refundVersion) {
        this.refundVersion = refundVersion;
    }

    public TbRefundLogisticsInterceptDto getTbRefundLogisticsIntercept() {
        return tbRefundLogisticsIntercept;
    }

    public void setTbRefundLogisticsIntercept(TbRefundLogisticsInterceptDto tbRefundLogisticsIntercept) {
        this.tbRefundLogisticsIntercept = tbRefundLogisticsIntercept;
    }

    public LocalDateTime getRdsCreateTime() {
        return rdsCreateTime;
    }

    public void setRdsCreateTime(LocalDateTime rdsCreateTime) {
        this.rdsCreateTime = rdsCreateTime;
    }

    public LocalDateTime getRdsModifyTime() {
        return rdsModifyTime;
    }

    public void setRdsModifyTime(LocalDateTime rdsModifyTime) {
        this.rdsModifyTime = rdsModifyTime;
    }

    public String getIsSuccess() { return isSuccess; }

    public void setIsSuccess(String isSuccess) {
        this.isSuccess = isSuccess;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public String getSubMessage() {
        return subMessage;
    }

    public void setSubMessage(String subMessage) {
        this.subMessage = subMessage;
    }

    public String getSpecialRefundType() {
        return specialRefundType;
    }

    public void setSpecialRefundType(String specialRefundType) {
        this.specialRefundType = specialRefundType;
    }

    //endregion
}
