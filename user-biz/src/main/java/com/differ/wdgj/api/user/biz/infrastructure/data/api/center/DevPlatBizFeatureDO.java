package com.differ.wdgj.api.user.biz.infrastructure.data.api.center;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 平台业务特性实体（dev_plat_bizFeature）
 *
 * <AUTHOR>
 * @date 2024-06-20 17:09
 */
public class DevPlatBizFeatureDO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 平台
     */
    private Integer plat;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 配置模式 0 DEFAULT 1 NORMAL 2 CUSTOMIZATION
     */
    private Integer configMode;

    /**
     * 配置约束
     */
    private String configRestraint;

    /**
     * 配置内容
     */
    private String configContent;

    /**
     * 是否生效
     */
    private Byte enable;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime modifiedTime;

    // region

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPlat() {
        return plat;
    }

    public void setPlat(Integer plat) {
        this.plat = plat;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Integer getConfigMode() {
        return configMode;
    }

    public void setConfigMode(Integer configMode) {
        this.configMode = configMode;
    }

    public String getConfigRestraint() {
        return configRestraint;
    }

    public void setConfigRestraint(String configRestraint) {
        this.configRestraint = configRestraint;
    }

    public String getConfigContent() {
        return configContent;
    }

    public void setConfigContent(String configContent) {
        this.configContent = configContent;
    }

    public Byte getEnable() {
        return enable;
    }

    public void setEnable(Byte enable) {
        this.enable = enable;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    // endregion
}
