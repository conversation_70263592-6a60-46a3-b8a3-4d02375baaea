package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.user.biz.infrastructure.data.dto.wdgj.MatchOrderGoodsResultDto;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 网店管家存储过程仓储
 *
 * <AUTHOR>
 * @date 2024-07-04 20:26
 */
public interface WdgjStoredProcedureMapper {
    /**
     * 商品匹配
     *
     * @param shopId       店铺Id
     * @param tradeGoodsNo 商品编码
     * @param goodsName    商品名称
     * @param goodsSpec    商品规格
     */
    MatchOrderGoodsResultDto matchOrderGoods(@Param("shopId") int shopId, @Param("tradeGoodsNo") String tradeGoodsNo, @Param("goodsName") String goodsName, @Param("goodsSpec") String goodsSpec);

    /**
     * 售后单变更通知
     *
     * @param billId      售后单Id
     * @param noticeTypes 消息通知类别，多个用逗号分割
     */
    void afterSaleChangeNotice(@Param("billId") int billId, @Param("noticeTypes") String noticeTypes);

    /**
     * 计算普通商品库存
     *
     * @param shopId       店铺Id
     * @param goodsId      erp商品Id
     * @param specId       erp规格Id
     * @param warehouseIds erp仓库Id列表
     */
    Map<String, Object> calculationNormalStock(@Param("shopId") Integer shopId, @Param("goodsId") Integer goodsId, @Param("specId") Integer specId, @Param("warehouseIds") String warehouseIds);

    /**
     * 计算组合商品库存
     *
     * @param type         同步规则
     * @param fitId        erp组合商品Id
     * @param warehouseIds erp仓库Id列表
     */
    BigDecimal calculationFitStock(@Param("type") Integer type, @Param("fitId") Integer fitId, @Param("warehouseIds") String warehouseIds);
}
