package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

import com.differ.wdgj.api.user.biz.domain.stock.data.task.OrderUpdateBusinessTypesEnum;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 订单业务触发表（对应表g_api_tradeBusinessUpdate）
 * <AUTHOR>
 * @date 2024-03-20 19:11
 */
public class ApiTradeBusinessUpdateDO {
    /**
    * 计算的增长Id
    */
    private int recId;

    /**
    * 业务类型 {@link OrderUpdateBusinessTypesEnum}
    */
    private int businessType;

    /**
    * 原始单Id
    */
    private int billId;

    /**
    * 外部店铺Id
    */
    private int shopId;

    /**
    * 原始单号
    */
    private String tradeNo;

    /**
    * 状态(0-待触发，1-已触发)
    */
    private int status;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 已重试次数
    */
    private int retryCount;

    /**
    * 业务扩展信息
    */
    private String extendInfo;

    //region get/set
    public int getRecId() {
        return recId;
    }

    public void setRecId(int recId) {
        this.recId = recId;
    }

    public int getBusinessType() {
        return businessType;
    }

    public void setBusinessType(int businessType) {
        this.businessType = businessType;
    }

    public int getBillId() {
        return billId;
    }

    public void setBillId(int billId) {
        this.billId = billId;
    }

    public int getShopId() {
        return shopId;
    }

    public void setShopId(int shopId) {
        this.shopId = shopId;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public String getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }
    //endregion


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiTradeBusinessUpdateDO that = (ApiTradeBusinessUpdateDO) o;
        return businessType == that.businessType && billId == that.billId && shopId == that.shopId && Objects.equals(tradeNo, that.tradeNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(businessType, billId, shopId, tradeNo);
    }
}
