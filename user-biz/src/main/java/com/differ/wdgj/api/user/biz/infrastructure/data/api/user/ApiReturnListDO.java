package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.AfterSalePostStatusEnum;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.enums.WdgjRefundTypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyExchangeStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 售后单数据库实体(g_api_return_list)
 *
 * <AUTHOR>
 * @date 2024-06-27 19:44
 */
public class ApiReturnListDO {
    /**
     * 主键。
     */
    private int billId;

    /**
     * 退款单编号。
     */
    private String refundId;

    /**
     * TradeID。
     */
    private Integer tradeID;

    /**
     * 最后一次更新的时间。
     */
    private LocalDateTime getTime;

    /**
     * 递交状态。
     * {@link AfterSalePostStatusEnum}
     */
    private int curStatus;

    /**
     * 发货状态。
     */
    private int synStatus;

    /**
     * 原始单id（g_api_tradeList）。
     */
    private Integer oldBillID;

    /**
     * 交易主订单号。
     */
    private String oldTid;

    /**
     * 交易子订单号。
     */
    private String oldOid;

    /**
     * 退款创建时间。
     */
    private LocalDateTime createdTime;

    /**
     * 申请退款原因。
     */
    private String returnReason;

    /**
     * 退款状态。
     * {@link PolyExchangeStatusEnum}::wdgjValue
     */
    private String returnStatus;

    /**
     * 退款说明。
     */
    private String remark;

    /**
     * 0未知 ；1天猫退款；2淘宝退款 ；3天猫退货。
     * {@link WdgjRefundTypeEnum}
     */
    private int type;

    /**
     * 平台售后单类型
     */
    private String platRefundType;

    /**
     * bid。
     */
    private Integer bid;

    /**
     * isPrime。
     */
    private byte isPrime;

    /**
     * 客户昵称。
     */
    private String customerId;

    /**
     * 卖家昵称ID
     */
    private String nickUId;

    /**
     * 寄回物流公司名称。
     */
    private String logisticName;

    /**
     * 寄回物流公司单号。
     */
    private String logisticNo;

    /**
     * 管家店铺id
     */
    private int shopID;

    /**
     * 唯品会JIT仓库描述
     */
    private String jitWareHouse;

    /**
     * 换货收件人
     */
    private String chgSndTo;

    /**
     * 换货联系方式
     */
    private String chgTel;

    /**
     * 换货州省
     */
    private String chgProvince;

    /**
     * 换货地市
     */
    private String chgCity;

    /**
     * 换货区县
     */
    private String chgTown;

    /**
     * 换货地址
     */
    private String changeAdr;

    /**
     * 实际退款金额
     */
    private BigDecimal refundFee;

    /**
     * 参数code
     * 1.抖音 ：物流公司编码
     */
    private String paramCode;

    /**
     * 保留字段1
     * 1.抖音：换货地址Id
     */
    private String reserved1;

    /**
     * 买家密文信息
     */
    private String receiverMaskId;

    /**
     * 加密类型
     */
    private Integer encryptType;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 新版换货标识
     */
    private int bNewReturnFlag;

    /**
     * 快递自动拦截
     */
    private boolean bexInterceptAuto;

    /**
     * 快递拦截出资方
     */
    private String exInterceptInvestor;

    /**
     * 快递拦截状态
     */
    private String exInterceptEnum;

    /**
     * 仓库编码
     */
    private String whseCode;

    /**
     * 退款阶段
     */
    private String refundPhase;

    /**
     * 售后版本
     */
    private String refundVersion;

    //region get/set
    public int getBillId() {
        return billId;
    }

    public void setBillId(int billId) {
        this.billId = billId;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public Integer getTradeID() {
        return tradeID;
    }

    public void setTradeID(Integer tradeID) {
        this.tradeID = tradeID;
    }

    public int getSynStatus() {
        return synStatus;
    }

    public void setSynStatus(int synStatus) {
        this.synStatus = synStatus;
    }

    public LocalDateTime getGetTime() {
        return getTime;
    }

    public void setGetTime(LocalDateTime getTime) {
        this.getTime = getTime;
    }

    public int getCurStatus() {
        return curStatus;
    }

    public void setCurStatus(int curStatus) {
        this.curStatus = curStatus;
    }

    public Integer getOldBillID() {
        return oldBillID;
    }

    public void setOldBillID(Integer oldBillID) {
        this.oldBillID = oldBillID;
    }

    public String getOldTid() {
        return oldTid;
    }

    public void setOldTid(String oldTid) {
        this.oldTid = oldTid;
    }

    public String getOldOid() {
        return oldOid;
    }

    public void setOldOid(String oldOid) {
        this.oldOid = oldOid;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public void setReturnReason(String returnReason) {
        this.returnReason = returnReason;
    }

    public String getReturnStatus() {
        return returnStatus;
    }

    public void setReturnStatus(String returnStatus) {
        this.returnStatus = returnStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public byte getIsPrime() {
        return isPrime;
    }

    public void setIsPrime(byte isPrime) {
        this.isPrime = isPrime;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getNickUId() {
        return nickUId;
    }

    public void setNickUId(String nickUId) {
        this.nickUId = nickUId;
    }

    public String getLogisticName() {
        return logisticName;
    }

    public void setLogisticName(String logisticName) {
        this.logisticName = logisticName;
    }

    public String getLogisticNo() {
        return logisticNo;
    }

    public void setLogisticNo(String logisticNo) {
        this.logisticNo = logisticNo;
    }

    public int getShopID() {
        return shopID;
    }

    public void setShopID(int shopID) {
        this.shopID = shopID;
    }

    public String getJitWareHouse() {
        return jitWareHouse;
    }

    public void setJitWareHouse(String jitWareHouse) {
        this.jitWareHouse = jitWareHouse;
    }

    public String getChgSndTo() {
        return chgSndTo;
    }

    public void setChgSndTo(String chgSndTo) {
        this.chgSndTo = chgSndTo;
    }

    public String getChgTel() {
        return chgTel;
    }

    public void setChgTel(String chgTel) {
        this.chgTel = chgTel;
    }

    public String getChgProvince() {
        return chgProvince;
    }

    public void setChgProvince(String chgProvince) {
        this.chgProvince = chgProvince;
    }

    public String getChgCity() {
        return chgCity;
    }

    public void setChgCity(String chgCity) {
        this.chgCity = chgCity;
    }

    public String getChgTown() {
        return chgTown;
    }

    public void setChgTown(String chgTown) {
        this.chgTown = chgTown;
    }

    public String getChangeAdr() {
        return changeAdr;
    }

    public void setChangeAdr(String changeAdr) {
        this.changeAdr = changeAdr;
    }

    public BigDecimal getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(BigDecimal refundFee) {
        this.refundFee = refundFee;
    }

    public String getParamCode() {
        return paramCode;
    }

    public void setParamCode(String paramCode) {
        this.paramCode = paramCode;
    }

    public String getReserved1() {
        return reserved1;
    }

    public void setReserved1(String reserved1) {
        this.reserved1 = reserved1;
    }

    public String getReceiverMaskId() {
        return receiverMaskId;
    }

    public void setReceiverMaskId(String receiverMaskId) {
        this.receiverMaskId = receiverMaskId;
    }

    public Integer getEncryptType() {
        return encryptType;
    }

    public void setEncryptType(Integer encryptType) {
        this.encryptType = encryptType;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public int getbNewReturnFlag() {
        return bNewReturnFlag;
    }

    public void setbNewReturnFlag(int bNewReturnFlag) {
        this.bNewReturnFlag = bNewReturnFlag;
    }

    public boolean isBexInterceptAuto() {
        return bexInterceptAuto;
    }

    public void setBexInterceptAuto(boolean bexInterceptAuto) {
        this.bexInterceptAuto = bexInterceptAuto;
    }

    public String getExInterceptInvestor() {
        return exInterceptInvestor;
    }

    public void setExInterceptInvestor(String exInterceptInvestor) {
        this.exInterceptInvestor = exInterceptInvestor;
    }

    public String getExInterceptEnum() {
        return exInterceptEnum;
    }

    public void setExInterceptEnum(String exInterceptEnum) {
        this.exInterceptEnum = exInterceptEnum;
    }

    public String getWhseCode() {
        return whseCode;
    }

    public void setWhseCode(String whseCode) {
        this.whseCode = whseCode;
    }

    public String getRefundPhase() {
        return refundPhase;
    }

    public void setRefundPhase(String refundPhase) {
        this.refundPhase = refundPhase;
    }

    public String getRefundVersion() {
        return refundVersion;
    }

    public void setRefundVersion(String refundVersion) {
        this.refundVersion = refundVersion;
    }

    public String getPlatRefundType() {
        return platRefundType;
    }

    public void setPlatRefundType(String platRefundType) {
        this.platRefundType = platRefundType;
    }

    //endregion
}
