package com.differ.wdgj.api.user.biz.domain.apicall.plugins.authorization;

import com.differ.wdgj.api.user.biz.domain.apicall.AbstractApiCall;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallContext;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallExtendedInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.common.syncaccount.SyncAccountRequestBizData;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.common.syncaccount.SyncAccountResponseBizData;

/**
 * 同步账号
 *
 * <AUTHOR>
 * @date 2024/8/27 下午4:26
 */
public class SyncAccountApiCall extends AbstractApiCall<SyncAccountRequestBizData, SyncAccountResponseBizData> {
    /**
     * 获取接口类型
     *
     * @return 接口类型
     */
    @Override
    protected PolyAPITypeEnum getApiType() {
        return PolyAPITypeEnum.COMMOM_SYNCACCOUNT;
    }

    /**
     * 请求前执行
     *
     * @param context          上下文
     * @param requestBizData   请求业务数据
     * @param callExtendedInfo 请求扩展数据
     */
    @Override
    protected void onBeforeRequest(ApiCallContext context, SyncAccountRequestBizData requestBizData, ApiCallExtendedInfo callExtendedInfo) {
        //  请求参数需加密处理
        // callExtendedInfo.getSignExtraData().put(PolyBasalParamEnum.BIZ_CONTENT.toString(), );
    }
}
