package com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund;

import java.time.LocalDateTime;

/**
 * 淘宝售后单下载外部请求
 *
 * <AUTHOR>
 * @date 2025/4/7 下午2:04
 */
public class RdsTbRefundOutRequest {
    /**
     * 开始时间
     */
    private LocalDateTime startUpdateTime;

    /**
     * 结束时间
     */
    private LocalDateTime endUpdateTime;

    /**
     * 每页大小
     */
    private int pageSize;

    /**
     * 页码
     */
    private int pageIndex;

    /**
     * 昵称
     */
    private String sellNick;


    public LocalDateTime getStartUpdateTime() {
        return startUpdateTime;
    }

    public void setStartUpdateTime(LocalDateTime startUpdateTime) {
        this.startUpdateTime = startUpdateTime;
    }

    public LocalDateTime getEndUpdateTime() {
        return endUpdateTime;
    }

    public void setEndUpdateTime(LocalDateTime endUpdateTime) {
        this.endUpdateTime = endUpdateTime;
    }

    public String getSellNick() {
        return sellNick;
    }

    public void setSellNick(String sellNick) {
        this.sellNick = sellNick;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
    }
}
