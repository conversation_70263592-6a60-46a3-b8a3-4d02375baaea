package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote;

import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;

import java.time.LocalDateTime;

/**
 * 会员库店铺配置信息
 *
 * <AUTHOR>
 * @date 2024-06-24 20:22
 */
public class ApiShopConfigDto {
    /**
     * Api店铺Id
     */
    private Integer apiShopId;

    /**
     * 外部店铺id
     */
    private Integer shopId;

    /**
     * 业务类型{@link ApiShopConfigBizTypes}
     */
    private Integer bizType;

    /**
     * 配置约束，Json数据
     */
    private String configValue;

    /**
     * 更新时间
     */
    private LocalDateTime modifiedTime;

    // region get/set
    public Integer getApiShopId() {
        return apiShopId;
    }

    public void setApiShopId(Integer apiShopId) {
        this.apiShopId = apiShopId;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public LocalDateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(LocalDateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    //endregion
}
