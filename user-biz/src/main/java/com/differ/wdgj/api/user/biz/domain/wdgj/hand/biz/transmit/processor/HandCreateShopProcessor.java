package com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.processor;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.component.util.tools.ExtUtils;
import com.differ.wdgj.api.user.biz.domain.shop.config.subdomain.IBasicsShopConfigOperationService;
import com.differ.wdgj.api.user.biz.domain.shop.config.subdomain.impl.BasicsShopConfigOperationService;
import com.differ.wdgj.api.user.biz.domain.wdgj.bizconfig.impl.ClearShopConfigLocalCacheProcessor;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.IHandBizTransmitProcessor;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data.HandBizTransmitMqDto;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data.HandBizTransmitResult;
import com.differ.wdgj.api.user.biz.domain.wdgj.hand.biz.transmit.data.HandCreateShopRequest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.shop.ApiShopConfigCache;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.DevShopConfigDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.ApiBizConfigSyncTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.SwitchDbContext;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.center.DevShopConfigMapper;
import com.differ.wdgj.api.user.biz.tasks.mq.jmq.plugins.ApiBizConfigSyncMqHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 手动创建店铺
 *
 * <AUTHOR>
 * @date 2025/2/21 下午3:10
 */
public class HandCreateShopProcessor implements IHandBizTransmitProcessor {
    //region 常量
    /**
     * 标题
     */
    private final static String CAPTION = "手动创建店铺";
    //endregion

    @Override
    public HandBizTransmitResult execute(HandBizTransmitMqDto dto) {
        // 基础信息校验
        if (StringUtils.isEmpty(dto.getOutAccount())) {
            return HandBizTransmitResult.failedResult("会员名为空");
        }
        if (dto.getOutShopId() == 0) {
            return HandBizTransmitResult.failedResult("会员库店铺Id为0");
        }

        // 解析业务参数
        HandCreateShopRequest createShopRequest = JsonUtils.deJson(dto.getHandBizRequest(), HandCreateShopRequest.class);
        if (createShopRequest == null) {
            return HandBizTransmitResult.failedResult("反序列化业务参数失败");
        }
        if(createShopRequest.getShopId() == null || createShopRequest.getShopId() == 0){
            return HandBizTransmitResult.failedResult("中心库店铺Id为0");
        }

        // 按需获取公共库店铺配置
        DevShopConfigMapper devShopConfigMapper = BeanContextUtil.getBean(DevShopConfigMapper.class);
        List<DevShopConfigDO> shopConfigs =
                DBSwitchUtil.doDBWithContext(SwitchDbContext.buildEsApi(),
                        () -> devShopConfigMapper.getShopConfigById(createShopRequest.getShopId()));
        if(CollectionUtils.isEmpty(shopConfigs)){
            return HandBizTransmitResult.failedResult("查询公共库店铺配置失败");
        }
        LogFactory.info(CAPTION, dto.getOutAccount(),  () -> ExtUtils.stringBuilderAppend(String.format("会员名：%s；店铺Id：%s；店铺配置：%s", dto.getOutAccount(), dto.getOutShopId(), JsonUtils.toJson(shopConfigs))));

        // 需要初始化的店铺配置类型
        List<ApiShopConfigBizTypes> needInitBizTypes = Arrays.asList(ApiShopConfigBizTypes.DOWNLOAD_ORDER, ApiShopConfigBizTypes.AFTER_SALES, ApiShopConfigBizTypes.SYNC_STOCK);
        // 初始化会员库存通过店铺配置
        IBasicsShopConfigOperationService shopConfigOperationService = new BasicsShopConfigOperationService();
        DBSwitchUtil.doDBWithUser(dto.getOutAccount(),
                () -> {
                    for (ApiShopConfigBizTypes needInitBizType : needInitBizTypes) {
                        // 更新数据库
                        DevShopConfigDO shopConfig = shopConfigs.stream().filter(x -> x.getType() == needInitBizType.getValue()).findFirst().orElse(null);
                        if(shopConfig != null){
                            shopConfigOperationService.updateShopConfig(dto.getOutAccount(), dto.getOutShopId(), needInitBizType, shopConfig.getConfigValue());
                            // 清除Redis缓存
                            ApiShopConfigCache.create(dto.getOutAccount()).clearCache(dto.getOutShopId(), needInitBizType);
                        }
                    }
                    shopConfigOperationService.updateApiShopId(dto.getOutAccount(), dto.getOutShopId(), createShopRequest.getShopId());
                });

        // 发送内存缓存更新JMQ通知
        ApiBizConfigSyncMqHandler apiBizConfigSyncMqHandler = SystemAppConfig.get().isLocalTest()
                ? new ApiBizConfigSyncMqHandler()
                : BeanContextUtil.getBean(ApiBizConfigSyncMqHandler.class);
        String bizConfigInfo = ClearShopConfigLocalCacheProcessor.buildBizEntity(dto.getOutAccount(), dto.getOutShopId(), needInitBizTypes);
        apiBizConfigSyncMqHandler.sendMessage(ApiBizConfigSyncTypeEnum.CLEAR_SHOP_CONFIG_LOCAL_CACHE, bizConfigInfo);

        return HandBizTransmitResult.successResult();
    }
}
