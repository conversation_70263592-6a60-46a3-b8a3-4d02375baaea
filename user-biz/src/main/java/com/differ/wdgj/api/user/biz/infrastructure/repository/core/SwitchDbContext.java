package com.differ.wdgj.api.user.biz.infrastructure.repository.core;

import com.differ.wdgj.api.component.multidb.core.DataSourceContext;
import com.differ.wdgj.api.user.biz.infrastructure.config.SystemAppConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.SystemEnvTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.SystemErrorCodes;
import com.differ.wdgj.api.user.biz.infrastructure.exception.AppException;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * @Description 数据库切换的上下文信息
 * <AUTHOR>
 * @Date 2020-07-10 10:52
 */
public class SwitchDbContext implements DataSourceContext {

    public SwitchDbContext(SwitcherDBTypeEnum dataSourceSwitcherType, String wdgjUser) {
        this.dataSourceSwitcherType = dataSourceSwitcherType;
        this.wdgjUser = wdgjUser;
    }

    /***
     * 数据库自动切换类型
     */
    private SwitcherDBTypeEnum dataSourceSwitcherType;

    /**
     * 管家会员名
     */
    private String wdgjUser;

    /**
     * 唯一键，区别数据源
     */
    private String uniqueKey;

    /**
     * 创建管家类型数据源上下文
     *
     * @param wdgjUser
     * @return
     */
    public static SwitchDbContext buildWDGJ(String wdgjUser) {
        return new SwitchDbContext(SwitcherDBTypeEnum.WDGJ, wdgjUser);
    }

    /**
     * 创建RDS类型数据源上下文
     *
     * @return
     */
    public static SwitchDbContext buildRdsPush(String wdgjUser) {
        return new SwitchDbContext(SwitcherDBTypeEnum.RDS_PUSH, wdgjUser);
    }

    /**
     * 创建Api类型数据源上下文
     *
     * @return
     */
    public static SwitchDbContext buildEsApi() {
        return new SwitchDbContext(SwitcherDBTypeEnum.API, null);
    }

    public SwitcherDBTypeEnum getDataSourceSwitcherType() {
        return dataSourceSwitcherType;
    }

    public String getWdgjUser() {
        return wdgjUser;
    }

    @Override
    public String getUniqueKey() {
        if (StringUtils.isEmpty(uniqueKey)) {
            uniqueKey = String.format("%s-%s", dataSourceSwitcherType, StringUtils.isEmpty(wdgjUser) ? "" : wdgjUser).toLowerCase();
        }
        return uniqueKey;
    }

    /**
     * 获取数据库名
     *
     * @return
     */
    @Override
    public String getDbName() {
        String dbName = null;
        switch (this.dataSourceSwitcherType) {
            case WDGJ:
                dbName = String.format("wdgjyun_%s", wdgjUser).toLowerCase();
                break;
            case RDS_PUSH:
                dbName = getRdsPushDbName();
                break;
            case API:
                dbName = getEsApiDbName();
                break;
            default:
                throw new AppException(SystemErrorCodes.LOGICERROR, String.format("代理不支持%s库操作", this.dataSourceSwitcherType));
        }
        return dbName;
    }

    /**
     * 获取RDS数据库名
     *
     * @return
     */
    private String getRdsPushDbName() {
        String dbName = null;
        SystemEnvTypeEnum systemEnvType = SystemAppConfig.get().getSystemEnvTypeEnum();
        switch (systemEnvType) {
            case PROD:
                dbName = "sys_info";
                break;
            default:
                dbName = "sys_info2";
                break;
        }
        return dbName;
    }

    /**
     * 获取API数据库名
     *
     * @return
     */
    private String getEsApiDbName() {
        String dbName = null;
        SystemEnvTypeEnum systemEnvType = SystemAppConfig.get().getSystemEnvTypeEnum();
        switch (systemEnvType) {
            case DEV:
                dbName = "differyunapi2017_dev";
                break;
            case MODULE_TEST:
                dbName = "differyunapi2017_test";
                break;
            case SYSTEM_TEST:
                dbName = "differyunapi2017_system";
                break;
            default:
                dbName = "differyunapi2017_official";
                break;
        }
        return dbName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SwitchDbContext that = (SwitchDbContext) o;
        return dataSourceSwitcherType == that.dataSourceSwitcherType &&
                Objects.equals(wdgjUser, that.wdgjUser);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dataSourceSwitcherType, wdgjUser);
    }
}
