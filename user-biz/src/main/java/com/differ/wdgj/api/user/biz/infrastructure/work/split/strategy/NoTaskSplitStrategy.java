package com.differ.wdgj.api.user.biz.infrastructure.work.split.strategy;


import com.differ.wdgj.api.user.biz.infrastructure.work.data.PageDynamicStatus;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.SubPageTask;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.TaskSplitStrategyContext;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.RunStatusEnum;

/**
 * 固定拆分方式
 *
 * <AUTHOR>
 * @date 2024/7/12 13:53
 */
public class NoTaskSplitStrategy implements TaskSplitStrategy<SubPageTask> {
    // region 变量

    /**
     * 任务拆分策略上下文
     */
    private final TaskSplitStrategyContext context;

    // endregion

    // region 构造器

    public NoTaskSplitStrategy(TaskSplitStrategyContext context) {
        this.context = context;
    }

    // endregion

    /**
     * 拆分下一个任务
     *
     * @param lastSubTask 上次的子任务
     * @param first       首次拆
     * @return 返回拆分子任务
     */
    @Override
    public SubPageTask splitNext(SubPageTask lastSubTask, boolean first) {
        if (first) {
            PageDynamicStatus pageDynamicStatus = new PageDynamicStatus();
            pageDynamicStatus.setRunStatus(RunStatusEnum.RUNNING);
            pageDynamicStatus.setPageIndex(1);
            pageDynamicStatus.setPageSize(lastSubTask.getPageSize());
            if (null != lastSubTask.getTaskSourceStartTime() && null != lastSubTask.getTaskSourceEndTime()) {
                pageDynamicStatus.setTimeType(lastSubTask.getSourceTimeType());
            }
            pageDynamicStatus.setLoadStartTime(lastSubTask.getTaskSourceStartTime());
            pageDynamicStatus.setLoadEndTime(lastSubTask.getTaskSourceEndTime());
            lastSubTask.setDynamicStatus(pageDynamicStatus);
            return lastSubTask;
        } else {
            return null;
        }
    }
}
