package com.differ.wdgj.api.user.biz.domain.stock.data.enums;

import com.differ.wdgj.api.component.util.enums.CodeEnum;
import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.NameEnum;

/**
 * 菠萝派库存同步错误枚举
 *
 * <AUTHOR>
 * @date 2024-03-05 19:44
 */
public enum SyncStockPolyErrorCodeEnum implements NameEnum, CodeEnum {

    // region 枚举

    /**
     * 未知错误
     * 根据subCode和caption未识别异常时赋值
     */
    UNKNOWN_ERROR("", "【API】未知错误", SyncStockRestrictedModeEnum.None),

    /**
     * 无效商品
     */
    PRODUCT_NOT_EXIST("LXO.REQUEST_FAILURE.PRODUCTNOTEXIST", "无效商品", SyncStockRestrictedModeEnum.None),

    /**
     * 无效SKU
     */
    SKU_NOT_EXIST("LXO.REQUEST_FAILURE.SKUNOTEXIST", "无效SKU", SyncStockRestrictedModeEnum.None),

    /**
     * 授权过期
     */
    SESSION_EXPIRED("LXO.REQUEST_FAILURE.SESSIONEXPIRED", "授权过期", SyncStockRestrictedModeEnum.None),

    /**
     * 上下架失败
     */
    SHELVES_FAILED("LXO.REQUEST_FAILURE.FIRERULES", "上下架失败", SyncStockRestrictedModeEnum.UnmountFailure),

    /**
     * 活动中商品
     */
    STOCK_LOCK("LXO.REQUEST_FAILURE.STOCKLOCK", "活动中商品", SyncStockRestrictedModeEnum.Activity),

    /**
     * 平台限定商品
     */
    STOCK_PLATOPERATIONLIMIT("LXO.REQUEST_FAILURE.PLATOPERATIONLIMIT", "平台限定商品", SyncStockRestrictedModeEnum.GoodsLimit),

    /**
     * 平台未知错误
     */
    STOCK_PLATSERVICEERROR("LXO.REQUEST_FAILURE.PLATSERVICEERROR", "平台未知错误", SyncStockRestrictedModeEnum.PlatServiceError),

    /**
     * 禁止同步库存
     */
    STOCK_FORBIDDEN("LXO.REQUEST_FAILURE.STOCKFORBIDDEN", "禁止同步库存",SyncStockRestrictedModeEnum.GoodsLimit),

    /**
     * 分销商品
     */
    FEN_XIAO("LXO.REQUEST_FAILURE.FENXIAO", "分销商品", SyncStockRestrictedModeEnum.None),

    /**
     * 停用库存同步
     */
    STOCK_DISABLE("LXO.REQUEST_FAILURE.STOCKDISABLE", "停用库存同步", SyncStockRestrictedModeEnum.None),

    /**
     * 触发熔断
     */
    CIRCUIT_BREAK_HINT("LXO.VipShopJit.REQUEST_FAILURE.VIPAPIS_INVENTORYSERVICE_CIRCUIT_BREAK_HINT", "触发熔断", SyncStockRestrictedModeEnum.None),

    /**
     * 停用库存同步保留匹配关系
     */
    DOWNGRADE_STOP_SYNC_RETAINED_MATCH("LXO.REQUEST_FAILURE.INVALIDPERMISSION", "停用库存同步保留匹配关系", SyncStockRestrictedModeEnum.None),

    /**
     * 停用库存同步，可删除匹配关系
     */
    DOWNGRADE_STOP_SYNC_NOT_RETAINED_MATCH("LXO.REQUEST_FAILURE.STOCK.STOP.NOT.RETAINED.MATCH", "停用库存同步，可删除匹配关系", SyncStockRestrictedModeEnum.None),

    /**
     * 淘特同步失败
     */
    TAOBAO_C_TYPE_STOCK_ERROR("LXO.REQUEST_FAILURE.NEWINTERFACECALLFAIL", "淘特同步失败", SyncStockRestrictedModeEnum.None),

    /**
     * 淘宝C店商品同步失败
     */
    TAOBAO_C_TYPE_CATEGORY_STOCK_ERROR("LXO.REQUEST_FAILURE.NEWINTERFACECALLFAIL.CATEGORIES", "淘宝C店条目错误码", SyncStockRestrictedModeEnum.None),

    /**
     * 淘宝C店商品属性不存在或者规格已经被删除
     */
    TAOBAO_C_GOODS_NEED_CHECK("LXO.REQUEST_FAILURE.CGOODS_NEED_CHECK", "淘宝C店商品属性不存在或者规格已经被删除", SyncStockRestrictedModeEnum.None),

    /**
     * 淘宝后端商品缺少后端商品id
     */
    TAOBAO_GOODS_LESS_REAL_ITEM_ID("LXO.REQUEST_FAILURE.GOODS_LESS_REALITEMID", "淘宝后端商品缺少后端商品id", SyncStockRestrictedModeEnum.TaobaoRelItem),

    /**
     * 淘宝自定义属性商品异常
     */
    TAOBAO_C_CUSTOMIZE_GOODS("LXO.REQUEST_FAILURE.CCUSTOMIZE_GOODS", "淘宝自定义属性商品异常", SyncStockRestrictedModeEnum.None),

    /**
     * 上架失败
     */
    UP_OPERATE_FAIL("LXO.REQUEST_FAILURE.UP_OPERATE_FAIL", "上架失败", SyncStockRestrictedModeEnum.UnmountFailure),

    /**
     * 上下架失败
     */
    FIRERULES("LXO.REQUEST_FAILURE.FIRERULES", "上下架失败", SyncStockRestrictedModeEnum.UnmountFailure),

    /**
     * 请联系平台提高APP调用量上限或稍后再试
     */
    FREQUENTOPERATION("LXO.REQUEST_FAILURE.FREQUENTOPERATION", "请联系平台提高APP调用量上限或稍后再试", SyncStockRestrictedModeEnum.PlatCurrentLimiting),

    /**
     * 请求超过频率 请过几分钟重试
     */
    BEYONDFREQUENCYLIMIT("LXO.REQUEST_FAILURE.BEYONDFREQUENCYLIMIT", "请求超过频率 请过几分钟重试", SyncStockRestrictedModeEnum.PlatCurrentLimiting),

    /**
     * 服务暂时不可用 一般是发生了降级
     */
    SERVICE_UNAVAILABLE("LXO.REQUEST_FAILURE.SERVICE_UNAVAILABLE", "服务暂时不可用 一般是发生了降级", SyncStockRestrictedModeEnum.PlatServiceError),

    /**
     * 重试该请求
     */
    FAILNEEDRETRY("LXO.REQUEST_FAILURE.FAILNEEDRETRY", "重试该请求", SyncStockRestrictedModeEnum.None),
    ;

    // endregion

    // region 构造器

    /**
     * 构造器
     *
     * @param subCode 错误编码
     * @param caption 错误内容
     */
    SyncStockPolyErrorCodeEnum(String subCode, String caption, SyncStockRestrictedModeEnum restrictedMode) {
        this.subCode = subCode;
        this.caption = caption;
        this.restrictedMode = restrictedMode;
    }

    // endregion

    // region 属性

    /**
     * 错误编号
     */
    private final String subCode;
    /**
     * 错误描述
     */
    private final String caption;
    /**
     *  限制/禁止模式
     */
    private final SyncStockRestrictedModeEnum restrictedMode;

    // endregion

    // region 方法

    @Override
    public String getName() {
        return this.caption;
    }

    @Override
    public String getCode() {
        return this.subCode;
    }

    /**
     * 获取 限制/禁止模式
     * @return 限制/禁止模式
     */
    public SyncStockRestrictedModeEnum getRestrictedMode() {
        return this.restrictedMode;
    }

    // endregion

    //region 静态方法

    /**
     * 根据管家商品的平台值获取对应的枚举。（subCode优先）
     *
     * @param subCode 错误编号
     * @param caption 错误描述
     * @return 对应的枚举
     */
    public static SyncStockPolyErrorCodeEnum create(String subCode, String caption) {
        SyncStockPolyErrorCodeEnum syncStockPolyErrorCode = EnumConvertCacheUtil.convert(subCode, SyncStockPolyErrorCodeEnum.class, EnumConvertType.CODE);
        if(syncStockPolyErrorCode == null){
            syncStockPolyErrorCode = EnumConvertCacheUtil.convert(caption, SyncStockPolyErrorCodeEnum.class, EnumConvertType.VALUE);
        }
        return syncStockPolyErrorCode;
    }

    //endregion
}
