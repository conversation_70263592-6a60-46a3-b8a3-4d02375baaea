package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange.batch;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.handle.AfterSaleHandleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleGoodsSysMatchItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.handle.core.AbstractPerBatchProcessOrderHandle;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 前置批量处理 - 查询商品匹配信息
 *
 * <AUTHOR>
 * @date 2024/8/7 下午4:33
 */
public class BatchQuerySysMatchHandle extends AbstractPerBatchProcessOrderHandle<BusinessGetExchangeOrderResponseOrderItem> {
    //region 常量
    /**
     * api商品匹配仓储(g_api_sysMatch)
     */
    private final ApiSysMatchMapper apiSysMatchMapper = BeanContextUtil.getBean(ApiSysMatchMapper.class);

    //endregion

    //region 构造
    /**
     * 构造
     *
     * @param context 上下文
     */
    public BatchQuerySysMatchHandle(AfterSaleSaveContext context) {
        super(context);
    }
    //endregion

    /**
     * 前置批量查询
     *
     * @param orderItems 原始售后单列表
     * @return 过滤结果
     */
    @Override
    public AfterSaleHandleResult perBatchQueryProcess(List<SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem>> orderItems) {
        // 空校验
        if(CollectionUtils.isEmpty(orderItems)){
            return AfterSaleHandleResult.success();
        }

        // 获取平台规格Id
        Set<String> platGoodsIds = new HashSet<>();
        Set<String> platSkuIds = new HashSet<>();
        orderItems.forEach(sourceOrder -> {
            if(sourceOrder.getPloyOrder() != null && CollectionUtils.isNotEmpty(sourceOrder.getPloyOrder().getExchangeGoods())){
                sourceOrder.getPloyOrder().getExchangeGoods().forEach(exchangeGoods -> {
                    if(StringUtils.isNotEmpty(exchangeGoods.getSku())){
                        platSkuIds.add(exchangeGoods.getSku());
                    }
                    if(StringUtils.isNotEmpty(exchangeGoods.getPlatProductId())){
                        platGoodsIds.add(exchangeGoods.getPlatProductId());
                    }
                });
            }
        });
        // 兼容商品匹配数据无平台规格id，自动补全0的逻辑
        if(CollectionUtils.isNotEmpty(platGoodsIds)){
            platSkuIds.add("0");
        }

        // 查询商品匹配信息
        if(CollectionUtils.isNotEmpty(platGoodsIds) || CollectionUtils.isNotEmpty(platSkuIds)){
            // 查询商品匹配信息
            List<AfterSaleGoodsSysMatchItem> apiReturnInfos = new ArrayList<>();
            DBSwitchUtil.doDBWithUser(context.getMemberName(), () -> apiReturnInfos.addAll(apiSysMatchMapper.selectWithExtraByPlatIds(new ArrayList<>(platGoodsIds), new ArrayList<>(platSkuIds))));

            // 构建订单级匹配信息
            orderItems.forEach(sourceOrder -> {
                sourceOrder.getDbOrderExt().setGoodsSysMatch(apiReturnInfos);
            });
        }

        return AfterSaleHandleResult.success();
    }

    /**
     * 标题
     *
     * @return 标题
     */
    @Override
    protected String caption() {
        return "批量查询g_api_sysMatch";
    }
}
