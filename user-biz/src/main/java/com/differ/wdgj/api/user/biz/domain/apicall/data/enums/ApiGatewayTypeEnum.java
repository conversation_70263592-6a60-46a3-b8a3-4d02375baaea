package com.differ.wdgj.api.user.biz.domain.apicall.data.enums;

import com.differ.wdgj.api.component.util.enums.EnumConvertCacheUtil;
import com.differ.wdgj.api.component.util.enums.EnumConvertType;
import com.differ.wdgj.api.component.util.enums.NameEnum;
import com.differ.wdgj.api.component.util.enums.ValueEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * API网关类型
 *
 * <AUTHOR>
 * @date 2022-03-09 18:21
 */
public enum ApiGatewayTypeEnum implements ValueEnum, NameEnum {
    /**
     * 菠萝派网关
     */
    POLY_API("菠萝派网关", 0),

    /**
     * 云网关
     */
    YUN("云网关", 1);

    // region 属性

    /**
     * 分类名称
     */
    private final String name;
    /**
     * 枚举值
     */
    private final Integer value;

    /**
     * 构造方法
     *
     * @param name  名称
     * @param value 值
     */
    ApiGatewayTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    /**
     * 获取名称
     *
     * @return 名称
     */
    @Override
    public String getName() {
        return this.name;
    }

    /**
     * 获取类型
     *
     * @return 类型
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    /**
     * 根据值获取对应的枚举。
     *
     * @param value 值
     * @return 对应的枚举
     */
    public static ApiGatewayTypeEnum create(String value) {
        if (StringUtils.isBlank(value)) {
            return ApiGatewayTypeEnum.POLY_API;
        }

        return EnumConvertCacheUtil.convert(value, ApiGatewayTypeEnum.class, EnumConvertType.VALUE);
    }
}