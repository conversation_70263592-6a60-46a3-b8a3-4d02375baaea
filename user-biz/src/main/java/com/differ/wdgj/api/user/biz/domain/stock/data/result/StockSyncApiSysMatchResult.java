package com.differ.wdgj.api.user.biz.domain.stock.data.result;

import java.util.Objects;

/**
 * 库存同步匹配表结果
 * <AUTHOR>
 * @date 2024-03-14 13:33
 */
public class StockSyncApiSysMatchResult {

    /**
     * 商品匹配表Id
     */
    private int apiSysMatchId;

    /**
     * 同步状态
     */
    private int isSys;

    /**
     * 同步结果
     */
    private String sysLog;

    /**
     * 同步类型
     */
    private String sysGoodsType;

    /**
     * 触发标识
     */
    private int synFlag;

    /**
     * 同步数量
     */
    private int sysCount;

    //region get/set
    public int getApiSysMatchId() {
        return apiSysMatchId;
    }

    public void setApiSysMatchId(int apiSysMatchId) {
        this.apiSysMatchId = apiSysMatchId;
    }

    public int getIsSys() {
        return isSys;
    }

    public void setIsSys(int isSys) {
        this.isSys = isSys;
    }

    public String getSysLog() {
        return sysLog;
    }

    public void setSysLog(String sysLog) {
        this.sysLog = sysLog;
    }

    public int getSynFlag() {
        return synFlag;
    }

    public void setSynFlag(int synFlag) {
        this.synFlag = synFlag;
    }

    public int getSysCount() {
        return sysCount;
    }

    public void setSysCount(int sysCount) {
        this.sysCount = sysCount;
    }

    public String getSysGoodsType() {
        return sysGoodsType;
    }

    public void setSysGoodsType(String sysGoodsType) {
        this.sysGoodsType = sysGoodsType;
    }
    //endregion

    //region equal/hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StockSyncApiSysMatchResult that = (StockSyncApiSysMatchResult) o;
        return apiSysMatchId == that.apiSysMatchId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(apiSysMatchId);
    }
    //endregion
}
