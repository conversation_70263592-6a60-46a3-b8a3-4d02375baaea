package com.differ.wdgj.api.user.biz.infrastructure.thread.pool.core;

import java.util.concurrent.Callable;

/**
 * @Description 带吉客号的执行器
 * <AUTHOR>
 * @Date 2021/11/22 14:37
 */
public abstract class MemberRunnable extends AbstractMemberTaskAble implements Runnable {


    public MemberRunnable(String jackNo) {
        super(jackNo);
    }

    public MemberRunnable(String jackNo, Object argv) {
        super(jackNo, argv);
    }

    public MemberRunnable(String jackNo, Object argv, boolean supportUnique) {
        super(jackNo, argv, supportUnique);
    }

    public MemberRunnable(String jackNo, Object argv, boolean supportUnique, Long loggerSn) {
        super(jackNo, argv, supportUnique, loggerSn);
    }

    @Override
    public String toString() {
        return this.getTaskId();
    }

    /**
     * Runnable转为Callable
     * @return
     */
    public Callable toCallable() {
        return new MemberCallable(this.getMember(), this.getArgv(), this.isSupportUnique(), this.getLoggerSn()) {
            @Override
            public Object call() throws Exception {
                MemberRunnable.this.run();
                return null;
            }

            @Override
            public String toString() {
                return MemberRunnable.this.toString();
            }
        };
    }
}
