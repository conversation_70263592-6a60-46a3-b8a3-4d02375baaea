package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.load;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;

/**
 * 售后子任务下载操作者 抽象
 *
 * <AUTHOR>
 * @date 2024/9/25 上午11:57
 */
public abstract class AbstractLoadSubTaskOperator implements ILoadSubTask {
    // region 变量

    /**
     * 上下文
     */
    protected AfterSaleLoadTaskContext context;

    // endregion

    // region 构造器
    public AbstractLoadSubTaskOperator(AfterSaleLoadTaskContext context) {
        this.context = context;
    }
    // endregion
}
