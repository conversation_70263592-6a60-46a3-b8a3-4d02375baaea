package com.differ.wdgj.api.user.biz.domain.stock.data;

import java.util.Objects;

/**
 * 匹配唯一键增强
 *
 * <AUTHOR>
 * @date 2024-03-01 19:16
 */
public class MatchIdEnhance {

    /**
     * 匹配 GUID
     */
    private Integer matchId;

    /**
     * 扩展唯一键
     */
    private String extraUnique;

    //region 公共静态方法

    /**
     * 数据类型转换
     *
     * @param matchEnhance 匹配数据
     * @return 结果
     */
    public static MatchIdEnhance convert(GoodsMatchEnhance matchEnhance) {
        MatchIdEnhance guidEnhance = new MatchIdEnhance();
        guidEnhance.setMatchId(matchEnhance.getSysMatch().getId());
        guidEnhance.setExtraUnique(matchEnhance.getMultiSign());
        return guidEnhance;
    }

    /**
     * 数据类型转换
     *
     * @param matchId 匹配表id
     * @return 结果
     */
    public static MatchIdEnhance createNoExtra(int matchId) {
        MatchIdEnhance guidEnhance = new MatchIdEnhance();
        guidEnhance.setMatchId(matchId);
        guidEnhance.setExtraUnique("");
        return guidEnhance;
    }

    /**
     * 数据类型转换
     *
     * @param matchId     匹配表id
     * @param extraUnique 扩展唯一键
     * @return 结果
     */
    public static MatchIdEnhance create(int matchId, String extraUnique) {
        MatchIdEnhance guidEnhance = new MatchIdEnhance();
        guidEnhance.setMatchId(matchId);
        guidEnhance.setExtraUnique(extraUnique);
        return guidEnhance;
    }


    //endregion

    //region 重写基类方法
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        MatchIdEnhance that = (MatchIdEnhance) o;

        return matchId.equals(that.matchId) &&
                extraUnique.equals(that.extraUnique);
    }

    @Override
    public int hashCode() {
        return Objects.hash(matchId, extraUnique);
    }
    //endregion

    // region getter & setter

    public Integer getMatchId() {
        return matchId;
    }

    public void setMatchId(Integer matchId) {
        this.matchId = matchId;
    }

    public String getExtraUnique() {
        return extraUnique;
    }

    public void setExtraUnique(String extraUnique) {
        this.extraUnique = extraUnique;
    }

    // endregion
}
