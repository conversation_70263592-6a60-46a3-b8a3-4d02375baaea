package com.differ.wdgj.api.user.biz.infrastructure.data.api.center;

import com.alibaba.fastjson.annotation.JSONField;
import com.differ.wdgj.api.component.util.json.core.StringDateTimeDeserializer;

import java.time.LocalDateTime;

public class MemberAccountMappingDO {

    /**
     * 用户名
     */
    private String userName;

    /**
     * 外部平台类型[OuterBusinessPlats枚举值之一] 网店管家 = 1;
     */
    private Byte outPlatTypes;

    /**
     * 外部会员主账号
     */
    private String outAccount;

    /**
     * 外部会员授权码
     */
    private String outaccountcode;

    /**
     * 外部授权Key(与对接方通讯)
     */
    private String sessionkey;

    /**
     * 外部会员服务器名称
     */
    private String servername;

    /**
     * 业务服务器IP端口
     */
    private String serveripport;

    /**
     * 外部账号映射表是否启用
     */
    private Boolean isactived;

    /**
     * 创建时间
     */

    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime createtime;

    /**
     * 最后修改时间
     */

    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime lastmodifytime;


    @JSONField(deserializeUsing = StringDateTimeDeserializer.class)
    private LocalDateTime dataversion;
    //endregion

    //region getter setter

    /**
     * 获取用户名
     *
     * @return UserName - 用户名
     */
    public String getUserName() {
        return userName;
    }

    /**
     * 设置用户名
     *
     * @param userName 用户名
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     * 获取外部平台类型[OuterBusinessPlats枚举值之一] 网店管家 = 1;
     *
     * @return OutPlatTypes - 外部平台类型[OuterBusinessPlats枚举值之一] 网店管家 = 1;
     */
    public Byte getOutPlatTypes() {
        return outPlatTypes;
    }

    /**
     * 设置外部平台类型[OuterBusinessPlats枚举值之一] 网店管家 = 1;
     *
     * @param outPlatTypes 外部平台类型[OuterBusinessPlats枚举值之一] 网店管家 = 1;
     */
    public void setOutPlatTypes(Byte outPlatTypes) {
        this.outPlatTypes = outPlatTypes;
    }

    /**
     * 获取外部会员主账号
     *
     * @return OutAccount - 外部会员主账号
     */
    public String getOutAccount() {
        return outAccount;
    }

    /**
     * 设置外部会员主账号
     *
     * @param outAccount 外部会员主账号
     */
    public void setOutAccount(String outAccount) {
        this.outAccount = outAccount;
    }

    /**
     * 获取外部会员授权码
     *
     * @return OutAccountCode - 外部会员授权码
     */
    public String getOutaccountcode() {
        return outaccountcode;
    }

    /**
     * 设置外部会员授权码
     *
     * @param outaccountcode 外部会员授权码
     */
    public void setOutaccountcode(String outaccountcode) {
        this.outaccountcode = outaccountcode;
    }

    /**
     * 获取外部授权Key(与对接方通讯)
     *
     * @return SessionKey - 外部授权Key(与对接方通讯)
     */
    public String getSessionkey() {
        return sessionkey;
    }

    /**
     * 设置外部授权Key(与对接方通讯)
     *
     * @param sessionkey 外部授权Key(与对接方通讯)
     */
    public void setSessionkey(String sessionkey) {
        this.sessionkey = sessionkey;
    }

    /**
     * 获取外部会员服务器名称
     *
     * @return ServerName - 外部会员服务器名称
     */
    public String getServername() {
        return servername;
    }

    /**
     * 设置外部会员服务器名称
     *
     * @param servername 外部会员服务器名称
     */
    public void setServername(String servername) {
        this.servername = servername;
    }

    /**
     * 获取业务服务器IP端口
     *
     * @return ServerIPPort - 业务服务器IP端口
     */
    public String getServeripport() {
        return serveripport;
    }

    /**
     * 设置业务服务器IP端口
     *
     * @param serveripport 业务服务器IP端口
     */
    public void setServeripport(String serveripport) {
        this.serveripport = serveripport;
    }

    /**
     * 获取外部账号映射表是否启用
     *
     * @return IsActived - 外部账号映射表是否启用
     */
    public Boolean getIsactived() {
        return isactived;
    }

    /**
     * 设置外部账号映射表是否启用
     *
     * @param isactived 外部账号映射表是否启用
     */
    public void setIsactived(Boolean isactived) {
        this.isactived = isactived;
    }

    /**
     * 获取创建时间
     *
     * @return CreateTime - 创建时间
     */
    public LocalDateTime getCreatetime() {
        return createtime;
    }

    /**
     * 设置创建时间
     *
     * @param createtime 创建时间
     */
    public void setCreatetime(LocalDateTime createtime) {
        this.createtime = createtime;
    }

    /**
     * 获取最后修改时间
     *
     * @return LastModifyTime - 最后修改时间
     */
    public LocalDateTime getLastmodifytime() {
        return lastmodifytime;
    }

    /**
     * 设置最后修改时间
     *
     * @param lastmodifytime 最后修改时间
     */
    public void setLastmodifytime(LocalDateTime lastmodifytime) {
        this.lastmodifytime = lastmodifytime;
    }

    /**
     * @return DataVersion
     */
    public LocalDateTime getDataversion() {
        return dataversion;
    }

    /**
     * @param dataversion
     */
    public void setDataversion(LocalDateTime dataversion) {
        this.dataversion = dataversion;
    }
    //endregion


    @Override
    public String toString() {
        return "MemberAccountmapping{" +
                "username='" + userName + '\'' +
                ", outplattypes=" + outPlatTypes +
                ", outaccount='" + outAccount + '\'' +
                ", outaccountcode='" + outaccountcode + '\'' +
                ", sessionkey='" + sessionkey + '\'' +
                ", servername='" + servername + '\'' +
                ", serveripport='" + serveripport + '\'' +
                ", isactived=" + isactived +
                ", createtime=" + createtime +
                ", lastmodifytime=" + lastmodifytime +
                ", dataversion=" + dataversion +
                '}';
    }
}