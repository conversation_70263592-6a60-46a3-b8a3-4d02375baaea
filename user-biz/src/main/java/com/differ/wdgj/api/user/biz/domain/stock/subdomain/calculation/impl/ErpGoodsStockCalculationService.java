package com.differ.wdgj.api.user.biz.domain.stock.subdomain.calculation.impl;

import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.ErpGoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.subdomain.calculation.IErpGoodsStockCalculationService;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.WdgjStoredProcedureMapper;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * erp商品库存计算服务
 *
 * <AUTHOR>
 * @date 2025/6/16 20:07
 */
public class ErpGoodsStockCalculationService implements IErpGoodsStockCalculationService {
    //region 常量
    /**
     * 网店管家存储过程仓储
     */
    private final WdgjStoredProcedureMapper wdgjStoredProcedureMapper;

    /**
     * 会员名
     */
    private final String memberName;
    //endregion

    //region 构造
    public ErpGoodsStockCalculationService(String memberName) {
        wdgjStoredProcedureMapper = BeanContextUtil.getBean(WdgjStoredProcedureMapper.class);
        this.memberName = memberName;
    }
    //endregion

    /**
     * 计算普通商品库存
     *
     * @param shopId       店铺Id
     * @param erpGoodsId   erp商品Id
     * @param erpSpecId    erp规格Id
     * @param warehouseIds erp仓库Id列表
     * @return 计算结果
     */
    @Override
    public ErpGoodsStockCalculationResult calculationNormalStock(Integer shopId, Integer erpGoodsId, Integer erpSpecId, List<Integer> warehouseIds) {
        // 调用存储过程
        String warehouseIdsStr = StringUtils.join(warehouseIds, ",");
        Map<String, Object> mapResult = DBSwitchUtil.doDBWithUser(memberName, () -> wdgjStoredProcedureMapper.calculationNormalStock(shopId, erpGoodsId, erpSpecId, warehouseIdsStr));
        // 存储过程结果
        if (MapUtils.isEmpty(mapResult)) {
            return ErpGoodsStockCalculationResult.failed("存储过程未返回数据");
        }

        // 获取错误代码
        Integer errorCode = (Integer) mapResult.get("errmsg");
        if (errorCode == null) {
            return ErpGoodsStockCalculationResult.failed("未获取到错误代码");
        }

        // 根据错误代码处理结果
        switch (errorCode) {
            case 0:
                return ErpGoodsStockCalculationResult.failed("库存计算过程出错");
            case 1:
                return ErpGoodsStockCalculationResult.failed("错误的匹配记录");
            case 3:
                return ErpGoodsStockCalculationResult.failed("同步仓库不能为空");
            case 10:
            case 12:
            case 14:
            case 15:
            case 18:
                return ErpGoodsStockCalculationResult.failed("该记录对应商品已不存在、停用或同步仓库不存在该货品");
            case 100:
                // 获取库存数量
                Integer stockQty = (Integer) mapResult.get("stockqty");
                String detailCount = (String) mapResult.get("DetailCount");
                return ErpGoodsStockCalculationResult.success(BigDecimal.valueOf(stockQty), detailCount);
            default:
                return ErpGoodsStockCalculationResult.failed("未知错误代码" + errorCode);
        }
    }

    /**
     * 计算组合商品库存
     *
     * @param type         同步规则
     * @param fitId        erp组合商品Id
     * @param warehouseIds erp仓库Id列表
     * @return 计算结果
     */
    @Override
    public ErpGoodsStockCalculationResult calculationFitStock(Integer type, Integer fitId, List<Integer> warehouseIds) {
        // 调用存储过程
        String warehouseIdsStr = StringUtils.join(warehouseIds, ",");
        BigDecimal stockQty = DBSwitchUtil.doDBWithUser(memberName, () -> wdgjStoredProcedureMapper.calculationFitStock(type, fitId, warehouseIdsStr));
        return ErpGoodsStockCalculationResult.success(stockQty, StringUtils.EMPTY);
    }
}
