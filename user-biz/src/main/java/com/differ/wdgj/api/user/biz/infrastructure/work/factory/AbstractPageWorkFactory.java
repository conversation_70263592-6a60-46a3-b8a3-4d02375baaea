package com.differ.wdgj.api.user.biz.infrastructure.work.factory;

import com.differ.wdgj.api.user.biz.infrastructure.work.business.page.PageWorkBusinessProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.SubPageTask;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkData;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.enums.WorkEnum;
import com.differ.wdgj.api.user.biz.infrastructure.work.data.WorkResult;
import com.differ.wdgj.api.user.biz.infrastructure.work.mutex.DefaultNoWorkMutex;
import com.differ.wdgj.api.user.biz.infrastructure.work.mutex.WorkMutex;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.DefaultWorkSubTaskOperate;
import com.differ.wdgj.api.user.biz.infrastructure.work.operate.WorkSubTaskOperate;
import com.differ.wdgj.api.user.biz.infrastructure.work.template.PageWorkRunTemplate;
import com.differ.wdgj.api.user.biz.infrastructure.work.template.WorkExecTemplate;

/**
 * 抽象工厂
 *
 * <AUTHOR>
 * @date 2024/7/2 14:47
 */
public abstract class AbstractPageWorkFactory<T extends WorkData<?>, S extends SubPageTask, R extends WorkResult> extends AbstractWorkFactory {

    protected AbstractPageWorkFactory(WorkEnum workEnum) {
        super(workEnum);
    }

    /**
     * 创建模板(核心)
     *
     * @return 模板
     */
    @Override
    public WorkExecTemplate<WorkData<?>> createTemplate() {
        return new PageWorkRunTemplate(workEnum, this);
    }

    /**
     * 创建数据操作
     *
     * @return 数据操作
     */
    @Override
    public WorkSubTaskOperate<S> createDataOperate() {
        return new DefaultWorkSubTaskOperate<>(workEnum);
    }

    /**
     * 创建业务处理器
     *
     * @param workData 任务信息
     * @return 业务处理器
     */
    public abstract PageWorkBusinessProcessor<T, S, R> createBusinessProcessor(T workData);

    /**
     * 工作任务互斥对象
     *
     * @return 工作任务互斥对象
     */
    @Override
    public WorkMutex createWorkMutex() {
        return new DefaultNoWorkMutex();
    }
}
