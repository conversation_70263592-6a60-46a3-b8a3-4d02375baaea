package com.differ.wdgj.api.user.biz.domain.aftersale.save.savedata;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.CheckAfterSaleHashCodeResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.QueryDbOrderResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderDataResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.SourceAfterSaleOrderItem;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetSaveOrderItem;

import java.util.List;

/**
 * 保存售后单到DB操作类-接口
 *
 * <AUTHOR>
 * @date 2024-06-06 14:35
 */
public interface IAfterSaleDataOperation {
    /**
     * 校验hash
     *
     * @param sourceOrders 原始售后单列表
     * @return 校验结果
     */
    <T> SaveAfterSaleResult<List<CheckAfterSaleHashCodeResult>> checkPolyOrderHashCode(List<SourceAfterSaleOrderItem<T>> sourceOrders);

    /**
     * 构建售后单组合信息
     *
     * @param sourceOrders 原始售后单列表
     * @return 售后单组合列表
     */
    <T> SaveAfterSaleResult<List<QueryDbOrderResult>> getOldDbOrderItems(List<SourceAfterSaleOrderItem<T>> sourceOrders);

    /**
     * 保存数据
     *
     * @param convertOrderResults 售后单列表
     */
    SaveAfterSaleResult<List<SaveOrderDataResult>> saveData(final List<TargetSaveOrderItem> convertOrderResults);
}
