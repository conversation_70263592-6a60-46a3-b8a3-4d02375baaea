package com.differ.wdgj.api.user.biz.domain.aftersale.save.notice;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.target.TargetOutNoticeItem;

/**
 * 售后单-外部消息通知接口
 * <AUTHOR>
 * @date 2024-06-06 14:36
 */
public interface IOutAfterSaleNoticeOperation {
    /**
     * 执行通知
     *
     * @param targetOutNoticeItem 外部消息通知订单列表
     */
    void notice(TargetOutNoticeItem targetOutNoticeItem);
}
