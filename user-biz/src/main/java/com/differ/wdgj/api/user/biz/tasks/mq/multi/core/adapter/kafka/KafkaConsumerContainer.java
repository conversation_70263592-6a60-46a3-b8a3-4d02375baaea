package com.differ.wdgj.api.user.biz.tasks.mq.multi.core.adapter.kafka;

import org.springframework.boot.autoconfigure.kafka.KafkaProperties;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * kafka消费者容器,目的是过滤出对应分组的消费者适配器
 *
 * <AUTHOR>
 * @date 2024/4/12 16:01
 */
public class KafkaConsumerContainer {
    /**
     * 消费者容器
     */
    private static Map<KafkaGroup, KafkaAdapter> consumerContainer = new ConcurrentHashMap<>();

    /**
     * 获取消费者适配器，唯一维度：地址，topic，消费组
     *
     * @param topic
     * @param kafkaProperties
     * @return
     */
    public static KafkaAdapter getTopicGroupConsumer(String topic, KafkaProperties kafkaProperties) {
        // 地址，topic，消费组，这三者确定一个监听函数
        List<String> bootstrapServers = kafkaProperties.getBootstrapServers();
        String groupId = kafkaProperties.getConsumer().getGroupId();
        KafkaGroup kafkaGroup = new KafkaGroup(bootstrapServers, topic, groupId);
        return consumerContainer.computeIfAbsent(kafkaGroup, k -> new KafkaAdapter(topic, kafkaProperties));
    }
}
