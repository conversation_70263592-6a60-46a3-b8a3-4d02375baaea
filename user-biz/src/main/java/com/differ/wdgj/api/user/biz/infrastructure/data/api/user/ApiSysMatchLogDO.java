package com.differ.wdgj.api.user.biz.infrastructure.data.api.user;

import java.time.LocalDateTime;

/**
 * 商品匹配日志表
 * g_api_sysmatchlog
 *
 * <AUTHOR>
 * @date 2024-03-11 13:16
 */
public class ApiSysMatchLogDO {
    /**
    * RecID。
    */
    public int recId;

    /**
    * ID。
    */
    public int id;

    /**
    * 平台商品ID。
    */
    public String numiId;

    /**
    * 平台SKU ID。
    */
    public String skuId;

    /**
    * 管家货品ID。
    */
    public int goodsId;

    /**
    * 管家规格ID。
    */
    public int specId;

    /**
    * 日志明细。
    */
    public String logDetail;

    /**
    * 日志记录时间。
    */
    public LocalDateTime logTime;

    /**
    * 操作人。
    */
    public String operator;

    //region get/set
    public int getRecId() {
        return recId;
    }

    public void setRecId(int recId) {
        this.recId = recId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getNumiId() {
        return numiId;
    }

    public void setNumiId(String numiId) {
        this.numiId = numiId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public int getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }

    public int getSpecId() {
        return specId;
    }

    public void setSpecId(int specId) {
        this.specId = specId;
    }

    public String getLogDetail() {
        return logDetail;
    }

    public void setLogDetail(String logDetail) {
        this.logDetail = logDetail;
    }

    public LocalDateTime getLogTime() {
        return logTime;
    }

    public void setLogTime(LocalDateTime logTime) {
        this.logTime = logTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
    //endregion
}
