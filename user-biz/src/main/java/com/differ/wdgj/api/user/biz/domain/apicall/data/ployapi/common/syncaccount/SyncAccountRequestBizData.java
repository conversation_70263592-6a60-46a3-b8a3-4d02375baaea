package com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.common.syncaccount;

import com.differ.wdgj.api.user.biz.domain.apicall.data.core.BasePlatRequestBizData;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 平台账号同步入参
 *
 * <AUTHOR>
 * @date 2024/8/27 下午4:24
 */
public class SyncAccountRequestBizData extends BasePlatRequestBizData {
    /**
     * 第三方平台AppKey
     */
    private String appKey;
    /**
     * 第三方平台AppSecret
     */
    private String appSecret;
    /**
     * 第三方平台SessionKey
     */
    private String sessionKey;
    /**
     * 过期日期
     */
    private LocalDateTime sessionKeyExpiretime;
    /**
     * refreshToken 过期日期
     */
    private LocalDateTime refreshTokenExpireTime;
    /**
     * 订购 过期日期
     */
    private LocalDateTime subscriptionExpireTime;
    /**
     * 有效时间
     */
    private int sessionKeyTimeout;
    /**
     * 自动刷新token的key
     */
    private String refreshTokenKey;
    /**
     * 自定义秘钥信息
     */
    private String customSecret;
    /**
     * 自定义秘钥信息
     */
    private Map<String, String> customSecrets;
    /**
     * 其他参数
     */
    private String otherParms;

    /**
     * 是否返回店铺授权冲突信息
     */
    private Boolean showRepeatShops;

    /**
     * 平台店铺ID
     */
    private String venderId;

    /**
     * 平台店铺名称
     */
    private String nickName;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 菠萝派 Token（用于请求指定 Token，如菠萝派商城）
     */
    private String polyToken;

    //region get/set
    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public LocalDateTime getSessionKeyExpiretime() {
        return sessionKeyExpiretime;
    }

    public void setSessionKeyExpiretime(LocalDateTime sessionKeyExpiretime) {
        this.sessionKeyExpiretime = sessionKeyExpiretime;
    }

    public LocalDateTime getRefreshTokenExpireTime() {
        return refreshTokenExpireTime;
    }

    public void setRefreshTokenExpireTime(LocalDateTime refreshTokenExpireTime) {
        this.refreshTokenExpireTime = refreshTokenExpireTime;
    }

    public LocalDateTime getSubscriptionExpireTime() {
        return subscriptionExpireTime;
    }

    public void setSubscriptionExpireTime(LocalDateTime subscriptionExpireTime) {
        this.subscriptionExpireTime = subscriptionExpireTime;
    }

    public int getSessionKeyTimeout() {
        return sessionKeyTimeout;
    }

    public void setSessionKeyTimeout(int sessionKeyTimeout) {
        this.sessionKeyTimeout = sessionKeyTimeout;
    }

    public String getRefreshTokenKey() {
        return refreshTokenKey;
    }

    public void setRefreshTokenKey(String refreshTokenKey) {
        this.refreshTokenKey = refreshTokenKey;
    }

    public String getCustomSecret() {
        return customSecret;
    }

    public void setCustomSecret(String customSecret) {
        this.customSecret = customSecret;
    }

    public Map<String, String> getCustomSecrets() {
        return customSecrets;
    }

    public void setCustomSecrets(Map<String, String> customSecrets) {
        this.customSecrets = customSecrets;
    }

    public String getOtherParms() {
        return otherParms;
    }

    public void setOtherParms(String otherParms) {
        this.otherParms = otherParms;
    }

    public Boolean getShowRepeatShops() {
        return showRepeatShops;
    }

    public void setShowRepeatShops(Boolean showRepeatShops) {
        this.showRepeatShops = showRepeatShops;
    }

    public String getVenderId() {
        return venderId;
    }

    public void setVenderId(String venderId) {
        this.venderId = venderId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getPolyToken() {
        return polyToken;
    }

    public void setPolyToken(String polyToken) {
        this.polyToken = polyToken;
    }
    //endregion
}
