package com.differ.wdgj.api.user.biz.domain.stock.data.enums;

import com.differ.wdgj.api.component.util.enums.CodeEnum;

/**
 * 库存更新方式
 *
 * <AUTHOR>
 * @date 2024-03-04 17:49
 */
public enum SyncStockTypeEnum implements CodeEnum {
    /**
     * 全量
     */
    Whole("JH_01"),

    /**
     * 增量
     */
    Increment("JH_02"),

    /**
     * 减量
     */
    Decrement("JH_03"),
    ;

    /**
     * 构造
     * @param ployCode 菠萝派枚举代码
     */
    SyncStockTypeEnum(String ployCode){
        this.ployCode = ployCode;
    }

    /**
     *菠萝派枚举代码
     */
    private String ployCode;


    /**
     * 获取菠萝派枚举代码
     *
     * @return 菠萝派枚举代码
     */
    @Override
    public String getCode() {
        return this.ployCode;
    }
}
