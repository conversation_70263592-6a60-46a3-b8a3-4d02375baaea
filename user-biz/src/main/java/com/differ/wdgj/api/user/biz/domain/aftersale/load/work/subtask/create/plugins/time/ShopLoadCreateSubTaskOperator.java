package com.differ.wdgj.api.user.biz.domain.aftersale.load.work.subtask.create.plugins.time;

import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.context.AfterSaleLoadTaskContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadArgs;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.AfterSaleLoadSubTask;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.LoadAfterSaleDetail;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.data.work.LoadAfterSaleWorkResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.load.work.processor.BaseLoadAfterSaleProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.bizfeature.plugins.LoadAfterSalesConfigContent;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * 普通下载创建子任务
 * 涉及根据上一次下载结果获取本次下载时间区间
 *
 * <AUTHOR>
 * @date 2024/9/20 上午11:40
 */
public class ShopLoadCreateSubTaskOperator extends BaseTimeCreateSubTaskOperator {
    // region 构造器
    public ShopLoadCreateSubTaskOperator(AfterSaleLoadTaskContext context, BaseLoadAfterSaleProcessor loadProcessor) {
        super(context, loadProcessor);
    }
    // endregion

    //region 实现基类方法

    /**
     * 子任务特殊处理
     *
     * @param loadArgs  工作任务数据
     * @param subTask 子任务
     * @return 特殊处理后子任务
     */
    @Override
    protected LoadAfterSaleWorkResult subTaskCreateProcess(AfterSaleLoadArgs loadArgs, AfterSaleLoadSubTask subTask) {
        // 基础数据
        LoadAfterSalesConfigContent platFeature = context.getDownloadAfterSalesPlatFeature(subTask.getApiShopType());
        LocalDateTime endTime = LocalDateTime.now();

        // 时间处理
        switch (platFeature.getLoadOrderType()){
            case FULL:
                // 全量下载
                subTask.setTaskSourceStartTime(endTime.minusSeconds(platFeature.getEveryLoadOffset()));
                subTask.setTaskSourceEndTime(endTime);
                break;
            case INCREMENT:
                // 增量下载
                LocalDateTime startTime = endTime.minusSeconds(platFeature.getEveryLoadOffset());
                // 上次一次下载成功，依赖上一次的结束时间作为本次任务开始时间
                if(context.getLastWorkResult() != null){
                    LoadAfterSaleDetail lastSubResult = context.getLastWorkResult().getDetails().stream().filter(x -> StringUtils.equals(x.getSubTypeKey(), subTask.getSubTypeKey())).findFirst().orElse(null);
                    if(lastSubResult != null && lastSubResult.getLastSuccessDataTime() != null && startTime.isBefore(lastSubResult.getLastSuccessDataTime())) {
                        startTime = lastSubResult.getLastSuccessDataTime().minusSeconds(platFeature.getIncrementMaxRangeLimit());
                    }
                }
                subTask.setTaskSourceStartTime(startTime);
                subTask.setTaskSourceEndTime(endTime);
                break;
        }

        return LoadAfterSaleWorkResult.onSuccess();
    }

    //endregion


}
