package com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote;

import com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch.data.CommonWarehouseMatchDTO;

import java.util.Set;

/**
 * 店铺级仓库匹配信息
 *
 * <AUTHOR>
 * @date 2024/12/23 下午5:00
 */
public class ApiShopWhsMatchDto {
    /**
     * 外部店铺id
     */
    private Integer shopId;

    /**
     * 仓库匹配列表
     */
    private Set<CommonWarehouseMatchDTO> whsMatches;


    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public Set<CommonWarehouseMatchDTO> getWhsMatches() {
        return whsMatches;
    }

    public void setWhsMatches(Set<CommonWarehouseMatchDTO> whsMatches) {
        this.whsMatches = whsMatches;
    }
}
