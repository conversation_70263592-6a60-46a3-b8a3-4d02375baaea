package com.differ.wdgj.api.user.biz.domain.apicall.data.enums;

/**
 * 菠萝派基础参数
 *
 * <AUTHOR>
 * @date 2024/8/27 下午4:36
 */
public enum PolyBasalParamEnum {
    /**
     * 方法名称
     */
    METHOD("method"),
    /**
     * 应用编号
     */
    APPKEY("appkey"),
    /**
     * 访问令牌
     */
    TOKEN("token"),
    /**
     * 平台编号
     */
    PLAT_ID("platid"),
    /**
     * 版本号
     */
    VERSION("version"),
    /**
     * 业务参数(json数据格式)
     */
    BIZ_CONTENT("bizcontent"),
    /**
     * 返回格式(xml或者json)
     */
    CONTENT_TYPE("contenttype"),
    /**
     * 时间戳
     */
    TIMES_TAMP("timestamp"),
    /**
     * 商户请求参数的签名串
     */
    SIGN("sign"),
    /**
     * 请求菠萝派来源服务
     */
    SOURCE_FROM_TYPE("sourcefromtype"),
    /**
     * 会员名
     */
    OUT_USERNAME("outusername"),
    /**
     * 店铺Id
     */
    SHOP_ID("shopid"),
    /**
     * 模拟类型
     */
    SIMULATE_RESPONSE_TYPE("simulateresponsetype"),
    ;

    /**
     * 请求参数名
     */
    private final String paramName;

    PolyBasalParamEnum(String paramName) {
        this.paramName = paramName;
    }

    /**
     * 获取请求参数名
     *
     * @return 请求参数名
     */
    public String getParamName() {
        return paramName;
    }

    @Override
    public String toString() {
        return paramName;
    }
}
