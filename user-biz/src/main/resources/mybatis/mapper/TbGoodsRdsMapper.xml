<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.TbGoodsRdsMapper" >
    <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush.TbGoodsRdsDo" >
        <id column="num_iid" property="numiid"/>
        <result column="nick" property="nick" jdbcType="VARCHAR"/>
        <result column="created" property="orderCreateTime" jdbcType="DATE"/>
        <result column="modified" property="orderModifiedTime" jdbcType="DATE"/>
        <result column="jdp_created" property="createTime" jdbcType="DATE"/>
        <result column="jdp_modified" property="modifiedTime" jdbcType="DATE"/>
        <result column="approve_status" property="approveStatus" jdbcType="VARCHAR"/>
        <result column="jdp_delete" property="jdpDelete" jdbcType="TINYINT"/>
        <result column="jdp_response" property="jdpResponse" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量获取淘宝商品菠萝派数据 -->
    <select id="selectByNumIids" resultType="java.lang.String">
        select jdp_response
        from jdp_tb_item where num_iid in
        <foreach collection="platGoodsIds" item="Id" open="(" close=")" separator=",">
            #{Id}
        </foreach>
    </select>


</mapper>