<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.center.PlatBizFeatureMapper" >

    <!-- 表字段与模型属性映射 -->
    <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.center.DevPlatBizFeatureDO">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="plat" jdbcType="INTEGER" property="plat"/>
        <result column="biz_type" jdbcType="INTEGER" property="bizType"/>
        <result column="config_mode" jdbcType="INTEGER" property="configMode"/>
        <result column="config_restraint" jdbcType="VARCHAR" property="configRestraint"/>
        <result column="config_content" jdbcType="VARCHAR" property="configContent"/>
        <result column="enable" jdbcType="TINYINT" property="enable"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="modified_time" jdbcType="TIMESTAMP" property="modifiedTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>

    <!-- 查询数据（根据平台和业务类型） -->
    <select id="queryByPlatAndBiz" resultMap="BaseResultMap">
        SELECT id, plat, biz_type, config_mode, config_restraint, config_content, enable, remark, modified_time, create_time
        FROM dev_plat_bizFeature
        WHERE plat = #{plat} and biz_type = #{bizType} and enable = 1
    </select>
</mapper>