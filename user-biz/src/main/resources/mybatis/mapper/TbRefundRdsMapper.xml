<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.TbRefundRdsMapper" >
    <resultMap id="TaobaoJsonValueObjectResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush.TbRefundRdsDo">
        <id property="refundId" column="refund_id"/>
        <result property="sellerNick" column="seller_nick"/>
        <result property="buyerNick" column="buyer_nick"/>
        <result property="status" column="status"/>
        <result property="created" column="created"/>
        <result property="tid" column="tid"/>
        <result property="oid" column="oid"/>
        <result property="modified" column="modified"/>
        <result property="jdpHashcode" column="jdp_hashcode"/>
        <result property="jdpResponse" column="jdp_response"/>
        <result property="jdpCreated" column="jdp_created"/>
        <result property="jdpModified" column="jdp_modified"/>
    </resultMap>

    <!--查询淘宝退货退款单列表-->
    <select id="getRDSRefundOrderDataByModified" resultMap="TaobaoJsonValueObjectResultMap">
        <![CDATA[
        SELECT refund_id, seller_nick, buyer_nick, status, created, tid, oid, modified, jdp_hashcode, jdp_response, jdp_created, jdp_modified
        FROM jdp_tb_refund jtr
        WHERE jdp_modified BETWEEN #{dtStartTime} AND #{dtEndTime} AND jtr.seller_nick = #{sellNick}
        ORDER BY jtr.jdp_modified
        LIMIT #{start}, #{pageSize}
        ]]>
    </select>

    <!--查询淘宝退货退款单列表-->
    <select id="getRefundsCountByModified" resultType="int">
        <![CDATA[
        SELECT  COUNT(*)
        FROM jdp_tb_refund jtr
        WHERE jdp_modified BETWEEN #{dtStartTime} AND #{dtEndTime} AND jtr.seller_nick = #{sellNick}
        ]]>
    </select>
</mapper>