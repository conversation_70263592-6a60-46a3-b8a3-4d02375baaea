<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiReturnLogMapper">

    <!-- 批量新增 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO g_api_return_log (billID, Operator, LogDetail, LogTime)
        VALUES
        <foreach collection="logs" item="item" separator=",">
            (#{item.billId}, #{item.operator}, #{item.logDetail}, now())
        </foreach>
    </insert>

</mapper>