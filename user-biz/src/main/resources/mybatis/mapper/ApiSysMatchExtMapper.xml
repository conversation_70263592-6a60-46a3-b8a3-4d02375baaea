<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchExtMapper" >
    <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchExtDO" >
        <id column="Id" property="id" jdbcType="INTEGER" />
        <result column="rel_item_id" property="relItemId" jdbcType="INTEGER" />
        <result column="platStoreCodes" property="platStoreCodes" jdbcType="VARCHAR" />
        <result column="platStoreNames" property="platStoreNames" jdbcType="VARCHAR" />
        <result column="nextResetSyncTime" property="nextResetSyncTime" jdbcType="TIMESTAMP" />
        <result column="increFlag" property="increFlag" jdbcType="INTEGER" />
        <result column="restrictedMode" property="restrictedMode" jdbcType="INTEGER" />
        <result column="platGoodsType" property="platGoodsType" jdbcType="INTEGER" />
        <result column="jsonParams" property="jsonParams" jdbcType="VARCHAR" />
    </resultMap>

    <!-- 批量获取商品匹配扩展 -->
    <select id="selectByIds"
            parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select ID,rel_item_id,PlatStoreCodes,PlatStoreNames,JsonParams,nextResetSyncTime,increFlag,PlatGoodsType,restrictedMode
        from g_api_sysmatchext where Id in
        <foreach collection="ids" item="Id" open="(" close=")" separator=",">
            #{Id}
        </foreach>
    </select>

    <!-- 批量获取商品匹配扩展 -->
    <update id="batchUpdateJsonParams"
            parameterType="java.lang.Integer">
        UPDATE g_api_sysMatchExt M JOIN
        (
        <foreach collection="jsonParamsMap.entrySet()" index="matchId" item="jsonParams" separator=" UNION ">
            SELECT
            #{matchId} AS id,
            #{jsonParams} AS jsonParams
        </foreach>
        ) N USING(Id)
        SET
        M.jsonParams = N.jsonParams
        WHERE M.Id = N.Id
    </update>

    <!-- 批量新增或更新api商品匹配扩展 -->
    <insert id="addOrUpdateGoodsMatchExtra">
        <foreach collection="goodsMatchExtraList" item="item" index="index" open=""
                 close="" separator=";">
            insert into g_api_sysMatchExt
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">`id`,</if>
                <if test="item.relItemId != null">`relItemId`,</if>
                <if test="item.platStoreCodes != null">`platStoreCodes`,</if>
                <if test="item.platStoreNames != null">`platStoreNames`,</if>
                <if test="item.nextResetSyncTime != null">`nextResetSyncTime`,</if>
                <if test="item.increFlag != null">`increFlag`,</if>
                <if test="item.restrictedMode != null">`restrictedMode`,</if>
                <if test="item.platGoodsType != null">`platGoodsType`,</if>
                <if test="item.jsonParams != null">`jsonParams`</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id,jdbcType=INTEGER},</if>
                <if test="item.relItemId != null">#{item.relItemId,jdbcType=VARCHAR},</if>
                <if test="item.platStoreCodes != null">#{item.platStoreCodes,jdbcType=VARCHAR},</if>
                <if test="item.platStoreNames != null">#{item.platStoreNames,jdbcType=VARCHAR},</if>
                <if test="item.nextResetSyncTime != null">#{item.nextResetSyncTime,jdbcType=DATE},</if>
                <if test="item.increFlag != null">#{item.increFlag,jdbcType=INTEGER},</if>
                <if test="item.restrictedMode != null">#{item.restrictedMode,jdbcType=INTEGER},</if>
                <if test="item.platGoodsType != null">#{item.platGoodsType,jdbcType=INTEGER},</if>
                <if test="item.jsonParams != null">#{item.jsonParams,jdbcType=VARCHAR}</if>
            </trim>
            ON DUPLICATE KEY UPDATE
            <trim suffixOverrides=",">
                <if test="item.relItemId != null">relItemId=#{item.relItemId,jdbcType=INTEGER},</if>
                <if test="item.platStoreCodes != null">platStoreCodes=#{item.platStoreCodes,jdbcType=VARCHAR},</if>
                <if test="item.platStoreNames != null">platStoreNames=#{item.platStoreNames,jdbcType=VARCHAR},</if>
                <if test="item.nextResetSyncTime != null">nextResetSyncTime=#{item.nextResetSyncTime,jdbcType=DATE},</if>
                <if test="item.increFlag != null">increFlag=#{item.increFlag,jdbcType=INTEGER},</if>
                <if test="item.restrictedMode != null">restrictedMode=#{item.restrictedMode,jdbcType=INTEGER},</if>
                <if test="item.platGoodsType != null">platGoodsType=#{item.platGoodsType,jdbcType=INTEGER},</if>
                <if test="item.jsonParams != null">jsonParams=#{item.jsonParams,jdbcType=VARCHAR}</if>
            </trim>
        </foreach>
    </insert>

    <!-- 根据主键删除商品匹配 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from g_api_sysmatchext where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
</mapper>