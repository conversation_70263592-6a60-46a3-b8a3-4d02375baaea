<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.TbTradeRdsMapper" >
    <resultMap id="TaobaoJsonValueObjectResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush.TbTradeRdsDo">
        <id property="tId" column="tid"/>
        <result property="status" column="status"/>
        <result property="type" column="type"/>
        <result property="sellerNick" column="seller_nick"/>
        <result property="buyerNick" column="buyer_nick"/>
        <result property="created" column="created"/>
        <result property="modified" column="modified"/>
        <result property="jdpHashcode" column="jdp_hashcode"/>
        <result property="jdpResponse" column="jdp_response"/>
        <result property="jdpCreated" column="jdp_created"/>
        <result property="jdpModified" column="jdp_modified"/>
    </resultMap>


    <!--查询淘宝退货退款单列表-->
    <select id="getTradeListByTIds" resultMap="TaobaoJsonValueObjectResultMap">
        SELECT tid, type, seller_nick, buyer_nick, status, created, modified, jdp_hashcode, jdp_response, jdp_created, jdp_modified
        FROM jdp_tb_trade jtr
        WHERE tid in
        <foreach collection="tIds" item="tId" open="(" close=")" separator=",">
        #{tId}
        </foreach>
    </select>
</mapper>