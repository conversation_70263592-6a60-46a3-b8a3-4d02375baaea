package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule;

import com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch.data.CommonWarehouseMatchDTO;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockGoodsExtRuleTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.multi.adapter.core.IMultiWarehouseAdapter;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockNumRuleDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ShopConfigStockSyncRuleEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.SyncStockShopConfigUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * StockCalculationRule 单元测试
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("库存计算规则测试")
class StockCalculationRuleTest {

    @Mock
    private IMultiWarehouseAdapter multiWhsAdapter;

    @Mock
    private SyncStockShopConfig syncStockConfig;

    @Mock
    private SyncStockNumRuleDto syncNumRule;

    private StockSyncContext context;
    private GoodsMatchEnhance goodsMatchEnhance;
    private ApiSysMatchDO apiSysMatch;

    @BeforeEach
    void setUp() {
        // 初始化上下文
        context = new StockSyncContext();
        context.setSyncStockConfig(syncStockConfig);
        context.setMultiWhsAdapter(multiWhsAdapter);

        // 初始化匹配数据
        apiSysMatch = new ApiSysMatchDO();
        goodsMatchEnhance = new GoodsMatchEnhance();
        goodsMatchEnhance.setSysMatch(apiSysMatch);

        // 默认配置
        when(syncStockConfig.getSyncNumRule()).thenReturn(syncNumRule);
        when(syncStockConfig.getIsSyncMoreZeroAutoShelves()).thenReturn(true);
    }

    @Test
    @DisplayName("测试基本信息设置")
    void testBasicInfoSetting() {
        // Given
        apiSysMatch.setGoodsID(1001);
        apiSysMatch.setSpecID(2001);
        apiSysMatch.setGoodsType(1);
        apiSysMatch.setShopId(3001);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_ONE);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then
            assertThat(rule.getErpGoodsId()).isEqualTo(1001);
            assertThat(rule.getErpSpecId()).isEqualTo(2001);
            assertThat(rule.getGoodsType()).isEqualTo(1);
            assertThat(rule.getShopId()).isEqualTo(3001);
            assertThat(rule.getShopConfigStockSyncRule()).isEqualTo(ShopConfigStockSyncRuleEnum.RULE_ONE);
            assertThat(rule.isbSyncPctStock()).isTrue();
        }
    }

    @Test
    @DisplayName("测试匹配级百分比优先级")
    void testMatchLevelPercentagePriority() {
        // Given
        apiSysMatch.setBSingletb(true);
        apiSysMatch.setSingleNumPer(80);
        when(syncNumRule.getSyncNumPercentage()).thenReturn(60);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then
            assertThat(rule.getPercent()).isEqualTo(BigDecimal.valueOf(80));
        }
    }

    @Test
    @DisplayName("测试店铺级百分比设置")
    void testShopLevelPercentageSetting() {
        // Given
        apiSysMatch.setBSingletb(false);
        when(syncNumRule.getSyncNumPercentage()).thenReturn(70);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then
            assertThat(rule.getPercent()).isEqualTo(BigDecimal.valueOf(70));
        }
    }

    @Test
    @DisplayName("测试固定数量规则设置")
    void testFixedQuantityRuleSetting() {
        // Given
        apiSysMatch.setBruleStop(true);
        apiSysMatch.setBFixNum(1); // 固定数量模式
        apiSysMatch.setFixNum(100);
        apiSysMatch.setVirNumBase(10);
        apiSysMatch.setVirNumTop(200);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then
            assertThat(rule.getExtRuleType()).isEqualTo(StockGoodsExtRuleTypeEnum.FIX);
            assertThat(rule.getFixedQuantity()).isEqualTo(BigDecimal.valueOf(100));
            assertThat(rule.getMinStockQuantity()).isEqualTo(BigDecimal.valueOf(10));
            assertThat(rule.getMaxStockQuantity()).isEqualTo(BigDecimal.valueOf(200));
        }
    }

    @Test
    @DisplayName("测试条件固定数量规则设置")
    void testConditionFixedQuantityRuleSetting() {
        // Given
        apiSysMatch.setBruleStop(true);
        apiSysMatch.setBFixNum(2); // 条件固定数量模式
        apiSysMatch.setFixNum(50);
        apiSysMatch.setVirNumBase(5);
        apiSysMatch.setVirNumTop(150);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then
            assertThat(rule.getExtRuleType()).isEqualTo(StockGoodsExtRuleTypeEnum.CONDITION_FIX);
            assertThat(rule.getFixedQuantity()).isEqualTo(BigDecimal.valueOf(50));
            assertThat(rule.getMinStockQuantity()).isEqualTo(BigDecimal.valueOf(5));
            assertThat(rule.getMaxStockQuantity()).isEqualTo(BigDecimal.valueOf(150));
        }
    }

    @Test
    @DisplayName("测试条件增加数量规则设置")
    void testConditionIncrementalQuantityRuleSetting() {
        // Given
        apiSysMatch.setBruleStop(true);
        apiSysMatch.setBVirNum(true);
        apiSysMatch.setVirNumInc(20);
        apiSysMatch.setVirNumBase(10);
        apiSysMatch.setVirNumTop(100);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then
            assertThat(rule.getExtRuleType()).isEqualTo(StockGoodsExtRuleTypeEnum.CONDITION_FIX_INCREASE);
            assertThat(rule.getIncrementalQuantity()).isEqualTo(BigDecimal.valueOf(20));
            assertThat(rule.getMinStockQuantity()).isEqualTo(BigDecimal.valueOf(10));
            assertThat(rule.getMaxStockQuantity()).isEqualTo(BigDecimal.valueOf(100));
        }
    }

    @Test
    @DisplayName("测试匹配级仓库优先级")
    void testMatchLevelWarehousePriority() {
        // Given
        apiSysMatch.setRuleWarehouse("1001,1002,1003");
        goodsMatchEnhance.setMultiSign("MULTI_SIGN_TEST");

        Set<CommonWarehouseMatchDTO> whsMatches = new HashSet<>();
        CommonWarehouseMatchDTO whsMatch = new CommonWarehouseMatchDTO();
        whsMatch.setErpWarehouseIds(Arrays.asList(1001, 1004, 1005));
        whsMatches.add(whsMatch);

        when(multiWhsAdapter.getWhsMatchesByPlat(any(), eq("MULTI_SIGN_TEST"))).thenReturn(whsMatches);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then - 应该取匹配级仓库和平台仓库的交集
            assertThat(rule.getWarehouseCodes()).containsExactly(1001);
        }
    }

    @Test
    @DisplayName("测试平台仓库匹配")
    void testPlatformWarehouseMatching() {
        // Given
        goodsMatchEnhance.setMultiSign("MULTI_SIGN_TEST");

        Set<CommonWarehouseMatchDTO> whsMatches = new HashSet<>();
        CommonWarehouseMatchDTO whsMatch = new CommonWarehouseMatchDTO();
        whsMatch.setErpWarehouseIds(Arrays.asList(2001, 2002, 2003));
        whsMatches.add(whsMatch);

        when(multiWhsAdapter.getWhsMatchesByPlat(any(), eq("MULTI_SIGN_TEST"))).thenReturn(whsMatches);

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then
            assertThat(rule.getWarehouseCodes()).containsExactlyInAnyOrder(2001, 2002, 2003);
        }
    }

    @Test
    @DisplayName("测试店铺级仓库设置")
    void testShopLevelWarehouseSetting() {
        // Given
        when(syncNumRule.getWarehouseIds()).thenReturn(Arrays.asList("3001", "3002", "3003"));

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then
            assertThat(rule.getWarehouseCodes()).containsExactlyInAnyOrder(3001, 3002, 3003);
        }
    }

    @Test
    @DisplayName("测试默认店铺配置规则")
    void testDefaultShopConfigRule() {
        // Given
        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(null);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then
            assertThat(rule.getShopConfigStockSyncRule()).isEqualTo(ShopConfigStockSyncRuleEnum.RULE_THREE);
        }
    }

    @Test
    @DisplayName("测试空匹配数据处理")
    void testNullMatchDataHandling() {
        // Given
        goodsMatchEnhance.setSysMatch(null);
        when(syncNumRule.getSyncNumPercentage()).thenReturn(50);
        when(syncNumRule.getWarehouseIds()).thenReturn(Arrays.asList("4001", "4002"));

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_TWO);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then
            assertThat(rule.getErpGoodsId()).isNull();
            assertThat(rule.getErpSpecId()).isNull();
            assertThat(rule.getGoodsType()).isNull();
            assertThat(rule.getShopId()).isNull();
            assertThat(rule.getPercent()).isEqualTo(BigDecimal.valueOf(50));
            assertThat(rule.getWarehouseCodes()).containsExactlyInAnyOrder(4001, 4002);
            assertThat(rule.getShopConfigStockSyncRule()).isEqualTo(ShopConfigStockSyncRuleEnum.RULE_TWO);
        }
    }

    @Test
    @DisplayName("测试空仓库列表处理")
    void testEmptyWarehouseListHandling() {
        // Given - 所有仓库来源都为空

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then
            assertThat(rule.getWarehouseCodes()).isEmpty();
        }
    }

    @Test
    @DisplayName("测试无效仓库ID过滤")
    void testInvalidWarehouseIdFiltering() {
        // Given
        apiSysMatch.setRuleWarehouse("1001,0,invalid,1002,");
        when(syncNumRule.getWarehouseIds()).thenReturn(Arrays.asList("2001", "0", "", "2002"));

        try (MockedStatic<SyncStockShopConfigUtils> mockedUtils = mockStatic(SyncStockShopConfigUtils.class)) {
            mockedUtils.when(() -> SyncStockShopConfigUtils.getShopConfigStockSyncRule(any()))
                    .thenReturn(ShopConfigStockSyncRuleEnum.RULE_THREE);

            // When
            StockCalculationRule rule = StockCalculationRule.create(goodsMatchEnhance, context);

            // Then - 应该过滤掉无效的仓库ID
            assertThat(rule.getWarehouseCodes()).containsExactlyInAnyOrder(1001, 1002);
        }
    }
}
