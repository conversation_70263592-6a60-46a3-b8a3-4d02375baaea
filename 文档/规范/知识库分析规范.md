# 知识库分析规范

## 1. 概述

本规范用于指导AI按照一定的流程分析指定业务代码，生成结构化的知识库文档。知识库的主要目标是使 AI 能够：

1. **快速理解需求**：准确把握业务需求和业务规则
2. **快速定位业务代码**：准确找到需要修改的代码位置
3. **提供正确的业务修改建议**：确保修改符合业务逻辑和系统架构

知识库文档应当全面、准确地描述业务代码的结构、功能和实现细节，便于 AI 快速理解和正确修改代码。

## 2. 分析前提

在开始分析之前，必须满足以下前提条件：

1. **必须提供指定文件夹路径**：明确指定需要分析的代码所在的文件夹路径
2. **必须提供业务名称**：明确指定需要分析的业务名称或模块名称
3. **如果未提供上述信息**：不进行分析，而是提示用户提供必要信息

示例提示：
```
请提供需要分析的代码文件夹路径和业务名称，例如：
- 文件夹路径：D:\project\src\main\java\com\example\business
- 业务名称：订单管理模块
```

## 3. 分析流程

### 3.1 代码结构分析

1. **扫描文件夹结构**：了解代码的组织方式和主要组件，建立代码地图
2. **识别核心类和接口**：找出业务的核心类、接口和枚举，便于快速定位代码
3. **分析依赖关系**：了解组件之间的依赖关系，避免修改引起连锁反应
4. **识别设计模式**：识别代码中使用的设计模式，确保修改符合现有架构

### 3.2 业务功能分析

1. **识别业务流程**：分析业务的主要流程和步骤，建立流程模型
2. **识别业务规则**：提取代码中的业务规则和约束，确保修改符合业务逻辑
3. **识别异常处理**：分析代码中的异常处理机制，确保修改后系统稳定性
4. **识别关键算法**：分析代码中的关键算法和处理逻辑，避免修改引起性能问题

### 3.3 数据结构分析

1. **分析实体类**：了解业务中的主要实体及其属性，明确数据模型
2. **分析数据传输对象**：了解业务中的DTO及其用途，确保数据传输正确
3. **分析数据访问层**：了解业务如何与数据库交互，避免 SQL 注入和性能问题
4. **分析缓存机制**：了解业务中的缓存使用情况，确保缓存一致性

### 3.4 接口分析

1. **分析对外接口**：了解业务提供的API和服务，确保兼容性
2. **分析对内接口**：了解业务内部组件之间的接口，避免破坏内部依赖
3. **分析第三方接口**：了解业务与第三方系统的交互，确保集成正确
4. **分析消息接口**：了解业务中的消息发送和接收机制，确保消息处理可靠

## 4. 文档生成规则

### 4.0 文档生成路径

生成的知识库文档应存放在以下指定路径：

```
D:\esapi-java\delivery\DF.WDGJ.API\文档\知识库
```

文件命名规则：
- 单个文档：`[业务名称].md`
- 分批文档：`[业务名称]-[部分名称].md`

例如：
- `订单管理模块.md`
- `订单管理模块-核心流程.md`

### 4.1 文档结构

生成的知识库文档应包含以下部分，以支持 AI 快速理解需求、定位代码和提供正确的修改建议：

1. **概述**：简要介绍业务的功能和用途，帮助 AI 快速理解业务背景
2. **代码地图**：提供业务代码的文件和目录结构，帮助 AI 快速定位代码
3. **架构设计**：描述业务的整体架构和主要组件，确保修改符合架构
4. **核心流程**：详细说明业务的核心流程和步骤，确保修改符合业务逻辑
5. **关键实现**：详细说明业务的关键实现细节，包括算法和设计模式
6. **数据结构**：描述业务中的主要数据结构，确保数据处理正确
7. **接口说明**：描述业务提供的接口和服务，确保集成正确
8. **异常处理**：描述业务的异常处理机制，确保系统稳定性
9. **修改指南**：提供修改业务代码的具体指导，包括注意事项和最佳实践

### 4.2 分批生成规则

如果发现一次性无法生成完整文档，应按照以下规则分批生成：

1. **按业务功能划分**：将业务按功能模块划分，分别生成文档
2. **按文档章节划分**：将文档按章节划分，分别生成
3. **保持连贯性**：确保分批生成的文档之间保持连贯性
4. **提供导航信息**：在每个分批文档中提供导航信息，指引读者阅读顺序

示例分批方案：
```
- 第1部分：概述与架构设计
- 第2部分：核心流程与关键实现
- 第3部分：数据结构与接口说明
- 第4部分：异常处理与最佳实践
```

### 4.3 代码示例规则

在文档中包含代码示例时，应遵循以下规则：

1. **保持简洁**：代码示例应简洁明了，突出关键逻辑
2. **添加注释**：为关键代码添加注释，解释其功能和用途
3. **使用语法高亮**：使用Markdown的代码块语法，指定正确的语言
4. **避免过长**：避免过长的代码示例，必要时进行截断

示例代码块：
```java
/**
 * 处理订单支付
 * @param orderId 订单ID
 * @param amount 支付金额
 * @return 支付结果
 */
public PaymentResult processPayment(String orderId, BigDecimal amount) {
    // 验证订单
    Order order = orderRepository.findById(orderId);
    if (order == null) {
        throw new OrderNotFoundException("订单不存在：" + orderId);
    }

    // 验证金额
    if (amount.compareTo(order.getTotalAmount()) != 0) {
        throw new InvalidAmountException("支付金额不匹配");
    }

    // 调用支付服务
    PaymentResult result = paymentService.pay(orderId, amount);

    // 更新订单状态
    order.setStatus(OrderStatus.PAID);
    orderRepository.save(order);

    return result;
}
```

### 4.4 图表使用规则

在文档中使用图表时，应遵循以下规则：

1. **使用合适的图表类型**：根据内容选择合适的图表类型（流程图、类图、时序图等）
2. **保持简洁清晰**：图表应简洁清晰，避免过于复杂
3. **提供文字说明**：为图表提供文字说明，解释其含义
4. **使用Markdown兼容语法**：使用Markdown兼容的图表语法，如Mermaid

示例流程图：
```mermaid
graph TD
    A[开始] --> B{订单是否存在?}
    B -->|是| C[验证订单状态]
    B -->|否| D[返回错误]
    C --> E{订单状态是否正确?}
    E -->|是| F[处理支付]
    E -->|否| G[返回错误]
    F --> H[更新订单状态]
    H --> I[结束]
    D --> I
    G --> I
```

## 5. 分析示例

以下是一个简单的分析示例，展示如何按照本规范分析业务代码：

### 示例业务：订单管理模块

#### 5.1 概述

订单管理模块负责处理订单的创建、支付、发货和退款等功能，是电商系统的核心业务模块之一。

#### 5.2 架构设计

订单管理模块采用分层架构设计，主要包括以下组件：

1. **Controller层**：处理HTTP请求，提供RESTful API
2. **Service层**：实现业务逻辑，处理订单的各种操作
3. **Repository层**：负责数据访问，与数据库交互
4. **Model层**：定义业务实体和数据传输对象
5. **Util层**：提供通用工具类和辅助功能

#### 5.3 核心流程

订单创建流程：
1. 验证用户信息和商品信息
2. 计算订单金额和优惠
3. 创建订单记录
4. 锁定商品库存
5. 返回订单信息

#### 5.4 关键实现

订单状态管理：
```java
public enum OrderStatus {
    CREATED(1, "已创建"),
    PAID(2, "已支付"),
    SHIPPED(3, "已发货"),
    COMPLETED(4, "已完成"),
    CANCELLED(5, "已取消"),
    REFUNDED(6, "已退款");

    private int code;
    private String desc;

    // 构造方法和getter方法...
}

public class OrderStatusManager {
    /**
     * 更新订单状态
     * @param orderId 订单ID
     * @param status 新状态
     * @return 更新结果
     */
    public boolean updateStatus(String orderId, OrderStatus status) {
        // 实现代码...
    }

    /**
     * 检查状态转换是否合法
     * @param currentStatus 当前状态
     * @param newStatus 新状态
     * @return 是否合法
     */
    private boolean isValidStatusTransition(OrderStatus currentStatus, OrderStatus newStatus) {
        // 实现代码...
    }
}
```

## 6. 注意事项

1. **代码定位优先**：重点关注代码的组织结构，确保 AI 能快速定位到相关代码
2. **业务逻辑清晰**：清晰描述业务逻辑和规则，确保 AI 能准确理解需求
3. **关键点突出**：突出强调代码中的关键点和注意事项，避免修改错误
4. **提供修改指导**：包含具体的修改指导和注意事项，确保修改符合要求
5. **依赖关系清晰**：清晰说明代码的依赖关系，避免修改引起连锁反应
6. **避免冗余**：避免不必要的重复和冗余内容，保持文档简洁清晰
7. **注意安全**：避免在文档中包含敏感信息，如密码、密钥等
8. **提供代码示例**：提供具体的代码示例，帮助 AI 理解如何正确修改代码

## 7. 分析请求模板

为了规范分析请求，建议使用以下模板：

```
请分析以下业务代码并生成知识库文档，以便我能快速理解需求、定位代码并提供正确的修改建议：

- 文件夹路径：[指定代码文件夹路径]
- 业务名称：[指定业务名称]
- 当前需求：[可选，指定当前需要实现的需求]
- 分析重点：[可选，指定需要重点分析的内容]
- 输出路径：D:\esapi-java\delivery\DF.WDGJ.API\文档\知识库
- 输出格式：[可选，指定输出格式，如Markdown、HTML等]
- 分批方案：[可选，指定分批生成的方案]
```

示例请求：
```
请分析以下业务代码并生成知识库文档，以便我能快速理解需求、定位代码并提供正确的修改建议：

- 文件夹路径：D:\project\src\main\java\com\example\order
- 业务名称：订单管理模块
- 当前需求：需要添加订单部分退款功能，支持对订单中的部分商品进行退款
- 分析重点：订单状态流转、支付和退款处理逻辑
- 输出路径：D:\esapi-java\delivery\DF.WDGJ.API\文档\知识库
- 输出格式：Markdown
- 分批方案：按业务功能划分
```

## 8. 使用知识库进行代码修改

生成知识库后，可以使用以下模板请求 AI 进行代码修改：

```
基于之前生成的[业务名称]知识库，请实现以下功能：

- 功能描述：[详细描述需要实现的功能]
- 修改范围：[指定需要修改的文件或模块]
- 具体要求：[列出具体的功能要求和限制条件]

请提供以下内容：
1. 需要修改的文件列表
2. 每个文件的具体修改内容
3. 修改的说明和注意事项
```

示例请求：
```
基于之前生成的订单管理模块知识库，请实现以下功能：

- 功能描述：添加订单部分退款功能，支持对订单中的部分商品进行退款
- 修改范围：订单服务和退款处理相关模块
- 具体要求：
  1. 支持选择订单中的部分商品进行退款
  2. 退款金额不能超过商品实际支付金额
  3. 退款后需更新订单状态和商品退款状态
  4. 需要记录退款历史和操作日志

请提供以下内容：
1. 需要修改的文件列表
2. 每个文件的具体修改内容
3. 修改的说明和注意事项
```
