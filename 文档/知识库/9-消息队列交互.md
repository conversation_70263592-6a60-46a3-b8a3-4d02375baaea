# 消息队列交互

## 概述

网店管家API项目主要通过消息队列（MQ）进行外部交互，而不是传统的REST API。项目采用了多种消息队列技术，包括JMQ（吉客云消息队列）、Kafka和数据库队列，实现了高可用、可靠的异步通信机制。

## 消息队列架构

### 整体架构

项目实现了一个多队列架构，支持多种消息队列技术的混合使用：

1. **JMQ**：基于RabbitMQ的吉客云消息队列，作为主要的消息队列
2. **Kafka**：用于高吞吐量场景
3. **数据库队列**：用于持久化和备份

多队列架构的主要优势：
- 提高系统可用性，一种队列不可用时可以切换到其他队列
- 根据不同业务场景选择合适的队列技术
- 支持消息的优先级处理和重试机制

### 队列类型

项目定义了多种队列类型，通过`SubQueueEnum`枚举进行区分：

```java
public enum SubQueueEnum {
    /**
     * JMQ
     */
    JMQ("jmq", SubQueueJmqFactory.class, QueueEngineEnum.JMQ),
    /**
     * JMQ的自定义备份队列
     */
    JMQ_BACK("jmq_back", SubQueueJmqBackFactory.class, QueueEngineEnum.JMQ),
    /**
     * 原生kafka
     */
    KAFKA("kafka", SubQueueCommonKafkaFactory.class, QueueEngineEnum.KAFKA),
    /**
     * 吉客云kafka
     */
    JKAFKA("jkafka", null, QueueEngineEnum.JKAFKA),
    /**
     * 用户数据库队列
     */
    DB_USER ("db_user", null, QueueEngineEnum.DB_USER),
    /**
     * 全局数据库队列
     */
    DB_CENTER ("db_center", null, QueueEngineEnum.DB_CENTER),
}
```

## 主要消息队列组件

### 1. JMQ组件

JMQ是项目中使用最广泛的消息队列组件，基于RabbitMQ实现。

#### 核心注解

```java
@ApiJMQ(
    code = "wdgj-api.aftersale.save",
    sitesToReceive = SiteTypeCodeConst.WDGJ_API_BUSINESS,
    sitesToSend = SiteTypeCodeConst.WDGJ_API_BUSINESS
)
```

- `code`：队列标识
- `sitesToReceive`：接收消息的站点
- `sitesToSend`：发送消息的站点

#### 消息发送

```java
public void sendMessage(String message) {
    // 解析队列code
    String code = this.resolveCode();
    // 开启信号量限制
    this.sendBySemaphoreLimit(code, message, 500);
}
```

#### 消息消费

```java
@Override
public JMQResult consume(JMQMessage<String> message) {
    try {
        // 消息处理逻辑
        return JMQResult.ACK;
    } catch (Exception e) {
        // 异常处理
        return JMQResult.RETRY;
    }
}
```

### 2. 多队列组件

多队列组件是项目的高级消息队列抽象，支持多种队列技术的混合使用。

#### 核心注解

```java
@ApiMultiMQ(
    code = "wdgj-api.multi.demo",
    subQueues = {SubQueueEnum.JMQ, SubQueueEnum.KAFKA},
    sitesToReceive = SiteTypeCodeConst.WDGJ_API_BUSINESS,
    sitesToSend = SiteTypeCodeConst.WDGJ_API_BUSINESS
)
```

- `code`：队列标识
- `subQueues`：子队列列表，按优先级排序
- `sitesToReceive`：接收消息的站点
- `sitesToSend`：发送消息的站点

#### 消息发送

```java
@Override
public void sendMulti(DemoData message) {
    QueueHeader header = new QueueHeader();
    // 设置数据类型
    header.setDataType("demo");
    // 设置平台
    header.setDataPlat(PolyPlatEnum.BUSINESS_Taobao.getValue());
    super.sendMulti(message, header);
}
```

#### 消息消费

```java
@Override
public QueueResult receiveMulti(DemoData message, QueueHeader header) {
    // 消息处理逻辑
    return QueueResult.ACK;
}
```

## 主要业务消息队列

### 1. 售后单保存消息队列

售后单保存消息队列用于异步处理售后单的保存操作。

#### 队列定义

```java
@ApiJMQ(
    code = "wdgj-api.aftersale.save",
    sitesToReceive = SiteTypeCodeConst.WDGJ_API_BUSINESS,
    sitesToSend = SiteTypeCodeConst.WDGJ_API_BUSINESS
)
@ConditionalOnEnvType(exceptEnv = {SystemEnvTypeEnum.DEV})
public class AfterSaleSaverMqHandler extends ApiJMQSender implements JMQSimpleReceive<String> {
    // 实现代码
}
```

#### 消息结构

```java
public class AfterSaleSaveMsgDto {
    // 会员名
    private String outAccount;
    // 外部店铺id
    private Integer outShopId;
    // 订单触发方式
    private Integer orderTriggerType;
    // 操作人
    private String operatorName;
    // 菠萝派请求id
    private String polyApiRequestId;
    // 退货退款单列表
    private List<BusinessGetRefundOrderResponseOrderItem> refunds;
    // 换货单列表
    private List<BusinessGetExchangeOrderResponseOrderItem> exchanges;
}
```

#### 消息处理流程

1. 接收售后单保存消息
2. 解析消息内容，提取售后单信息
3. 构建售后单保存上下文
4. 根据平台类型选择合适的售后单处理器
5. 执行售后单保存逻辑
6. 返回处理结果

### 2. 业务手动操作转发消息队列

业务手动操作转发消息队列用于处理用户手动触发的业务操作。

#### 队列定义

```java
@ApiJMQ(
    code = "wdgj-api.handbiz.transmit",
    sitesToReceive = SiteTypeCodeConst.WDGJ_API_BUSINESS,
    sitesToSend = SiteTypeCodeConst.WDGJ_API_BUSINESS
)
@ConditionalOnEnvType(exceptEnv = {SystemEnvTypeEnum.DEV})
public class HandBizTransmitMqHandler extends ApiJMQSender implements JMQSimpleReceive<String> {
    // 实现代码
}
```

#### 消息结构

```java
public class HandBizTransmitMqDto {
    // 会员名
    private String outAccount;
    // 外部店铺id
    private Integer outShopId;
    // 转发类型
    private HandBizTransmitTypeEnum transmitType;
    // 业务请求数据
    private String handBizRequest;
}
```

#### 消息处理流程

1. 接收业务手动操作转发消息
2. 解析消息内容，提取业务操作信息
3. 根据转发类型选择合适的业务处理器
4. 执行业务操作
5. 返回处理结果

### 3. 库存同步消息队列

库存同步消息队列用于处理商品库存的同步操作。

#### 队列定义

```java
@ApiJMQ(
    code = "wdgj-api.stock.save-result",
    sitesToReceive = SiteTypeCodeConst.WDGJ_API_BUSINESS,
    sitesToSend = SiteTypeCodeConst.WDGJ_API_BUSINESS
)
@ConditionalOnEnvType(exceptEnv = {SystemEnvTypeEnum.DEV})
public class StockSaveAdapterMqHandler extends ApiJMQSender implements JMQSimpleReceive<String> {
    // 实现代码
}
```

#### 消息结构

```java
public class DotNetRabbitMQRetryMsgDto {
    // 当前重试次数
    private int retryCount;
    // 最大重试次数
    private int retryMaxCount;
    // 业务数据实体
    private BizDataEntity entity;
}
```

#### 消息处理流程

1. 接收库存同步消息
2. 解析消息内容，提取库存信息
3. 执行库存同步操作
4. 处理同步结果
5. 如果失败，根据重试策略决定是否重试

## 消息队列配置

### JMQ配置

```java
@ApiJMQ(
    code = "队列标识",
    sitesToReceive = "接收站点",
    sitesToSend = "发送站点",
    queueEnabled = ApiMqEnabled.class,
    consumerFailCallback = ApiJMQConsumerFailCallback.class
)
```

### Kafka配置

```java
@Bean(BEAN_API_COMMON_KAFKAPROPERTIES_TEMPLATE)
@Scope("prototype")
@ConfigurationProperties(prefix = "omsapi.common.kafka", ignoreInvalidFields = true)
public KafkaProperties getApiCommonKafkaConfigProperties() {
    return KafkaUtil.defaultKafkaConfigProperties(null, null);
}
```

## 消息重试机制

项目实现了完善的消息重试机制，确保消息的可靠处理：

1. **JMQ重试**：通过返回`JMQResult.RETRY`触发消息重试
2. **多队列重试**：支持在不同队列间的重试策略
3. **最大重试次数**：达到最大重试次数后，消息会被转移到失败队列
4. **延时重试**：支持按照不同的延时级别进行重试

## 消息限流机制

为了防止消息处理过载，项目实现了消息限流机制：

```java
protected final Semaphore semaphore = new Semaphore(400, true);

private void sendBySemaphoreLimit(String code, String message, long timeoutMillis) {
    boolean acquired = false;
    try {
        acquired = semaphore.tryAcquire(timeoutMillis, TimeUnit.MILLISECONDS);
        if (acquired) {
            this.send(code, message);
        } else {
            log.warn("{}发送消息信号量获取超时，消息内容：{}", LOG_CAP, message);
        }
    } catch (Exception e) {
        log.error("{}发送消息异常，消息内容：{}", LOG_CAP, message, e);
    } finally {
        if (acquired) {
            semaphore.release();
        }
    }
}
```

## 最佳实践

1. **消息序列化**：使用JSON格式序列化消息，确保跨平台兼容性
2. **异常处理**：捕获并记录消息处理过程中的异常，避免消息丢失
3. **幂等性设计**：确保消息可以被重复处理而不产生副作用
4. **监控与告警**：实现消息队列的监控和告警机制，及时发现问题
5. **消息追踪**：记录消息的处理过程，便于问题排查
6. **环境隔离**：通过`@ConditionalOnEnvType`注解实现不同环境的消息队列配置隔离
