# 数据库设计

## 概述

网店管家API项目采用多数据源架构，支持动态切换数据库连接，主要包括以下几种数据库：

1. **中心库**：存储系统配置、用户信息等全局数据
2. **用户库**：存储各用户的业务数据，采用一用户一库的模式
3. **RDS库**：存储系统推送相关数据

## 多数据源架构

### 数据源类型

项目定义了多种数据源类型，通过`SwitcherDBTypeEnum`枚举进行区分：

```java
public enum SwitcherDBTypeEnum {
    /**
     * 网店管家用户库
     */
    WDGJ,

    /**
     * RDS推送库
     */
    RDS_PUSH,

    /**
     * API中心库
     */
    API
}
```

### 数据源切换机制

项目使用Spring的`AbstractRoutingDataSource`实现动态数据源切换，核心类为`BaseMultiDbDataSource`：

```java
public class BaseMultiDbDataSource extends AbstractRoutingDataSource implements InitializingBean, DisposableBean {
    @Override
    protected DataSource determineTargetDataSource() {
        // 取当前上下文
        DataSourceContext currentContext = this.determineCurrentLookupKey();

        // 支持固定的数据源
        DataSource dataSourceSingle = this.connectInfoLocalCache.getSingleDataSource(currentContext);
        if(dataSourceSingle != null){
            return dataSourceSingle;
        }

        // 通过上下文获取数据源
        DbConnectionInfo connectionInfo = this.connectInfoLocalCache.getCache(currentContext);
        return this.dataSources.computeIfAbsent(connectionInfo, (conn) -> createDataSource(conn));
    }
}
```

### 数据源上下文

通过`DataSourceContextHolder`维护当前线程的数据源上下文：

```java
public class DataSourceContextHolder {
    private static final ThreadLocal<DataSourceContext> contextHolder = new ThreadLocal<>();

    public static void set(DataSourceContext context) {
        contextHolder.set(context);
    }

    public static DataSourceContext get() {
        return contextHolder.get();
    }

    public static void remove() {
        contextHolder.remove();
    }
}
```

### 数据库切换工具

项目提供了`DBSwitchUtil`工具类，简化数据源切换操作：

```java
public static void doDBWithUser(String user, Action fun) {
    try {
        SwitchDbContext context = new SwitchDbContext(SwitcherDBTypeEnum.WDGJ, user);
        DataSourceContextHolder.set(context);
        fun.exec();
    } finally {
        //清除上下文资源
        DataSourceContextHolder.remove();
    }
}

public static <R> R doDBWithUser(String user, Function<R> fun) {
    try {
        SwitchDbContext context = new SwitchDbContext(SwitcherDBTypeEnum.WDGJ, user);
        DataSourceContextHolder.set(context);
        return fun.exec();
    } finally {
        //清除上下文资源
        DataSourceContextHolder.remove();
    }
}
```

### 数据库名称生成规则

根据不同的数据源类型，生成对应的数据库名称：

```java
public String getDbName() {
    String dbName = null;
    switch (this.dataSourceSwitcherType) {
        case WDGJ:
            dbName = String.format("wdgjyun_%s", wdgjUser).toLowerCase();
            break;
        case RDS_PUSH:
            dbName = getRdsPushDbName();
            break;
        case API:
            dbName = getEsApiDbName();
            break;
        default:
            throw new AppException(SystemErrorCodes.LOGICERROR, String.format("代理不支持%s库操作", this.dataSourceSwitcherType));
    }
    return dbName;
}
```

## 数据库表设计

### 中心库表

#### 1. 用户信息表 (member_users)

存储系统用户的基本信息。

**主要字段**：
- `userName`：用户名，主键
- `password`：用户登录密码
- `userType`：用户类别
- `apiKey`：API对应应用Key
- `apiSecret`：接口调用生成签名所需Secret
- `userStatus`：用户状态
- `isTempForbidden`：是否临时升级禁用
- `mobile`：手机号（唯一）
- `trueName`：真实姓名
- `email`：电子邮箱
- `qq`：QQ号
- `tel`：电话号码
- `company`：公司名称
- `province`：省份
- `city`：市级
- `town`：县级
- `address`：联系地址
- `remark`：备注
- `devLinkman`：开发者联系人
- `devMobile`：开发者手机号
- `devEmail`：开发者电子邮箱
- `devQQ`：开发者QQ号
- `targetRecordDB`：当前用户对应的DB实例名
- `regTime`：注册时间
- `occupaScale`：占用规模
- `taobaoAppKey`：淘宝AppKey

#### 2. 店铺信息表 (dev_shop)

存储用户的店铺信息。

**主要字段**：
- `shopId`：店铺id，主键
- `outShopId`：外部平台的店铺ID
- `outAccount`：外部账号名
- `outPlatTypes`：外部平台类型
- `shopName`：店铺名称
- `mobie`：店铺联系手机
- `token`：客户店铺唯一标识
- `platValue`：平台枚举值
- `isActived`：店铺是否启用
- `isDelete`：是否删除
- `userName`：用户名称
- `thirdAppKey`：代理商平台AppKey
- `thirdAppSecret`：代理商平台密钥
- `sessionKey`：会话编号
- `sessionKeyExpireTime`：SessionKey过期时间
- `sessionKeyTimeout`：SessionKey有效周期
- `refreshToken`：用于刷新失效的SessionKey
- `refreshTokenExpireTime`：刷新失效的SessionKey过期时间
- `authorizationDuration`：授权持续时间
- `authorizationTime`：授权时间
- `subscriptionExpireTime`：订购到期时间
- `customParms`：自用型平台自定义参数
- `createTime`：创建时间
- `lastModifyTime`：最后修改时间
- `authStatus`：授权状态
- `platShopId`：平台店铺ID
- `platShopName`：平台店铺名称

#### 3. 会员账号映射表 (member_account_mapping)

存储用户与外部平台账号的映射关系。

**主要字段**：
- `userName`：用户名
- `outPlatTypes`：外部平台类型
- `outAccount`：外部会员主账号
- `outaccountcode`：外部会员授权码
- `sessionkey`：外部授权Key
- `servername`：外部会员服务器名称
- `serveripport`：业务服务器IP端口
- `isactived`：外部账号映射表是否启用
- `createtime`：创建时间

#### 4. 配置表 (glo_db_config)

存储系统配置信息。

**主要字段**：
- `configKey`：配置键，主键
- `version`：版本号，主键
- `configType`：配置键分类，主键
- `descript`：描述
- `dataVersion`：数据版本

### 用户库表

#### 1. 商品匹配表 (g_api_sys_match)

存储商品匹配数据。

**主要字段**：
- `id`：ID，主键
- `shopId`：管家店铺ID
- `bTBGoods`：平台ID
- `isSys`：是否需要同步，0：不同步，1：待同步，99同步中
- `synFlag`：触发标识 0:无  1:入库审核；默认0
- `bGetStock`：用于标识是否可以更新IsSys字段
- `sysCount`：同步数量
- `sysLog`：同步结果
- `updateTime`：库存同步的更新时间
- `lastModify`：最后修改时间
- `goodsID`：管家货品ID
- `specID`：管家规格ID，-2表示批次商品
- `goodsType`：是否组合装
- `numiid`：平台货品ID
- `skuID`：平台规格ID
- `tBName`：平台商品名称
- `tBSku`：平台规格名称
- `tBOuterID`：平台商家主编码
- `skuOuterID`：平台商家子编码
- `sysGoodsType`：同步类型（出售中，仓库中）
- `subShopID`：子店铺ID
- `whseCode`：仓库代码
- `bFixNum`：是否同步固定数量
- `fixNum`：固定数量
- `bVirNum`：是否增加同步数量
- `virNumBase`：>=（库存同步规则中的特定同步规则中的）
- `virNumInc`：增加多少（库存同步规则中的特定同步规则中的）
- `bstop`：库存同步规则中的是否停用
- `bSingletb`：是否同步数量百分比
- `singleNumPer`：同步百分比

#### 2. 售后单表 (g_api_return_list)

存储售后单信息。

**主要字段**：
- `billId`：主键
- `refundId`：退款单编号
- `tradeID`：TradeID
- `getTime`：最后一次更新的时间
- `curStatus`：递交状态
- `synStatus`：发货状态
- `oldBillID`：原始单id（g_api_tradeList）
- `oldTid`：交易主订单号
- `oldOid`：交易子订单号
- `createdTime`：退款创建时间
- `returnReason`：申请退款原因
- `returnStatus`：退款状态
- `remark`：退款说明
- `type`：售后单类型，0未知；1天猫退款；2淘宝退款；3天猫退货
- `customerId`：客户昵称
- `nickUId`：卖家昵称ID
- `logisticName`：寄回物流公司名称
- `logisticNo`：寄回物流公司单号
- `shopID`：管家店铺id
- `refundFee`：实际退款金额
- `chgSndTo`：换货收件人
- `chgTel`：换货联系方式
- `chgProvince`：换货州省
- `chgCity`：换货地市
- `chgTown`：换货区县
- `changeAdr`：换货地址

#### 3. 售后单商品表 (g_api_return_detail)

存储售后单商品信息。

**主要字段**：
- `recId`：主键
- `billId`：售后单Id(g_api_return_list)
- `goodsId`：erp货品id
- `specId`：erp规格id
- `bFit`：是否为组合装
- `outerId`：商品外部商家编码
- `goodsCount`：商品数量
- `price`：商品价格
- `remark`：商家备注
- `platGoodsId`：平台商品id
- `platSkuId`：平台规格id
- `goodsTitle`：平台商品名称
- `sku`：平台规格名称
- `oid`：子订单号

### RDS推送库表

#### 1. 淘宝商品表 (tb_goods_rds)

存储淘宝商品信息。

**主要字段**：
- `id`：ID，主键
- `shop_id`：店铺ID
- `num_iid`：商品数字ID
- `outer_id`：商家编码
- `title`：商品标题
- `price`：商品价格
- `num`：库存数量
- `pic_url`：商品主图片地址
- `status`：商品状态
- `create_time`：创建时间
- `update_time`：更新时间

#### 2. 淘宝退款表 (tb_refund_rds)

存储淘宝退款信息。

**主要字段**：
- `id`：ID，主键
- `shop_id`：店铺ID
- `refund_id`：退款单号
- `tid`：交易单号
- `oid`：子订单号
- `buyer_nick`：买家昵称
- `refund_fee`：退款金额
- `status`：退款状态
- `create_time`：创建时间
- `update_time`：更新时间

#### 3. 换货退款表 (exchange_refund_rds)

存储换货退款信息。

**主要字段**：
- `id`：ID，主键
- `shop_id`：店铺ID
- `exchange_id`：换货单号
- `tid`：交易单号
- `buyer_nick`：买家昵称
- `status`：换货状态
- `create_time`：创建时间
- `update_time`：更新时间

## 数据访问层设计

### 基础Mapper接口

项目使用MyBatis作为ORM框架，定义了`BasicOperateMapper`接口作为基础Mapper：

```java
public interface BasicOperateMapper<T> extends Mapper<T>, MySqlMapper<T> {
}
```

通过继承`tk.mybatis.mapper.common.Mapper`和`tk.mybatis.mapper.common.MySqlMapper`，自动获得单表的基本CRUD操作。

### 自定义Mapper示例

```java
public interface DbConfigMapper extends BasicOperateMapper<GloDbConfigDO> {
    /**
     * 获取配置键
     * @param configKey 配置键
     * @param configType 配置键分类
     * @param version 版本号
     * @return 配置键
     */
    GloDbConfigDO selectByConfigKey(@Param("configKey")String configKey, @Param("configType")int configType, @Param("version")String version);
}
```

### MyBatis XML配置示例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.center.DbConfigMapper">
  <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.center.GloDbConfigDO">
    <id column="ConfigKey" jdbcType="VARCHAR" property="configKey" />
    <id column="Version" jdbcType="VARCHAR" property="version" />
    <id column="ConfigType" jdbcType="TINYINT" property="configType" />
    <result column="Descript" jdbcType="VARCHAR" property="descript" />
    <result column="DataVersion" jdbcType="TIMESTAMP" property="version" />
  </resultMap>
  <!-- 其他SQL语句 -->
</mapper>
```

## 数据库操作AOP

项目使用AOP实现数据库操作的拦截和增强：

### 数据库自动切换

```java
@Aspect
@Component
@Order(1)
public class DBSwitchUtil {
    @Around("@annotation(dataSourceAutoSwitcher)")
    public Object around(ProceedingJoinPoint point, DataSourceAutoSwitcher dataSourceAutoSwitcher) throws Throwable {
        // 获取注解参数
        SwitcherDBTypeEnum dbType = dataSourceAutoSwitcher.dbType();
        String userParamName = dataSourceAutoSwitcher.userParamName();

        // 获取用户参数值
        String userName = getUserNameFromArgs(point, userParamName);

        // 切换数据源
        try {
            SwitchDbContext context = new SwitchDbContext(dbType, userName);
            DataSourceContextHolder.set(context);
            return point.proceed();
        } finally {
            DataSourceContextHolder.remove();
        }
    }
}
```

### 表名修改拦截器

项目实现了`DbNameChangeInterceptor`拦截器，用于在SQL执行前修改表名，添加数据库名前缀：

```java
public Object intercept(Invocation invocation) throws Throwable {
    // 获取当前数据库名
    String dbName = DataSourceContextHolder.get().getDbName();

    // 获取SQL语句
    Object[] args = invocation.getArgs();
    MappedStatement ms = (MappedStatement) args[0];
    Object parameter = args[1];
    BoundSql boundSql = ms.getBoundSql(parameter);
    String sql = boundSql.getSql();

    // 解析SQL中的表名
    Set<String> names = TableNameHelper.getTableNames(sql);

    // 改写SQL中的表名为"会员数据库名.表名"
    for (String name : names) {
        sql = sql.replaceAll("(\\W)" + name + "(\\W|$)", "$1" + dbName + "." + name + "$2");
    }

    // 更新SQL
    FieldUtils.writeField(boundSql, "sql", sql, true);

    return invocation.proceed();
}
```

## 数据库缓存设计

项目实现了多级缓存机制，减少数据库访问：

### 配置缓存

```java
public class DbConfigCache extends AbstractHashCache<String, GloDbConfigDO> {
    /**
     * 获取数据源
     *
     * @param hashField 字段
     * @return 值
     */
    @Override
    protected GloDbConfigDO loadSource(String hashField) {
        DbConfigKey dbConfigKey = hashKeyConvert(hashField);
        if(dbConfigKey == null){
            return null;
        }

        DbConfigMapper dbConfigMapper = BeanContextUtil.getBean(DbConfigMapper.class);
        return DBSwitchUtil.doDBWithContext(SwitchDbContext.buildEsApi(),
                () -> dbConfigMapper.selectByConfigKey(dbConfigKey.getConfigKey().getName(),
                        dbConfigKey.getConfigType().getValue(),
                        dbConfigKey.getVersion()));
    }
}
```

## 数据库工具

### MyBatis代码生成器

项目包含MyBatis代码生成工具，位于`tools/mybatis-generator-for-api`目录：

- 支持生成数据库表DO类
- 生成Mapper接口
- 生成Mapper映射文件

## 数据库配置

### 多数据源配置

```java
@Configuration
@EnableConfigurationProperties(MultiDbProperties.class)
@AutoConfigureBefore(name = "com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure")
@Import({DbNameChangeInterceptor.class, DataSourceAutoSwitcherAop.class})
@ConditionalOnProperty(prefix = "wdgj.api.multidb", name = {"enable"}, havingValue = "true", matchIfMissing = true)
@ConditionalOnBean(value = {IMultiDbConnectInfoLocalCache.class, PoolProperties.class, JdbcUrlParam.class})
public class MultiDbAutoConfig {
    @Primary
    @Bean("multiDbDataSource")
    @ConditionalOnMissingBean(name = "multiDbDataSource")
    public BaseMultiDbDataSource getDynamicRds(@Autowired PoolProperties poolProperties,
                                              @Autowired JdbcUrlParam jdbcUrlParam,
                                              IMultiDbConnectInfoLocalCache localCache) throws Exception {
        // 配置动态数据源
        BaseMultiDbDataSource dataSource = new BaseMultiDbDataSource(poolProperties, jdbcUrlParam, localCache);
        return dataSource;
    }

    @Bean
    @Primary
    @Conditional(value = {JtaTransactionNotEanbedCondition.class})
    public DataSourceTransactionManager transactionManager(@Qualifier("multiDbDataSource") BaseMultiDbDataSource ds) {
        DataSourceTransactionManager manager = new DataSourceTransactionManager(ds);
        return manager;
    }
}
```

### 单数据源配置

```java
@Configuration
public class SingleDataSourceConfig extends DbConnectionInfo {
    /**
     * 单体中心库
     */
    public static final String CENTER_DB_DATASOURCE = "center_db_datasource";

    /**
     * 单体中心库数据源
     */
    @Bean(name = CENTER_DB_DATASOURCE)
    @ConfigurationProperties(prefix = "wdgj.api.center.db")
    public DataSource singleCenterDatasource() {
        return DataSourceBuilder.create().build();
    }
}
```

## 最佳实践

1. **使用DBSwitchUtil**：使用`DBSwitchUtil`工具类进行数据库操作，确保正确切换和释放数据源
2. **避免硬编码表名**：使用实体类和Mapper接口，避免SQL中硬编码表名
3. **合理使用缓存**：对频繁访问的数据使用缓存，减少数据库压力
4. **事务管理**：使用Spring的声明式事务管理，确保数据一致性
5. **批量操作**：处理大量数据时使用批量插入/更新，提高性能
6. **分页查询**：大数据量查询时使用分页，避免内存溢出
7. **参数化查询**：使用参数化查询，避免SQL注入风险
