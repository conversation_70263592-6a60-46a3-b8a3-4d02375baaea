# 换货单过滤转换逻辑分析 - 第3部分：数据结构、接口说明与最佳实践

## 6. 数据结构

### 6.1 主要数据对象

#### 6.1.1 源数据对象

- `SourceAfterSaleOrderItem<T>`：原始售后单数据
  - `T ployOrder`：菠萝派原始售后单数据（对于换货单，T为`BusinessGetExchangeOrderResponseOrderItem`）
  - `DbAfterSaleOrderItem dbOrder`：数据库中已存在的售后单数据
  - `AfterSaleSaveBizType bizType`：售后单业务类型
  - `DbAfterSaleOrderExtItem dbOrderExt`：售后单扩展数据，包含商品匹配信息和密文信息

- `SourceRefundGoodsItem<R>`：原始退货商品数据
  - `R ployRefundGoods`：菠萝派原始退货商品数据（对于换货单，R为`BusinessGetExchangeResponseRefundGoodInfo`）
  - `ApiTradeGoodsDO apiTradeGoods`：对应的交易商品数据

- `SourceExchangeGoodsItem<E>`：原始换货商品数据（换货单特有）
  - `E ployExchangeGoods`：菠萝派原始换货商品数据（对于换货单，E为`BusinessGetExchangeResponseExchangeGoodInfo`）

#### 6.1.2 目标数据对象

- `TargetCovertOrderItem`：转换后的目标售后单数据
  - `ApiReturnListDO afterSaleOrder`：售后单主表数据
  - `List<ApiReturnDetailDO> returnGoods`：售后单退货商品数据
  - `List<ApiReturnDetailTwoDO> exchangeGoods`：售后单换货商品数据（换货单特有）
  - `List<ApiReturnLogDO> afterSaleOrderLogs`：售后单日志数据
  - `List<IAfterSaleNotice> notices`：售后单通知数据
  - `AfterSaleProcessTypeEnum processType`：处理模式

#### 6.1.3 结果对象

- `AfterSaleHandleResult`：处理结果基类
  - `boolean success`：是否成功
  - `String message`：结果消息

- `GoodsConvertHandleResult`：商品转换结果，继承自`AfterSaleHandleResult`
  - `boolean isSaveGoods`：是否保存商品

- `FilterAndConvertOrderResult`：过滤转换结果
  - `boolean isNeedBizRetry`：是否需要业务重试
  - `AfterSaleProcessTypeEnum processType`：处理模式
  - `TargetCovertOrderItem targetOrder`：目标售后单数据

### 6.2 特有数据结构

换货单处理相比退货退款单处理，有以下特有的数据结构：

1. **换货商品数据**：`ApiReturnDetailTwoDO`，用于存储换货商品信息
2. **换货商品转换接口**：`IExchangeGoodsConvertHandle`，用于处理换货商品转换
3. **换货商品源数据**：`SourceExchangeGoodsItem`，用于封装原始换货商品数据
4. **组合类扩展**：`ExchangeHandleComposite`扩展了`ReturnHandleComposite`，增加了换货商品转换插件

## 7. 接口说明

### 7.1 核心接口

#### 7.1.1 前置批量处理接口

```java
public interface IPerBatchProcessOrderHandle<O> {
    /**
     * 前置批量处理
     *
     * @param orderItems 原始售后单列表
     * @return 过滤结果
     */
    AfterSaleHandleResult perBatchProcess(List<SourceAfterSaleOrderItem<O>> orderItems);
}
```

#### 7.1.2 前置过滤接口

```java
public interface IPreFiltrationOrderHandle<O> {
    /**
     * 前置过滤
     *
     * @param orderItem 原始售后单列表
     * @return 过滤结果
     */
    AfterSaleHandleResult preFiltration(SourceAfterSaleOrderItem<O> orderItem);
}
```

#### 7.1.3 订单转换接口

```java
public interface IOrderConvertHandle<O> {
    /**
     * 转换订单级信息
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    AfterSaleHandleResult convert(SourceAfterSaleOrderItem<O> sourceOrder, TargetCovertOrderItem targetOrder);
}
```

#### 7.1.4 退货商品转换接口

```java
public interface IRefundGoodsConvertHandle<O, R> {
    /**
     * 转换商品级信息
     *
     * @param orderItem   原始售后单数据
     * @param goodsItem   原始售后退货商品数据
     * @param targetOrder 目标售后单数据
     * @param refundGoods 目标售后退货商品数据
     * @return 结果
     */
    GoodsConvertHandleResult convert(SourceAfterSaleOrderItem<O> orderItem, SourceRefundGoodsItem<R> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailDO refundGoods);
}
```

#### 7.1.5 换货商品转换接口（换货单特有）

```java
public interface IExchangeGoodsConvertHandle<O, E> {
    /**
     * 转换商品级信息
     *
     * @param orderItem     原始售后单数据
     * @param goodsItem     原始售后换货商品数据
     * @param targetOrder   目标售后单数据
     * @param exchangeGoods 目标售后换货商品数据
     * @return 结果
     */
    GoodsConvertHandleResult convert(SourceAfterSaleOrderItem<O> orderItem, SourceExchangeGoodsItem<E> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods);
}
```

#### 7.1.6 后置处理接口

```java
public interface IPostProcessOrderHandle<O> {
    /**
     * 售后单后置处理
     *
     * @param sourceOrder 原始售后单数据
     * @param targetOrder 目标售后单数据
     * @return 结果
     */
    AfterSaleHandleResult process(SourceAfterSaleOrderItem<O> sourceOrder, TargetCovertOrderItem targetOrder);
}
```

### 7.2 组合类

`ExchangeHandleComposite`是换货单处理的核心组合类，它扩展了`ReturnHandleComposite`，增加了换货商品转换插件：

```java
public class ExchangeHandleComposite<O, R, E> extends ReturnHandleComposite<O, R> {
    //region 常量
    /**
     * 售后单换货商品数据转换插件列表
     */
    private final List<IExchangeGoodsConvertHandle<O, E>> exchangeGoodsConverts;
    //endregion

    /**
     * 构造
     *
     * @param perBatchQueryOrders   前置批量查询插件列表
     * @param subPreFilters         数据前置过滤插件列表
     * @param orderConverts         售后单数据转换插件列表
     * @param returnGoodsConverts   售后单退货商品数据转换插件列表
     * @param exchangeGoodsConverts 售后单换货商品数据转换插件列表
     * @param postProcesses         售后单后置处理插件列表
     */
    public ExchangeHandleComposite(List<IPerBatchProcessOrderHandle<O>> perBatchQueryOrders, 
                                  List<IPreFiltrationOrderHandle<O>> subPreFilters, 
                                  List<IOrderConvertHandle<O>> orderConverts, 
                                  List<IRefundGoodsConvertHandle<O, R>> returnGoodsConverts, 
                                  List<IExchangeGoodsConvertHandle<O, E>> exchangeGoodsConverts, 
                                  List<IPostProcessOrderHandle<O>> postProcesses) {
        super(perBatchQueryOrders, subPreFilters, orderConverts, returnGoodsConverts, postProcesses);
        this.exchangeGoodsConverts = exchangeGoodsConverts;
    }

    /**
     * 转换换货商品级信息
     *
     * @param sourceOrder   原始售后单数据
     * @param sourceGoods   原始售后换货商品数据
     * @param targetOrder   目标售后单数据
     * @param exchangeGoods 目标售后换货商品数据
     * @return 结果
     */
    public GoodsConvertHandleResult exchangeGoodsConvert(SourceAfterSaleOrderItem<O> sourceOrder, 
                                                        SourceExchangeGoodsItem<E> sourceGoods, 
                                                        TargetCovertOrderItem targetOrder, 
                                                        ApiReturnDetailTwoDO exchangeGoods) {
        if (CollectionUtils.isNotEmpty(exchangeGoodsConverts)) {
            for (IExchangeGoodsConvertHandle<O, E> goodsConvert : exchangeGoodsConverts) {
                GoodsConvertHandleResult result = goodsConvert.convert(sourceOrder, sourceGoods, targetOrder, exchangeGoods);
                // 失败或者换货商品不保存
                if (result.isFailed() || !result.isSaveGoods()) {
                    return result;
                }
            }
        }
        return GoodsConvertHandleResult.success();
    }
}
```

## 8. 异常处理

换货单过滤转换过程中的异常处理主要通过以下方式实现：

1. **结果对象**：使用`AfterSaleHandleResult`等结果对象封装处理结果，包含成功/失败状态和消息
2. **异常捕获**：在各处理插件中捕获异常，转换为失败结果
3. **日志记录**：记录异常信息，便于问题排查
4. **流程控制**：发生异常时，根据情况决定是否继续处理后续步骤

```java
public AfterSaleHandleResult preFiltration(SourceAfterSaleOrderItem<O> orderItem) {
    try {
        // 插件业务/覆写逻辑执行
        return coveringMethod != null
                ? coveringMethod.preFiltration(orderItem)
                : preFiltrationOrder(orderItem);
    } catch (Exception e) {
        String massage = String.format("【%s】【%s】售后单前置过滤订单失败-%s，原因：%s", context.getMemberName(), context.getShopId(), caption(), e.getMessage());
        log.error(massage, e);
        return GoodsConvertHandleResult.failed(massage);
    }
}
```

## 9. 修改指南

### 9.1 添加新的批量处理器

1. 创建新的批量处理器类，继承`AbstractPerBatchProcessOrderHandle`
2. 实现`perBatchQueryProcess`方法和`caption`方法
3. 在`BaseSaveExchangeOrderProcessor.createPerBatchQueryHandles()`方法中添加新的批量处理器

```java
public class NewBatchProcessHandle extends AbstractPerBatchProcessOrderHandle<BusinessGetExchangeOrderResponseOrderItem> {
    public NewBatchProcessHandle(AfterSaleSaveContext context) {
        super(context);
    }
    
    @Override
    protected AfterSaleHandleResult perBatchQueryProcess(List<SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem>> orderItems) {
        // 实现批量处理逻辑
        return AfterSaleHandleResult.success();
    }
    
    @Override
    protected String caption() {
        return "新的批量处理器";
    }
}
```

### 9.2 添加新的过滤器

1. 创建新的过滤器类，继承`AbstractPreFiltrationOrderHandle`
2. 实现`preFiltrationOrder`方法和`caption`方法
3. 在`BaseSaveExchangeOrderProcessor.createPreFiltrationHandles()`方法中添加新的过滤器

```java
public class NewExchangeFilterHandle extends AbstractPreFiltrationOrderHandle<BusinessGetExchangeOrderResponseOrderItem> {
    public NewExchangeFilterHandle(AfterSaleSaveContext context) {
        super(context);
    }
    
    @Override
    protected AfterSaleHandleResult preFiltrationOrder(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> orderItem) {
        // 实现过滤逻辑
        return AfterSaleHandleResult.success();
    }
    
    @Override
    protected String caption() {
        return "新的换货单过滤器";
    }
}
```

### 9.3 添加新的平台处理器

1. 创建新的处理器类，继承`BaseSaveExchangeOrderProcessor`
2. 根据需要重写相关方法
3. 在`SaveExchangeOrderFactory.createProcessor()`方法中添加新的平台处理器

```java
public class NewPlatformSaveExchangeOrderProcessor extends BaseSaveExchangeOrderProcessor {
    public NewPlatformSaveExchangeOrderProcessor(AfterSaleSaveContext context) {
        super(context);
    }
    
    @Override
    protected AfterSaleHandleResult orderConvert(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, TargetCovertOrderItem targetOrder) {
        // 调用父类方法
        AfterSaleHandleResult result = super.orderConvert(sourceOrder, targetOrder);
        if (result.isFailed()) {
            return result;
        }
        
        // 添加平台特定逻辑
        BusinessGetExchangeOrderResponseOrderItem ployOrder = sourceOrder.getPloyOrder();
        ApiReturnListDO afterSaleOrder = targetOrder.getAfterSaleOrder();
        
        // 设置平台特定字段
        afterSaleOrder.setCustomField(ployOrder.getSpecialField());
        
        return AfterSaleHandleResult.success();
    }
    
    @Override
    protected GoodsConvertHandleResult exchangeGoodsConvert(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> sourceOrder, SourceExchangeGoodsItem<BusinessGetExchangeResponseExchangeGoodInfo> sourceGoods, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods) {
        // 添加平台特定的换货商品转换逻辑
        return GoodsConvertHandleResult.success();
    }
}
```

### 9.4 添加新的换货商品转换器

1. 创建新的换货商品转换器类，继承`AbstractExchangeGoodsConvertHandle`
2. 实现`convertGoods`方法和`caption`方法
3. 在`BaseSaveExchangeOrderProcessor.createExchangeGoodsConvertsHandles()`方法中添加新的转换器

```java
public class NewExchangeGoodsConvertHandle extends AbstractExchangeGoodsConvertHandle<BusinessGetExchangeOrderResponseOrderItem, BusinessGetExchangeResponseExchangeGoodInfo> {
    public NewExchangeGoodsConvertHandle(AfterSaleSaveContext context) {
        super(context);
    }
    
    @Override
    protected GoodsConvertHandleResult convertGoods(SourceAfterSaleOrderItem<BusinessGetExchangeOrderResponseOrderItem> orderItem, SourceExchangeGoodsItem<BusinessGetExchangeResponseExchangeGoodInfo> goodsItem, TargetCovertOrderItem targetOrder, ApiReturnDetailTwoDO exchangeGoods) {
        // 实现换货商品转换逻辑
        return GoodsConvertHandleResult.success();
    }
    
    @Override
    protected String caption() {
        return "新的换货商品转换器";
    }
}
```

## 10. 注意事项

1. **过滤器顺序**：过滤器的执行顺序很重要，应确保基础过滤器先执行，特定过滤器后执行
2. **数据一致性**：转换过程中要保持数据的一致性，特别是涉及到历史数据的处理
3. **异常处理**：所有处理步骤都应有完善的异常处理机制，避免因异常导致整个流程中断
4. **平台差异**：不同平台的换货规则可能有较大差异，需要在各平台特定处理器中处理
5. **性能考虑**：批量处理和单个处理的平衡，避免过多的数据库操作
6. **扩展性**：设计时考虑扩展性，便于添加新的平台和新的处理逻辑
7. **日志记录**：关键步骤应有详细的日志记录，便于问题排查
8. **换货商品匹配**：换货商品的匹配逻辑可能与退货商品不同，需要特别注意
9. **密文处理**：换货单涉及收货地址等敏感信息，需要注意密文处理

## 11. 总结

换货单过滤转换逻辑采用了插件式架构设计，通过工厂模式、模板方法模式、策略模式等设计模式，实现了灵活、可扩展的处理流程。主要包括前置批量处理、前置过滤、订单级转换、退货商品级转换、换货商品级转换和后置处理六个环节，每个环节都有对应的插件实现。

与退货退款单相比，换货单处理的主要特点是增加了换货商品的处理环节，使用`ExchangeHandleComposite`扩展了`ReturnHandleComposite`，增加了换货商品转换插件。不同平台可以通过继承基础处理器并重写相关方法来实现特定的处理逻辑，或者通过添加新的批量处理器、过滤器、转换器等插件来扩展功能。

该设计使得系统能够灵活应对不同平台的换货单处理需求，同时保持了代码的可维护性和可扩展性。在进行修改时，应遵循现有的架构设计，通过添加或修改插件来实现新的功能，避免破坏整体架构。
