# 售后单下载业务 - 第4部分：异常处理与最佳实践

## 1. 异常处理

### 1.1 异常类型

售后单下载业务中可能出现的异常类型主要包括：

1. **业务异常**：由业务逻辑错误导致的异常，如参数校验失败、业务规则冲突等
2. **系统异常**：由系统错误导致的异常，如数据库连接失败、内存溢出等
3. **第三方异常**：由第三方服务错误导致的异常，如API调用失败、网络超时等
4. **平台异常**：由电商平台返回的错误导致的异常，如接口限流、权限不足等

### 1.2 异常处理策略

售后单下载业务采用了以下异常处理策略：

1. **统一异常捕获**：在门面层统一捕获异常，避免异常向上层传递
2. **异常分类处理**：根据异常类型采取不同的处理策略
3. **异常日志记录**：记录异常详细信息，便于问题排查
4. **友好错误提示**：向调用方返回友好的错误提示，隐藏技术细节
5. **子任务级别异常隔离**：子任务的异常不影响其他子任务的执行

### 1.3 异常处理实现

售后单下载业务的异常处理主要在`LoadAfterSaleFacade`类中实现：

```java
/**
 * 同步下载售后单
 *
 * @param param 售后单下载参数
 * @return 下载结果
 */
public LoadResult createTaskAndExec(LoadAfterSaleParam param) {
    WorkFacade facade = new WorkFacade();
    String taskId = StringUtils.EMPTY;
    try {
        // 业务处理逻辑...
    } catch (Exception e) {
        return exceptionResult(param.getMember(), taskId, e);
    }
}

/**
 * 异常结果
 *
 * @param memberName 会员名
 * @param taskId     任务ID
 * @param e          异常
 * @return 异常结果
 */
private LoadResult exceptionResult(String memberName, String taskId, Exception e) {
    // 记录异常日志
    String errorMessage = String.format("下载售后单异常, 原因: %s", e.getMessage());
    log.error(errorMessage, e);

    // 如果有任务ID，则完成任务
    if (StringUtils.isNotEmpty(taskId)) {
        WorkFacade facade = new WorkFacade();
        facade.workComplete(memberName, taskId, workType, new FailWorkResult(errorMessage));
    }

    // 返回异常结果
    return LoadResult.failedResult(errorMessage);
}
```

### 1.4 子任务异常处理

子任务的异常处理在`BaseLoadAfterSaleProcessor.execSubTask`方法中实现：

```java
/**
 * 执行子任务
 *
 * @param taskId   任务id
 * @param workData 工作任务数据
 * @param subTask  子任务
 * @return 子任务执行结果
 */
@Override
public final LoadAfterSaleWorkResult execSubTask(String taskId, WorkData<AfterSaleLoadArgs> workData, AfterSaleLoadSubTask subTask) {
    // 初始化结果
    LoadAfterSaleWorkResult pageResult = LoadAfterSaleWorkResult.onSuccess();

    try {
        // 业务处理逻辑...
    } catch (Exception e) {
        // 异常处理
        String errorMessage = String.format("【下载售后单异常】%s", e.getMessage());
        log.error(errorMessage, e);
        return pageResult.onFail(LoadAfterSaleWorkResult.onFail(subTask, errorMessage), errorMessage);
    } finally {
        // 调试日志
        LoadAfterSaleLogUtils.loadInfoLog(context, "售后单下载保存", () -> ExtUtils.stringBuilderAppend(String.format("下载售后单，子任务信息：%s；结果：%s。", JsonUtils.toJson(subTask), JsonUtils.toJson(pageResult))));
    }

    return pageResult;
}
```

## 2. 并发控制

### 2.1 并发问题

售后单下载业务中可能出现的并发问题主要包括：

1. **重复下载**：多个请求同时下载相同的售后单，导致数据重复
2. **资源竞争**：多个下载任务同时执行，导致系统资源紧张
3. **接口限流**：电商平台对接口调用频率有限制，并发请求可能导致限流

### 2.2 并发控制策略

售后单下载业务采用了以下并发控制策略：

1. **互斥锁**：通过`LoadAfterSaleWorkMutex`类实现任务级别的互斥，避免同一店铺的下载任务并发执行
2. **分页下载**：通过分页机制控制每次下载的数据量，避免一次性下载大量数据
3. **任务拆分**：将大任务拆分为多个小任务，分批执行，避免长时间占用资源
4. **队列任务**：通过队列任务机制，控制任务的执行顺序和并发数量

### 2.3 互斥锁实现

互斥锁是售后单下载业务的关键并发控制机制：

```java
/**
 * 售后单下载工作任务互斥
 */
public class LoadAfterSaleWorkMutex implements WorkMutex {
    /**
     * 获取互斥键
     *
     * @param workData 工作任务数据
     * @return 互斥键
     */
    @Override
    public String getMutexKey(WorkData<?> workData) {
        if (workData == null || workData.getContext() == null) {
            return null;
        }
        // 店铺级互斥
        return String.format("%s_%s", workData.getContext().getMemberName(), workData.getContext().getShopId());
    }

    /**
     * 获取互斥超时时间（秒）
     *
     * @return 互斥超时时间
     */
    @Override
    public int getMutexTimeoutSeconds() {
        return 60 * 60; // 1小时
    }
}
```

## 3. 性能优化

### 3.1 性能瓶颈

售后单下载业务中可能存在的性能瓶颈主要包括：

1. **网络延迟**：调用电商平台API可能存在网络延迟
2. **数据量大**：下载大量售后单数据可能导致内存占用过高
3. **保存耗时**：保存大量售后单数据可能导致数据库压力大
4. **并发限制**：电商平台对接口调用频率的限制可能导致下载速度慢

### 3.2 性能优化策略

售后单下载业务采用了以下性能优化策略：

1. **分页下载**：通过分页机制控制每次下载的数据量，避免一次性下载大量数据
2. **异步下载**：通过队列任务机制，异步执行下载任务，避免阻塞主线程
3. **批量保存**：通过批量保存机制，减少数据库交互次数
4. **缓存使用**：使用缓存减少重复查询，如店铺配置、平台业务特性等
5. **任务拆分**：将大任务拆分为多个小任务，分批执行，提高并行度

### 3.3 分页下载实现

分页下载是售后单下载业务的关键性能优化手段：

```java
/**
 * 创建运行模式
 *
 * @param taskId   任务id
 * @param workData 工作任务数据
 * @param subTask  子任务
 * @return 任务允许模式
 */
@Override
public final RunMode createRunMode(String taskId, WorkData<AfterSaleLoadArgs> workData, AfterSaleLoadSubTask subTask) {
    RunMode loadModel = null;
    // 根据业务特性设置下载模式
    LoadAfterSalesConfigContent platFeature = context.getDownloadAfterSalesPlatFeature(subTask.getApiShopType());
    if (platFeature != null) {
        switch (platFeature.getLoadOrderPageType()) {
            case DESC:
                loadModel = new DescPageRunMode(taskId, workData);
                break;
            case NEXT:
                loadModel = new NextTokenPageRunMode(taskId, workData);
                break;
            case ASC:
            default:
                loadModel = new AscPageRunMode(taskId, workData);
                break;
        }
    }

    // 按单号下载仅支持正序模式
    AfterSaleLoadArgs loadArgs = workData.getData();
    if (loadArgs.getLoadType() == AfterSaleLoadTypeEnum.AFTER_SALE_NO) {
        loadModel = new AscPageRunMode(taskId, workData);
    }
    return loadModel;
}
```

## 4. 最佳实践

### 4.1 售后单下载调用示例

以下是售后单下载业务的调用示例：

```java
/**
 * 普通下载示例
 */
public void shopLoadExample() {
    // 创建下载参数
    LoadAfterSaleParam param = new LoadAfterSaleParam();
    param.setMember("testMember");
    param.setShopId(123);
    param.setCreator("admin");
    param.setTriggerType(TriggerTypeEnum.MANUAL);
    
    // 设置下载参数
    AfterSaleLoadArgs loadArgs = new AfterSaleLoadArgs(AfterSaleLoadTypeEnum.SHOP_LOAD);
    param.setLoadArgs(loadArgs);
    
    // 调用下载方法
    LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
    LoadResult result = facade.createTaskAndExec(param);
    
    // 处理结果
    if (result.isSuccess()) {
        // 下载成功处理
        System.out.println("下载成功：" + result.getTaskId());
    } else {
        // 下载失败处理
        System.out.println("下载失败：" + result.getMessage());
    }
}
```

### 4.2 按时间下载示例

以下是按时间下载的示例：

```java
/**
 * 按时间下载示例
 */
public void timeRangeLoadExample() {
    // 创建下载参数
    LoadAfterSaleParam param = new LoadAfterSaleParam();
    param.setMember("testMember");
    param.setShopId(123);
    param.setCreator("admin");
    param.setTriggerType(TriggerTypeEnum.MANUAL);
    
    // 设置下载参数
    AfterSaleLoadArgs loadArgs = new AfterSaleLoadArgs(AfterSaleLoadTypeEnum.TIME_RANGE);
    loadArgs.setStartTime(LocalDateTime.now().minusDays(7));
    loadArgs.setEndTime(LocalDateTime.now());
    param.setLoadArgs(loadArgs);
    
    // 调用下载方法
    LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
    LoadResult result = facade.createTaskAndExec(param);
    
    // 处理结果
    if (result.isSuccess()) {
        // 下载成功处理
        System.out.println("下载成功：" + result.getTaskId());
    } else {
        // 下载失败处理
        System.out.println("下载失败：" + result.getMessage());
    }
}
```

### 4.3 按单号下载示例

以下是按单号下载的示例：

```java
/**
 * 按单号下载示例
 */
public void afterSaleNoLoadExample() {
    // 创建下载参数
    LoadAfterSaleParam param = new LoadAfterSaleParam();
    param.setMember("testMember");
    param.setShopId(123);
    param.setCreator("admin");
    param.setTriggerType(TriggerTypeEnum.MANUAL);
    
    // 设置下载参数
    AfterSaleLoadArgs loadArgs = new AfterSaleLoadArgs(AfterSaleLoadTypeEnum.AFTER_SALE_NO);
    loadArgs.setAfterSaleNos(Arrays.asList("RF123456", "RF123457"));
    param.setLoadArgs(loadArgs);
    
    // 调用下载方法
    LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
    LoadResult result = facade.createTaskAndExec(param);
    
    // 处理结果
    if (result.isSuccess()) {
        // 下载成功处理
        System.out.println("下载成功：" + result.getTaskId());
    } else {
        // 下载失败处理
        System.out.println("下载失败：" + result.getMessage());
    }
}
```

### 4.4 异步下载示例

以下是异步下载的示例：

```java
/**
 * 异步下载示例
 */
public void queueLoadExample() {
    // 创建下载参数
    LoadAfterSaleParam param = new LoadAfterSaleParam();
    param.setMember("testMember");
    param.setShopId(123);
    param.setCreator("admin");
    param.setTriggerType(TriggerTypeEnum.MANUAL);
    param.setPlat(PolyPlatEnum.BUSINESS_Taobao);
    
    // 设置下载参数
    AfterSaleLoadArgs loadArgs = new AfterSaleLoadArgs(AfterSaleLoadTypeEnum.SHOP_LOAD);
    param.setLoadArgs(loadArgs);
    
    // 调用异步下载方法
    LoadAfterSaleFacade facade = new LoadAfterSaleFacade();
    LoadResult result = facade.createQueueTask(param);
    
    // 处理结果
    if (result.isSuccess()) {
        // 下载成功处理
        System.out.println("异步下载任务创建成功：" + result.getTaskId());
    } else {
        // 下载失败处理
        System.out.println("异步下载任务创建失败：" + result.getMessage());
    }
}
```

## 5. 常见问题

### 5.1 售后单下载失败问题

**问题**：售后单下载失败，返回"下载售后单失败"错误。

**解决方案**：
1. 检查店铺配置是否正确，包括店铺授权、API权限等
2. 检查下载参数是否正确，如时间范围、售后单号等
3. 检查平台API是否正常，可能存在接口限流、服务不可用等情况
4. 查看日志，了解具体的错误原因，根据错误信息进行处理

### 5.2 售后单保存失败问题

**问题**：售后单下载成功，但保存失败，返回"下载售后单后保存全部失败"错误。

**解决方案**：
1. 检查售后单数据是否完整，可能存在必要字段缺失的情况
2. 检查数据库连接是否正常，可能存在数据库连接失败的情况
3. 检查是否存在数据冲突，如售后单已存在且不允许更新
4. 查看日志，了解具体的错误原因，根据错误信息进行处理

### 5.3 售后单下载超时问题

**问题**：售后单下载超时，任务长时间未完成。

**解决方案**：
1. 缩小下载时间范围，避免一次性下载大量数据
2. 使用异步下载方式，避免阻塞主线程
3. 检查平台API响应时间，可能存在网络延迟或服务响应慢的情况
4. 调整任务超时时间，适当延长超时时间

### 5.4 售后单数据不完整问题

**问题**：下载的售后单数据不完整，缺少部分字段或数据。

**解决方案**：
1. 检查平台API返回的数据是否完整，可能存在接口返回数据不全的情况
2. 检查数据转换逻辑，确保所有字段都正确转换
3. 检查是否需要调用详情接口，有些平台的列表接口返回的数据不全，需要调用详情接口获取完整数据
4. 更新平台业务特性配置，适配最新的接口返回格式

## 6. 修改指南

### 6.1 添加新平台支持

如果需要添加新平台的售后单下载支持，可以按照以下步骤进行：

1. **创建平台处理器**：继承`BaseLoadAfterSaleProcessor`类，创建新平台的处理器
2. **注册处理器**：在`LoadAfterSaleWorkFactory.createBusinessProcessor`方法中注册新平台的处理器
3. **实现平台特殊处理**：重写`subTaskCreatePlatProcess`、`onBeforeRequest`、`onAfterRequest`等方法，实现平台特殊处理
4. **添加平台下载实现**：实现`ILoadSubTask`接口，创建新平台的下载实现
5. **注册下载实现**：在`LoadSubTaskOperatorFactory.build`方法中注册新平台的下载实现
6. **添加平台配置**：在平台业务特性配置中添加新平台的配置信息

示例代码：

```java
/**
 * 新平台售后单下载处理器
 */
public class NewPlatformLoadAfterSaleProcessor extends BaseLoadAfterSaleProcessor {
    /**
     * 构造
     *
     * @param context 上下文
     */
    public NewPlatformLoadAfterSaleProcessor(AfterSaleLoadTaskContext context) {
        super(context);
    }
    
    /**
     * 子任务平台级特殊处理
     *
     * @param workData 工作任务数据
     * @param subTask  子任务
     * @return 特殊处理后子任务
     */
    @Override
    protected LoadAfterSaleWorkResult subTaskCreatePlatProcess(WorkData<AfterSaleLoadArgs> workData, AfterSaleLoadSubTask subTask) {
        // 实现新平台的特殊处理逻辑
        // ...
        return LoadAfterSaleWorkResult.onSuccess();
    }
    
    /**
     * 请求之前特殊处理
     *
     * @param loadType 业务类型
     * @param subTask  子任务
     */
    @Override
    protected void onBeforeRequest(AfterSaleQueryTypeEnum loadType, AfterSaleLoadSubTask subTask) {
        // 实现新平台的请求前处理逻辑
        // ...
    }
}
```

### 6.2 修改下载逻辑

如果需要修改售后单下载的逻辑，可以按照以下步骤进行：

1. **分析需求**：明确需要修改的下载逻辑，如分页方式、下载参数等
2. **定位代码**：找到需要修改的代码位置，如`BaseLoadAfterSaleProcessor`、`ILoadSubTask`实现类等
3. **修改代码**：修改相关代码，实现新的下载逻辑
4. **测试验证**：测试修改后的下载逻辑，确保功能正常
5. **更新文档**：更新相关文档，说明修改的内容和影响

### 6.3 添加新功能

如果需要添加新的售后单下载功能，可以按照以下步骤进行：

1. **分析需求**：明确新功能的需求和业务规则
2. **设计方案**：设计新功能的实现方案，包括数据结构、接口设计等
3. **实现功能**：实现新功能的代码，包括门面接口、业务逻辑等
4. **测试验证**：测试新功能，确保功能正常
5. **更新文档**：更新相关文档，说明新功能的使用方法和注意事项

## 7. 导航信息

本文档是售后单下载业务知识库的第4部分，主要介绍了异常处理和最佳实践。完整的知识库包含以下部分：

1. **第1部分：概述与架构设计**
2. **第2部分：核心流程与关键实现**
3. **第3部分：数据结构与接口说明**
4. **第4部分：异常处理与最佳实践**（当前文档）

请按照顺序阅读以获取完整的业务理解。
