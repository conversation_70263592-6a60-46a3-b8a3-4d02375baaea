# 缓存策略

## 概述

网店管家API项目实现了多层次的缓存策略，包括本地内存缓存和远程Redis缓存，以提高系统性能和响应速度。缓存策略的设计充分考虑了数据一致性、缓存过期、缓存更新等关键问题，为系统提供了高效的数据访问机制。

## 缓存架构

项目的缓存架构分为两个主要部分：

1. **本地缓存**：基于Caffeine实现的进程内内存缓存
2. **远程缓存**：基于Redis实现的分布式缓存

这种多层次的缓存架构具有以下优势：
- 本地缓存提供最快的数据访问速度
- 远程缓存支持跨实例的数据共享
- 分层设计减轻了数据库的访问压力
- 灵活的缓存策略适应不同的业务场景

## 本地缓存

### 核心组件

#### 1. AbstractLocalCache

`AbstractLocalCache`是本地缓存的抽象基类，基于Caffeine实现：

```java
public abstract class AbstractLocalCache<K, V> {
    // 本地缓存
    protected LoadingCache<K, Optional<V>> cache;
    
    // 默认最大数据个数,500
    protected int cacheMaxSize = 1000;
    
    // 默认缓存有效时间,10
    protected int expire = 10;
    
    // 默认缓存有效时间的单位,(分钟)
    protected TimeUnit timeUnit = TimeUnit.MINUTES;
    
    // 初始缓存大小
    protected int initialCapacity = 32;
    
    // 初始化缓存
    protected void checkInit() {
        if (inited) {
            return;
        }
        synchronized (this) {
            if (inited) {
                return;
            }
            this.cache = Caffeine.newBuilder()
                    // 最多存放数据个数
                    .maximumSize(cacheMaxSize)
                    // 设置初始缓存大小
                    .initialCapacity(initialCapacity)
                    // 缓存有效时间
                    .expireAfterWrite(expire, timeUnit)
                    .removalListener((RemovalListener<K, Optional<V>>) (k, v, removalCause) -> onRemove(k, v))
                    // 开启:记录状态数据功能
                    .recordStats()
                    .build(key -> loadData(key));
            inited = true;
        }
    }
    
    // 当缓存不存在时，会调用此函数来加载数据源
    private Optional<V> loadData(K key) {
        try {
            V value = loadSource(key);
            if(value == null) {
                return Optional.empty();
            }
            return Optional.of(value);
        } catch (Throwable ex) {
            log.warn("本地缓存加载数据", ex);
            throw ex;
        }
    }
    
    // 子类实现：加载数据源
    protected abstract V loadSource(K key);
    
    // 优先取缓存的有效数据,然后从数据源取
    public V getCacheThenSource(K key) {
        try {
            if (key == null) {
                throw new RuntimeException("取缓存数据key不能为空");
            }
            checkInit();
            
            Optional<V> value = LoopUtil.avoidThreadRecursionThrowable(() -> cache.get(key), this, key);
            return value.orElse(null);
            
        } catch (Throwable ex) {
            log.error("本地优先取缓存数据", ex);
            throw new RuntimeException(ex);
        }
    }
}
```

#### 2. AbstractUserLocalCache

`AbstractUserLocalCache`扩展了`AbstractLocalCache`，专门用于处理与用户相关的缓存：

```java
public abstract class AbstractUserLocalCache<K, V> extends AbstractLocalCache<AbstractUserLocalCache.UserKey<K>, V> implements ILocalCache<K, V> {
    
    // 获取会员的数据，优先取缓存的有效数据
    @Override
    public V getData(String wdgjUser, K key) {
        return this.getCacheThenSource(new UserKey<>(wdgjUser, key));
    }
    
    // 刷新本地缓存
    @Override
    public void refresh(String wdgjUser, K key) {
        this.cache.refresh(new UserKey<>(wdgjUser,key));
    }
    
    @Override
    protected V loadSource(UserKey<K> key) {
        return loadUserData(key.getWdgjUser(),key.getKey());
    }
    
    // 子类实现：加载用户数据
    protected abstract V loadUserData(String wdgjUser, K key);
}
```

### 本地缓存特性

1. **自动加载**：缓存miss时自动从数据源加载数据
2. **过期策略**：支持基于时间的过期策略
3. **容量限制**：支持基于容量的淘汰策略
4. **统计功能**：支持缓存命中率等统计信息
5. **线程安全**：支持并发访问
6. **防止缓存穿透**：使用Optional包装返回值，区分null值和不存在的key

### 本地缓存实现示例

```java
public class MultiDbConnectInfoLocalCache implements IMultiDbConnectInfoLocalCache {
    
    // 缓存过期时间
    private int expire = 30;
    
    // 缓存容量
    private int cacheMaxSize = 1000;
    
    // 数据源缓存
    private LoadingCache<DataSourceContext, DbConnectionInfo> dataSourceCache;
    
    // 单数据源缓存
    private LoadingCache<DataSourceContext, DataSource> singleDataSourceCache;
    
    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化缓存
        this.dataSourceCache = Caffeine.newBuilder()
                .maximumSize(cacheMaxSize)
                .expireAfterWrite(expire, TimeUnit.MINUTES)
                .build(key -> loadConnectionInfo(key));
                
        this.singleDataSourceCache = Caffeine.newBuilder()
                .maximumSize(cacheMaxSize)
                .expireAfterWrite(expire, TimeUnit.MINUTES)
                .build(key -> loadSingleDataSource(key));
    }
    
    @Override
    public DbConnectionInfo getCache(DataSourceContext context) {
        return this.dataSourceCache.get(context);
    }
    
    @Override
    public DataSource getSingleDataSource(DataSourceContext context) {
        return this.singleDataSourceCache.get(context);
    }
    
    // 加载连接信息
    private DbConnectionInfo loadConnectionInfo(DataSourceContext context) {
        // 实现数据加载逻辑
    }
    
    // 加载单数据源
    private DataSource loadSingleDataSource(DataSourceContext context) {
        // 实现数据源加载逻辑
    }
}
```

## 远程缓存

### 核心组件

#### 1. AbstractCache

`AbstractCache`是远程缓存的抽象基类：

```java
public abstract class AbstractCache implements InitializingBean {
    /**
     * 缓存实例对象
     */
    protected MultiRedis cacher;
    
    @Override
    public void afterPropertiesSet() throws Exception {
        this.cacher = BeanContextUtil.getBean(MultiRedis.class);
    }
}
```

#### 2. AbstractHashCache

`AbstractHashCache`扩展了`AbstractCache`，实现了基于Hash结构的缓存操作：

```java
public abstract class AbstractHashCache<T> extends AbstractCache implements IHashDataCache<T> {
    /**
     * 缓存键
     */
    protected String cacheKey;
    
    /**
     * 缓存为空时的默认值
     */
    protected String defaultEmptyHashValue = "{}";
    
    /**
     * 构造方法
     *
     * @param cacheKey 缓存键
     */
    protected AbstractHashCache(String cacheKey) {
        this.cacheKey = cacheKey;
    }
    
    /**
     * 获取值类型
     *
     * @return 值类型
     */
    public abstract Class<T> getValueClazz();
    
    /**
     * 获取缓存
     *
     * @param cacheKey  缓存键
     * @param hashField 字段
     * @return 值
     */
    protected T getCache(String cacheKey, String hashField) {
        // 校验入参
        if (StringUtils.isEmpty(hashField)) {
            return null;
        }
        
        // 查询缓存
        String hashValueStr = this.cacher.hashGetStrValue(cacheKey, hashField);
        
        // 缓存值为缺省值：返回空
        if (this.defaultEmptyHashValue.equals(hashValueStr)) {
            return null;
        }
        // 缓存值为空：从数据源获取数据
        else if (hashValueStr == null) {
            // 从数据源获取数据
            T source = this.loadSource(hashField);
            
            // 同步缓存
            this.hashSingleSet(cacheKey, hashField, source);
            return source;
        }
        // 缓存值不为空且不为缺省值：反序列化值并返回
        else {
            return JsonUtils.deJson(hashValueStr, this.getValueClazz());
        }
    }
    
    /**
     * 加载数据源
     *
     * @param hashField 字段
     * @return 值
     */
    protected abstract T loadSource(String hashField);
}
```

#### 3. AbstractHashExpireCache

`AbstractHashExpireCache`扩展了`AbstractCache`，实现了支持字段过期的Hash缓存：

```java
public abstract class AbstractHashExpireCache<T> extends AbstractCache implements IHashExpireCache<T> {
    /**
     * 缓存键
     */
    protected String cacheKey;
    
    /**
     * 缓存为空时的默认值
     */
    protected String defaultEmptyHashValue = "{}";
    
    /**
     * 缓存键最小过期时间（秒）
     */
    protected int minCacheKeyTimeOut = 60;
    
    /**
     * 缓存键最大过期时间（秒）
     */
    protected int maxCacheKeyTimeOut = 120;
    
    /**
     * 字段最小过期时间（秒）
     */
    protected int minFieldTimeout = 10;
    
    /**
     * 字段最大过期时间（秒）
     */
    protected int maxFieldTimeout = 20;
    
    /**
     * 构造方法
     *
     * @param cacheKey 缓存键
     */
    protected AbstractHashExpireCache(String cacheKey) {
        this.cacheKey = cacheKey;
    }
    
    /**
     * 获取值类型
     *
     * @return 值类型
     */
    public abstract Class<T> getValueClazz();
    
    /**
     * 查询缓存
     *
     * @param hashField 字段
     * @return 值
     */
    @Override
    public T getAndSyncIfAbsent(String hashField) {
        // 校验入参
        if (StringUtils.isEmpty(hashField)) {
            return null;
        }
        
        // 查询缓存
        String hashValueStr = this.cacher.hashExpireGet(this.cacheKey, hashField);
        
        // 缓存值为缺省值：返回空
        if (this.defaultEmptyHashValue.equals(hashValueStr)) {
            return null;
        }
        // 缓存值为空：从数据源获取数据
        else if (hashValueStr == null) {
            // 从数据源获取数据
            T source = this.loadSource(hashField);
            
            // 同步缓存
            this.hashExpireSet(hashField, source);
            return source;
        }
        // 缓存值不为空且不为缺省值：反序列化值并返回
        else {
            return JsonUtils.deJson(hashValueStr, this.getValueClazz());
        }
    }
    
    /**
     * 更新缓存（支持字段过期）
     *
     * @param hashField 字段
     * @param hashValue 值
     * @return 是否成功
     */
    @Override
    public boolean hashExpireSet(String hashField, T hashValue) {
        // 校验入参
        if (StringUtils.isEmpty(hashField)) {
            return false;
        }
        
        // 缓存值为空：设置缺省值
        if (hashValue == null) {
            return this.cacher.hashExpireSet(this.cacheKey, hashField, this.defaultEmptyHashValue, 
                    this.minCacheKeyTimeOut, this.maxCacheKeyTimeOut, 
                    this.minFieldTimeout, this.maxFieldTimeout);
        }
        // 缓存值不为空：序列化值并设置
        else {
            String hashValueStr = JsonUtils.toJson(hashValue);
            return this.cacher.hashExpireSet(this.cacheKey, hashField, hashValueStr, 
                    this.minCacheKeyTimeOut, this.maxCacheKeyTimeOut, 
                    this.minFieldTimeout, this.maxFieldTimeout);
        }
    }
    
    /**
     * 加载数据源
     *
     * @param hashField 字段
     * @return 值
     */
    protected abstract T loadSource(String hashField);
}
```

### 远程缓存特性

1. **多数据源支持**：支持多个Redis实例，适应不同的业务场景
2. **Hash结构**：使用Hash结构存储数据，减少内存占用
3. **字段过期**：支持Hash字段级别的过期时间设置
4. **自动加载**：缓存miss时自动从数据源加载数据
5. **空值缓存**：使用默认空值缓存null结果，防止缓存穿透
6. **批量操作**：支持批量获取和更新缓存

### 远程缓存实现示例

```java
public class DbConfigCache extends AbstractHashExpireCache<GloDbConfigDO> {
    
    private DbConfigCache() {
        super(DataCacheKeyEnum.DT_GLO_DBCONFIG.getCode());
        // 设置cache过期时间 30min~60min
        this.minCacheKeyTimeOut = 30 * 60;
        this.maxCacheKeyTimeOut = 60 * 60;
        // 设置hashKey过期时间 1min~2min
        this.minFieldTimeout  = 60;
        this.maxFieldTimeout  = 120;
    }
    
    /**
     * 枚举单例
     */
    public static DbConfigCache singleton() {
        return DbConfigCache.SingletonEnum.SINGLETON.instance;
    }
    
    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;
        
        private final DbConfigCache instance;
        
        private SingletonEnum() {
            instance = new DbConfigCache();
        }
    }
    
    /**
     * 获取值类型
     */
    @Override
    public Class<GloDbConfigDO> getValueClazz() {
        return GloDbConfigDO.class;
    }
    
    /**
     * 加载数据源
     */
    @Override
    protected GloDbConfigDO loadSource(String hashField) {
        DbConfigKey dbConfigKey = hashKeyConvert(hashField);
        if(dbConfigKey == null){
            return null;
        }
        
        DbConfigMapper dbConfigMapper = BeanContextUtil.getBean(DbConfigMapper.class);
        return DBSwitchUtil.doDBWithContext(SwitchDbContext.buildEsApi(),
                () -> dbConfigMapper.selectByConfigKey(dbConfigKey.getConfigKey().getName(),
                        dbConfigKey.getConfigType().getValue(),
                        dbConfigKey.getVersion()));
    }
}
```

## 缓存类型

项目定义了多种缓存类型，通过`DataCacheTypeEnum`枚举进行区分：

```java
public enum DataCacheTypeEnum implements CodeEnum {
    /**
     * 基础缓存,ConfigKeyEnum.Redis_ServerV2_Base
     */
    NORMAL_BASE(BasicMultiRedisProperties.class, "NORMAL_BASE"),
    /**
     * 实时缓存,ConfigKeyEnum.Redis_ServerV2_RealtimeData
     */
    REALTIME_DATA(RealtimeMultiRedisProperties.class, "REALTIME_DATA"),
    /**
     * dotNet基础缓存
     */
    DOT_NET_NORMAL_BASE(DotNetBasicMultiRedisProperties.class, "DOT_NET_NORMAL_BASE"),
    /**
     * 网店管家云端基础缓存
     */
    WDGJYUN_MEMBER(MemberMultiRedisProperties.class, "WDGJYUN_MEMBER"),
}
```

不同的缓存类型对应不同的Redis实例和配置，适应不同的业务场景：

1. **NORMAL_BASE**：基础缓存，用于存储通用配置和不频繁变更的数据
2. **REALTIME_DATA**：实时缓存，用于存储频繁变更的数据
3. **DOT_NET_NORMAL_BASE**：.NET基础缓存，用于与.NET系统共享的数据
4. **WDGJYUN_MEMBER**：会员缓存，用于存储会员相关的数据

## 主要缓存实现

### 1. 配置缓存

```java
public class DbConfigCache extends AbstractHashExpireCache<GloDbConfigDO> {
    // 实现配置缓存
}
```

用于缓存系统配置信息，减少数据库访问。

### 2. 会员缓存

```java
public class ApiMemberCache extends AbstractHashReadOnlyCache<MemberUserDO> {
    // 实现会员缓存
}
```

用于缓存会员基础信息，提高会员相关操作的性能。

### 3. 店铺缓存

```java
public class ApiAllShopCache extends AbstractHashCache<ApiAllShopDto> {
    // 实现店铺缓存
}
```

用于缓存店铺信息，提高店铺相关操作的性能。

### 4. 平台业务特性缓存

```java
public class PlatBizFeatureCache extends AbstractHashExpireCache<PlatBizFeatureCacheDto> {
    // 实现平台业务特性缓存
}
```

用于缓存平台业务特性配置，支持不同平台的业务处理。

### 5. 数据源缓存

```java
public class WdgjYunMemberDataSourceCache extends AbstractHashCache<WdgjYunMemberDataSourceDto> {
    // 实现数据源缓存
}
```

用于缓存数据库连接信息，支持多数据源切换。

## 缓存更新策略

项目实现了多种缓存更新策略，确保缓存数据的一致性：

### 1. 过期更新

通过设置缓存的过期时间，在缓存过期后自动从数据源重新加载数据：

```java
// 设置cache过期时间 30min~60min
this.minCacheKeyTimeOut = 30 * 60;
this.maxCacheKeyTimeOut = 60 * 60;
// 设置hashKey过期时间 1min~2min
this.minFieldTimeout  = 60;
this.maxFieldTimeout  = 120;
```

### 2. 主动更新

在数据变更时主动更新缓存：

```java
// 更新缓存
public void updateCache(String hashField, T hashValue) {
    this.hashExpireSet(hashField, hashValue);
}

// 删除缓存
public void removeCache(String hashField) {
    this.cacher.hashRemove(this.cacheKey, hashField);
}
```

### 3. 批量更新

支持批量更新缓存，提高性能：

```java
// 批量更新缓存
public void batchUpdateCache(Map<String, T> hashFieldValueMap) {
    for (Map.Entry<String, T> entry : hashFieldValueMap.entrySet()) {
        this.hashExpireSet(entry.getKey(), entry.getValue());
    }
}
```

## 缓存一致性保障

项目通过以下机制保障缓存的一致性：

1. **合理的过期时间**：根据数据的变更频率设置合理的过期时间
2. **随机过期时间**：使用最小和最大过期时间范围，避免缓存雪崩
3. **空值缓存**：缓存null结果，防止缓存穿透
4. **主动更新**：在数据变更时主动更新缓存
5. **本地缓存与远程缓存结合**：通过多级缓存减轻系统压力

## 最佳实践

1. **合理设置过期时间**：根据数据的变更频率设置合理的过期时间
2. **避免缓存穿透**：对于不存在的数据，缓存空值或默认值
3. **避免缓存雪崩**：使用随机过期时间，避免大量缓存同时过期
4. **合理使用本地缓存**：对于频繁访问且变更不频繁的数据，优先使用本地缓存
5. **监控缓存性能**：监控缓存的命中率、内存使用等指标，及时调整缓存策略
