# 线程池机制 - 第2部分：拒绝策略与任务适配器

## 拒绝策略

拒绝策略是线程池的重要组成部分，当线程池无法接受新任务时（如线程池已关闭或任务队列已满），会触发拒绝策略。项目实现了多种自定义拒绝策略，以满足不同业务场景的需求。

### 拒绝策略基类 (AbstractThreadPoolRejectedPolicy)

项目定义了`AbstractThreadPoolRejectedPolicy`作为拒绝策略的抽象基类，提供了统一的拒绝处理框架：

```java
public abstract class AbstractThreadPoolRejectedPolicy implements RejectedExecutionHandler {
    /**
     * 日志对象
     */
    protected static final Logger LOG = LoggerFactory.getLogger(AbstractThreadPoolRejectedPolicy.class);

    /**
     * 拒绝处理
     */
    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        // 记录拒绝日志
        LOG.warn("线程池拒绝策略触发，当前活动线程数：{}，队列大小：{}", executor.getActiveCount(), executor.getQueue().size());
        
        // 执行具体拒绝策略
        this.doReject(r, executor);
    }

    /**
     * 执行拒绝策略
     */
    protected abstract void doReject(Runnable r, ThreadPoolExecutor e);
}
```

### 主要拒绝策略实现

项目实现了多种拒绝策略，以满足不同业务场景的需求：

#### 1. 调用者运行策略 (CallerRunsRejectedPolicy)

当线程池拒绝任务时，由调用者线程执行任务，这种策略可以减缓新任务的提交速度：

```java
public class CallerRunsRejectedPolicy extends AbstractThreadPoolRejectedPolicy {
    @Override
    public void doReject(Runnable r, ThreadPoolExecutor e) {
        // 由调用者线程执行任务
        r.run();
    }
}
```

**适用场景**：
- 任务执行时间较短
- 调用者线程可以承担任务执行
- 需要保证任务一定被执行

#### 2. 等待策略 (WaitSecondAndRejectedPolicy)

当线程池拒绝任务时，先等待一段时间，尝试将任务放入队列，如果超时仍无法放入，则拒绝执行：

```java
public class WaitSecondAndRejectedPolicy extends AbstractThreadPoolRejectedPolicy {
    /**
     * 最大等待时间（单位秒）
     */
    private long maxWait;

    public WaitSecondAndRejectedPolicy() {
        // 默认3秒
        this.maxWait = 3;
    }

    @Override
    public void doReject(Runnable r, ThreadPoolExecutor e) {
        try {
            BlockingQueue<Runnable> queue = e.getQueue();
            if (!queue.offer(r, this.maxWait, TimeUnit.SECONDS)) {
                // 先等待3秒，超时后拒绝执行
                LOG.error("稍等拒绝策略,阻塞超时后，拒绝执行:{}", r.toString());
                throw new RejectedExecutionException("任务稍等后仍被拒绝");
            } else {
                LOG.info("稍等拒绝策略,阻塞未超时:{}", r.toString());
            }
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new RejectedExecutionException("线程被中断", ex);
        }
    }
}
```

**适用场景**：
- 任务可以短暂等待
- 线程池负载可能会在短时间内降低
- 需要尽可能执行任务，但也接受拒绝

#### 3. 重新入队策略 (TryRequeueRunsPolicy)

当线程池拒绝任务时，尝试将任务重新放入队列，等待时间更长：

```java
public class TryRequeueRunsPolicy extends AbstractThreadPoolRejectedPolicy {
    @Override
    public void doReject(Runnable r, ThreadPoolExecutor e) {
        try {
            if (e.getQueue().offer(r, 60, TimeUnit.SECONDS)) {
                return;
            }
        } catch (InterruptedException ex) {
            throw new RejectedExecutionException(String.format("[线程池拒绝策略]尝试重新入队被中断,当前队列数：%d", e.getQueue().size()), ex);
        }
        throw new RejectedExecutionException(String.format("[线程池拒绝策略]尝试重新入队失败,当前队列数：%d", e.getQueue().size()));
    }
}
```

**适用场景**：
- 任务可以长时间等待
- 线程池负载波动较大
- 任务执行顺序不重要

#### 4. 丢弃策略 (DiscardRejectedPolicy)

当线程池拒绝任务时，直接丢弃任务，不抛出异常：

```java
public class DiscardRejectedPolicy extends AbstractThreadPoolRejectedPolicy {
    @Override
    public void doReject(Runnable r, ThreadPoolExecutor e) {
        // 直接丢弃任务，不做任何处理
        LOG.warn("丢弃拒绝策略,直接丢弃任务:{}", r.toString());
    }
}
```

**适用场景**：
- 任务可以安全丢弃
- 任务执行不影响系统正常运行
- 系统负载过高时优先保证系统稳定

#### 5. 丢弃最旧任务策略 (DiscardOldestRejectedPolicy)

当线程池拒绝任务时，丢弃队列中最旧的任务，然后尝试执行新任务：

```java
public class DiscardOldestRejectedPolicy extends AbstractThreadPoolRejectedPolicy {
    @Override
    public void doReject(Runnable r, ThreadPoolExecutor e) {
        if (!e.isShutdown()) {
            // 丢弃队列中最旧的任务
            e.getQueue().poll();
            // 执行新任务
            e.execute(r);
        }
    }
}
```

**适用场景**：
- 新任务比旧任务更重要
- 任务可以安全丢弃
- 队列中的任务具有时效性

### 拒绝策略选择

不同的线程池可以配置不同的拒绝策略，根据业务场景选择合适的策略：

```java
// 在TaskEnum中配置拒绝策略
API_COMMON(1.0, 1.0, 0L, Integer.MAX_VALUE, 2000, CallerRunsRejectedPolicy.class, "api-common", "API通用"),
API_AFTER_SALE_LOAD(5, 10, 60L, TimeUnit.SECONDS, 1000, 500, WaitSecondAndRejectedPolicy.class, "api-after-sale-load", "售后单下载"),
```

## 任务适配器

任务适配器是线程池机制的重要组成部分，用于适配不同类型的任务，提供统一的任务提交接口。

### 会员任务接口 (MemberTaskAble)

`MemberTaskAble`是会员任务的标记接口，表示任务与特定会员相关：

```java
public interface MemberTaskAble {
    /**
     * 获取会员名
     */
    String getMember();
}
```

### 会员Runnable适配器 (MemberRunnable)

`MemberRunnable`是实现了`MemberTaskAble`接口的`Runnable`适配器，用于执行与会员相关的无返回值任务：

```java
public abstract class MemberRunnable implements Runnable, MemberTaskAble {
    /**
     * 会员名
     */
    private String member;

    /**
     * 构造方法
     */
    public MemberRunnable(String member) {
        this.member = member;
    }

    /**
     * 获取会员名
     */
    @Override
    public String getMember() {
        return member;
    }

    /**
     * 设置会员名
     */
    public void setMember(String member) {
        this.member = member;
    }
}
```

使用示例：

```java
// 创建会员任务
MemberRunnable task = new MemberRunnable("user123") {
    @Override
    public void run() {
        // 任务执行逻辑
        System.out.println("执行会员任务：" + getMember());
    }
};

// 提交任务
ThreadPoolFactory.singleton().execute(TaskEnum.API_COMMON, task);
```

### 会员Callable适配器 (MemberCallable)

`MemberCallable`是实现了`MemberTaskAble`接口的`Callable`适配器，用于执行与会员相关的有返回值任务：

```java
public abstract class MemberCallable<V> implements Callable<V>, MemberTaskAble {
    /**
     * 会员名
     */
    private String member;

    /**
     * 构造方法
     */
    public MemberCallable(String member) {
        this.member = member;
    }

    /**
     * 获取会员名
     */
    @Override
    public String getMember() {
        return member;
    }

    /**
     * 设置会员名
     */
    public void setMember(String member) {
        this.member = member;
    }
}
```

使用示例：

```java
// 创建会员任务
MemberCallable<String> task = new MemberCallable<String>("user123") {
    @Override
    public String call() throws Exception {
        // 任务执行逻辑
        return "执行会员任务：" + getMember();
    }
};

// 提交任务
Future<String> future = ThreadPoolFactory.singleton().submit(TaskEnum.API_COMMON, task);
String result = future.get();
```

### 自定义线程 (CustomThread)

`CustomThread`是自定义的线程类，用于在线程中存储额外的信息：

```java
public class CustomThread extends Thread {
    /**
     * 线程ID
     */
    private String threadId;

    /**
     * 构造方法
     */
    public CustomThread(Runnable target, String name) {
        super(target, name);
        this.threadId = UUID.randomUUID().toString();
    }

    /**
     * 获取线程ID
     */
    public String getThreadId() {
        return threadId;
    }
}
```

### 线程工厂 (CustomThreadFactory)

`CustomThreadFactory`是自定义的线程工厂，用于创建`CustomThread`：

```java
public class CustomThreadFactory implements ThreadFactory {
    /**
     * 线程名前缀
     */
    private String namePrefix;

    /**
     * 线程计数器
     */
    private AtomicInteger threadNumber = new AtomicInteger(1);

    /**
     * 构造方法
     */
    public CustomThreadFactory(String namePrefix) {
        this.namePrefix = namePrefix;
    }

    /**
     * 创建线程
     */
    @Override
    public Thread newThread(Runnable r) {
        String threadName = namePrefix + "-" + threadNumber.getAndIncrement();
        return new CustomThread(r, threadName);
    }
}
```

## 线程池监控

线程池监控是线程池机制的重要组成部分，用于监控线程池的运行状态，及时发现和处理异常情况。

### 线程池监控器 (ThreadPoolMonitor)

`ThreadPoolMonitor`是线程池监控器，用于定期收集线程池的运行状态：

```java
@Component
public class ThreadPoolMonitor {
    /**
     * 日志对象
     */
    private static final Logger LOG = LoggerFactory.getLogger(ThreadPoolMonitor.class);

    /**
     * 定时收集线程池状态
     */
    @Scheduled(fixedRate = 60000)
    public void collectThreadPoolStatus() {
        try {
            // 获取所有线程池信息
            Map<String, Map<String, Object>> poolInfoMap = new HashMap<>();
            for (TaskEnum taskEnum : TaskEnum.values()) {
                Map<String, Object> poolInfo = ThreadPoolFactory.singleton().getPoolInfo(taskEnum);
                poolInfoMap.put(taskEnum.name(), poolInfo);
            }
            
            // 记录线程池状态
            LOG.info("线程池状态：{}", JsonUtils.toJson(poolInfoMap));
            
            // 检查线程池异常
            checkThreadPoolException(poolInfoMap);
        } catch (Exception e) {
            LOG.error("收集线程池状态异常", e);
        }
    }

    /**
     * 检查线程池异常
     */
    private void checkThreadPoolException(Map<String, Map<String, Object>> poolInfoMap) {
        for (Map.Entry<String, Map<String, Object>> entry : poolInfoMap.entrySet()) {
            String taskEnumName = entry.getKey();
            Map<String, Object> poolInfo = entry.getValue();
            
            // 检查活动线程数
            int activeCount = (int) poolInfo.get("activeCount");
            int maximumPoolSize = (int) poolInfo.get("maximumPoolSize");
            if (activeCount >= maximumPoolSize) {
                LOG.warn("线程池活动线程数达到最大值，线程池：{}，活动线程数：{}，最大线程数：{}", taskEnumName, activeCount, maximumPoolSize);
            }
            
            // 检查队列大小
            int queueSize = (int) poolInfo.get("queueSize");
            int queueCapacity = (int) poolInfo.get("queueRemainingCapacity") + queueSize;
            if (queueSize >= queueCapacity * 0.8) {
                LOG.warn("线程池队列接近满，线程池：{}，队列大小：{}，队列容量：{}", taskEnumName, queueSize, queueCapacity);
            }
        }
    }
}
```

### 线程池告警事件 (ThreadPoolBlockingQueueAlarmEvent)

`ThreadPoolBlockingQueueAlarmEvent`是线程池队列告警事件，当线程池队列大小达到报警阈值时触发：

```java
public class ThreadPoolBlockingQueueAlarmEvent extends ApplicationEvent {
    /**
     * 线程池枚举
     */
    private TaskEnum taskEnum;

    /**
     * 队列大小
     */
    private int queueSize;

    /**
     * 队列报警阈值
     */
    private int alarmLimit;

    /**
     * 构造方法
     */
    public ThreadPoolBlockingQueueAlarmEvent(Object source, TaskEnum taskEnum, int queueSize, int alarmLimit) {
        super(source);
        this.taskEnum = taskEnum;
        this.queueSize = queueSize;
        this.alarmLimit = alarmLimit;
    }

    // getter方法...
}
```

### 线程池告警监听器 (ThreadPoolAlarmListener)

`ThreadPoolAlarmListener`是线程池告警监听器，用于处理线程池告警事件：

```java
@Component
public class ThreadPoolAlarmListener implements ApplicationListener<ThreadPoolBlockingQueueAlarmEvent> {
    /**
     * 日志对象
     */
    private static final Logger LOG = LoggerFactory.getLogger(ThreadPoolAlarmListener.class);

    /**
     * 处理线程池告警事件
     */
    @Override
    public void onApplicationEvent(ThreadPoolBlockingQueueAlarmEvent event) {
        TaskEnum taskEnum = event.getTaskEnum();
        int queueSize = event.getQueueSize();
        int alarmLimit = event.getAlarmLimit();
        
        // 记录告警日志
        LOG.warn("线程池队列告警，线程池：{}，队列大小：{}，告警阈值：{}", taskEnum.name(), queueSize, alarmLimit);
        
        // 发送告警通知
        sendAlarmNotification(taskEnum, queueSize, alarmLimit);
    }

    /**
     * 发送告警通知
     */
    private void sendAlarmNotification(TaskEnum taskEnum, int queueSize, int alarmLimit) {
        // 构建告警内容
        AlarmContent content = AlarmContent.build(
            AlarmIntervalTypeEnum.THREAD_POOL_QUEUE_ALARM,
            String.format("线程池队列告警，线程池：%s，队列大小：%d，告警阈值：%d", taskEnum.name(), queueSize, alarmLimit)
        );
        
        // 发送告警
        AlarmOperator.singleton().alarmInterval(
            AlarmIntervalTypeEnum.THREAD_POOL_QUEUE_ALARM,
            taskEnum.name(),
            content
        );
    }
}
```

## 线程池适配器

线程池适配器是线程池机制的扩展部分，用于适配不同框架的线程池，提供统一的线程池管理接口。

### Quartz线程池适配器 (QuartzThreadPoolAdapter)

`QuartzThreadPoolAdapter`是Quartz线程池适配器，用于将Quartz的线程池适配为项目的线程池：

```java
public class QuartzThreadPoolAdapter implements org.quartz.spi.ThreadPool {
    /**
     * 线程池
     */
    private BaseThreadPoolExecutor threadPool;

    /**
     * 初始化
     */
    @Override
    public void initialize() throws SchedulerConfigException {
        // 获取线程池
        threadPool = ThreadPoolFactory.singleton().getPool(TaskEnum.API_QUARTZ_EXEC);
    }

    /**
     * 执行任务
     */
    @Override
    public boolean runInThread(Runnable runnable) {
        try {
            // 提交任务
            threadPool.execute(runnable);
            return true;
        } catch (RejectedExecutionException e) {
            return false;
        }
    }

    /**
     * 获取线程池大小
     */
    @Override
    public int getPoolSize() {
        return threadPool.getPoolSize();
    }

    /**
     * 设置线程池大小（不支持）
     */
    @Override
    public void setInstanceId(String instanceId) {
        // 不支持
    }

    /**
     * 设置实例名（不支持）
     */
    @Override
    public void setInstanceName(String instanceName) {
        // 不支持
    }

    /**
     * 关闭线程池
     */
    @Override
    public void shutdown(boolean waitForJobsToComplete) {
        // 不需要关闭，由Spring容器管理
    }

    /**
     * 获取线程池大小（不支持）
     */
    @Override
    public boolean isShutdown() {
        return threadPool.isShutdown();
    }
}
```

### Spring异步线程池适配器 (SpringAsyncThreadPoolAdapter)

`SpringAsyncThreadPoolAdapter`是Spring异步线程池适配器，用于将Spring的异步线程池适配为项目的线程池：

```java
@Configuration
public class SpringAsyncThreadPoolAdapter implements AsyncConfigurer {
    /**
     * 获取异步执行器
     */
    @Override
    public Executor getAsyncExecutor() {
        // 获取线程池
        return ThreadPoolFactory.singleton().getPool(TaskEnum.API_SPRING_ASYNC);
    }

    /**
     * 获取异常处理器
     */
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new AsyncUncaughtExceptionHandler() {
            @Override
            public void handleUncaughtException(Throwable ex, Method method, Object... params) {
                // 记录异常日志
                LogFactory.get("Spring异步线程池").error(String.format("异步方法执行异常，方法：%s", method.getName()), ex);
            }
        };
    }
}
```
