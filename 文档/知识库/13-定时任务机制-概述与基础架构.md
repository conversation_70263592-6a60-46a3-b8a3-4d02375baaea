# 定时任务机制 - 概述与基础架构

## 概述

网店管家API项目实现了一套完善的定时任务机制，基于Quartz框架进行扩展，支持单机任务、分布式任务、集群任务和队列任务等多种任务类型。该机制通过抽象和封装，提供了统一的任务定义、调度和执行接口，同时支持任务监控、失败重试和负载均衡等高级特性。

## 定时任务整体架构

项目的定时任务架构采用了分层设计，从底层到顶层依次为：

1. **基础层**：基于Quartz的任务接口和调度器
2. **抽象层**：定义了不同类型任务的抽象基类
3. **实现层**：提供了各种类型任务的具体实现
4. **应用层**：业务定时任务的具体实现

整体继承关系如下：

```
Job (Quartz接口)
  ↑
SingleJob (单机任务接口)
  ↑
AbstractSingleJob (单机任务抽象实现)
  ↑
AbstractSingleBeanJob (支持执行策略的Bean定时任务)
  ↑
  ├── BaseSingleJob (单体定时任务)
  ├── AbstractSimpleDistributeJob (分布式定时任务)
  │     ↑
  │     └── AbstractUserSimpleDistributeJob (用户分布式定时任务)
  │           ↑
  │           └── AbstractUserFlowJob (流式用户简易分布式定时任务)
  ├── BaseClusterJob (集群定时任务)
  └── AbstractQueueJob (队列定时任务)
        ↑
        └── AbstractUserQueueJob (用户队列定时任务)
              ↑
              └── AbstractDynamicUserQueueJob (动态用户队列定时任务)
```

## 任务类型说明

项目支持以下几种类型的定时任务：

### 1. 单机任务 (BaseSingleJob)

单机任务是最基本的定时任务类型，在每个应用实例上独立运行，不考虑多实例间的协调。适用于独立的、无状态的任务，如本地缓存刷新、日志清理等。

### 2. 集群任务 (BaseClusterJob)

集群任务在一个集群的多个应用实例中，只有一个实例会执行定时任务，避免重复执行。适用于需要全局唯一执行的任务，如资源密集型任务、需要避免并发执行的任务等。

### 3. 分布式任务 (AbstractSimpleDistributeJob)

分布式任务将任务数据分片到多个应用实例上执行，实现并行处理。适用于大批量数据处理、需要并行执行提高效率的任务等。

### 4. 队列任务 (AbstractQueueJob)

队列任务通过队列机制管理任务数据，支持多实例负载均衡执行，同时保证任务的可靠性和持久性。适用于需要持续多次或无限次执行的任务、需要高频快速执行的任务等。

### 5. 用户任务

用户任务是针对多用户系统设计的特殊任务类型，支持按用户分组执行任务，实现用户级别的任务隔离和负载均衡。包括用户分布式任务、用户队列任务和动态用户队列任务等。

## 任务配置与注册

### 任务配置

项目使用注解方式配置定时任务，主要包括以下注解：

1. **@SingleJobSchedule**：单机任务调度配置
   ```java
   @SingleJobSchedule(
       jobName = "示例单机任务",
       cron = "0 0/5 * * * ?",
       sitesToRun = SiteTypeCodeConst.WDGJ_API_BUSINESS
   )
   ```

2. **@ClusterJobParameter**：集群任务参数配置
   ```java
   @ClusterJobParameter(
       jobExecStrategy = JobNameHashExecStrategy.class
   )
   ```

3. **@SimpleDistributeJobParameter**：分布式任务参数配置
   ```java
   @SimpleDistributeJobParameter(
       jobContextStrategy = EurekaContextStrategy.class,
       jobShardingStrategy = AverageJobShardingStrategy.class
   )
   ```

### 任务注册

任务注册通过Spring的自动配置机制实现，主要步骤：

1. 定义任务类并继承相应的基类
2. 使用注解配置任务参数
3. 将任务类注册为Spring Bean
4. 系统启动时自动注册到Quartz调度器

示例：
```java
@Component
@SingleJobSchedule(
    jobName = "示例单机任务",
    cron = "0 0/5 * * * ?",
    sitesToRun = SiteTypeCodeConst.WDGJ_API_BUSINESS
)
public class ExampleSingleJob extends BaseSingleJob {
    @Override
    protected void doWork() {
        // 任务实现
    }
}
```

## 任务执行策略

### 1. 执行时间策略

通过`JobExecTimeStrategy`接口定义任务执行时间策略：

```java
public interface JobExecTimeStrategy {
    /**
     * 是否可以执行
     */
    boolean canExec();
}
```

主要实现包括：

- **AlwaysExecStrategy**：始终执行
- **WorkTimeExecStrategy**：工作时间执行
- **NonWorkTimeExecStrategy**：非工作时间执行
- **CustomTimeExecStrategy**：自定义时间段执行

### 2. 集群执行策略

通过`ClusterJobExecStrategy`接口定义集群任务执行策略：

```java
public interface ClusterJobExecStrategy {
    /**
     * 是否可执行
     */
    boolean executable(String jobName);
}
```

主要实现包括：

- **JobNameHashExecStrategy**：基于任务名的哈希策略
- **InstanceNameHashExecStrategy**：基于实例名的哈希策略
- **LeaderElectionExecStrategy**：领导者选举策略
