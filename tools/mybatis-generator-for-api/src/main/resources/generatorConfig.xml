<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="MysqlContext" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <!-- 通用mapper所在目录 -->
        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="com.differ.wdgj.api.user.biz.infrastructure.repository.core.BasicOperateMapper"/>
        </plugin>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="************************************************"
                        userId="honggw"
                        password="honggw3307">
        </jdbcConnection>

        <!-- 对应生成的pojo所在包 -->
        <javaModelGenerator targetPackage="com.differ.wdgj.api.user.biz.infrastructure.data.api.user" targetProject=""/>
<!--        <javaModelGenerator targetPackage="com.differ.wdgj.api.user.biz.infrastructure.data.api.user" targetProject="D:\git-differ\WDGJ.API\dev\tools\mybatis-generator-for-api\src\main\java"/>-->

		<!-- 对应生成的mapper所在目录 -->
        <sqlMapGenerator targetPackage="mapper" targetProject=""/>
<!--        <sqlMapGenerator targetPackage="mapper" targetProject="D:\git-differ\WDGJ.API\dev\tools\mybatis-generator-for-api\src\main\resources/"/>-->

		<!-- 配置mapper对应的java映射 -->
        <javaClientGenerator targetPackage="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user" targetProject="" type="XMLMAPPER"/>
<!--        <javaClientGenerator targetPackage="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user" targetProject="D:\git-differ\WDGJ.API\dev\tools\mybatis-generator-for-api\src\main\java" type="XMLMAPPER"/>-->

        <!-- 数据库表 -->
        <table tableName="g_services_caselist"></table>
    </context>
</generatorConfiguration>