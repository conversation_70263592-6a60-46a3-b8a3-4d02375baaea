package com.differ.wdgj.api.business.controller;

import com.differ.jackyun.framework.component.basic.dto.JackYunResponse;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.tasks.mq.jmq.plugins.StockSaveAdapterMqHandler;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试接口Controller
 *
 * <AUTHOR>
 * @date 2024/3/11 9:46
 */
@ConditionalOnProperty(prefix = "system.controller.test", name = {"enable"}, havingValue = "true", matchIfMissing = false)
@RestController
@RequestMapping("test")
public class TestController {

    /**
     * tts回调任务
     *
     * @return
     */
    @RequestMapping("testMq")
    public JackYunResponse testMq(String message){
        BeanContextUtil.getBean(StockSaveAdapterMqHandler.class).sendMessage(message);
        return JackYunResponse.sendSuccess("");
    }
}
