<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<include resource="org/springframework/boot/logging/logback/base.xml" />
<!--	<turboFilter class = "com.differ.wdgj.api.user.biz.infrastructure.initializer.log.CustomLogInitializer"></turboFilter>-->
	<!-- kafka日志类配置 -->
	<appender name="logstash" class="com.differ.jackyun.framework.component.log.kafka.LogbackKafkaAppender">
	
		<!-- 主题（固定） -->
		<topic>kafka-logstash-topic</topic>
		
		<!-- 管家正式（张北）环境ELK-Kafka地址 -->
		<producerConfig>bootstrap.servers=10.0.16.102:9092,10.0.16.103:9092,10.0.16.104:9092</producerConfig>
		
	</appender>
	
	<!-- 默认日志等级 -->
	<root level="INFO">
		<appender-ref ref="logstash" />
	</root>
</configuration>