package com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.shop.config.whsmatch.data.CommonWarehouseMatchDTO;
import org.junit.Assert;
import org.junit.Test;

import java.util.Set;

/**
 * 仓库匹配数据适配器 工厂
 *
 * <AUTHOR>
 * @date 2025/3/10 下午5:00
 */
public class WarehouseMatchAdapterFacadeTest extends AbstractSpringTest {
    /**
     * 获取店铺级仓库匹配数据列表
     */
    @Test
    public void normalWarehouseMatchAdapterTest(){
        String memberName = "api2017";
        int shopId = 1253;
        Set<CommonWarehouseMatchDTO> warehouseMatches = WarehouseMatchAdapterFacade.getAll(memberName, shopId);

        Assert.assertNotNull(warehouseMatches);
    }
}
