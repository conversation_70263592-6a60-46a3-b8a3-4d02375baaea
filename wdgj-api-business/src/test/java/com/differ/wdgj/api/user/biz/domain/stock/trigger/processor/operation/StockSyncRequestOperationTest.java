package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.operation;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockRequestGoodInfo;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncGoodsRequestPair;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 库存同步请求操作单元测试
 *
 * <AUTHOR>
 * @date 2025/6/17 13:00
 */
public class StockSyncRequestOperationTest {

    @Mock
    private StockSyncContext mockContext;

    @Mock
    private SyncStockShopConfig mockSyncStockConfig;

    private StockSyncRequestOperation operation;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 设置默认的上下文行为
        when(mockContext.getSyncStockConfig()).thenReturn(mockSyncStockConfig);
        
        operation = new StockSyncRequestOperation(mockContext);
    }

    /**
     * 测试构造函数 - 有效上下文
     */
    @Test
    public void testConstructor_ValidContext() {
        StockSyncContext context = createTestContext();
        StockSyncRequestOperation op = new StockSyncRequestOperation(context);
        
        assertNotNull(op);
        // 通过反射验证context字段
        try {
            java.lang.reflect.Field contextField = StockSyncRequestOperation.class.getDeclaredField("context");
            contextField.setAccessible(true);
            assertEquals(context, contextField.get(op));
        } catch (Exception e) {
            fail("Failed to access context field: " + e.getMessage());
        }
    }

    /**
     * 测试构造函数 - 空上下文
     */
    @Test
    public void testConstructor_NullContext() {
        assertDoesNotThrow(() -> {
            new StockSyncRequestOperation(null);
        });
    }

    /**
     * 测试封装商品级请求对 - 有效参数
     */
    @Test
    public void testFormatGoodsRequestPair_ValidParameters() {
        GoodsMatchEnhance matchEnhance = createTestMatchEnhance();
        GoodsStockCalculationResult goodsStockInfo = createTestGoodsStockInfo();
        
        // 执行测试
        StockSyncGoodsRequestPair result = operation.formatGoodsRequestPair(matchEnhance, goodsStockInfo);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(matchEnhance, result.getMatchEnhance());
        assertNotNull(result.getGoodsInfo());
        
        // 验证商品请求信息
        BusinessBatchSyncStockRequestGoodInfo goodsRequest = result.getGoodsInfo();
        assertEquals("TEST_OUTER_ID", goodsRequest.getOuterId());
        assertEquals("TEST_SKU_OUTER_ID", goodsRequest.getOutSkuId());
        assertEquals("12345", goodsRequest.getPlatProductId());
        assertEquals("67890", goodsRequest.getSkuId());
        assertEquals(SyncStockTypeEnum.Whole.getCode(), goodsRequest.getSyncStockType());
        assertEquals(100, goodsRequest.getQuantity().intValue());
        assertEquals(100, goodsRequest.getRealQuantity().intValue());
        assertEquals("MULTI_SIGN_TEST", goodsRequest.getWhseCode());
    }

    /**
     * 测试封装商品级请求对 - 有效参数
     */
    @Test
    public void testFormatGoodsRequest_ValidParameters() {
        GoodsMatchEnhance matchEnhance = createTestMatchEnhance();
        GoodsStockCalculationResult goodsStockInfo = createTestGoodsStockInfo();
        
        // 执行测试
        BusinessBatchSyncStockRequestGoodInfo result = operation.formatGoodsRequest(matchEnhance, goodsStockInfo);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("TEST_OUTER_ID", result.getOuterId());
        assertEquals("TEST_SKU_OUTER_ID", result.getOutSkuId());
        assertEquals("12345", result.getPlatProductId());
        assertEquals("67890", result.getSkuId());
        assertEquals(SyncStockTypeEnum.Whole.getCode(), result.getSyncStockType());
        assertEquals(100, result.getQuantity().intValue());
        assertEquals(100, result.getRealQuantity().intValue());
        assertEquals("MULTI_SIGN_TEST", result.getWhseCode());
    }

    /**
     * 测试零库存商品请求
     */
    @Test
    public void testFormatGoodsRequest_ZeroStock() {
        GoodsMatchEnhance matchEnhance = createTestMatchEnhance();
        GoodsStockCalculationResult goodsStockInfo = GoodsStockCalculationResult.success(BigDecimal.ZERO, BigDecimal.ZERO, new HashMap<>());
        
        // 执行测试
        BusinessBatchSyncStockRequestGoodInfo result = operation.formatGoodsRequest(matchEnhance, goodsStockInfo);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getQuantity().intValue());
        assertEquals(0, result.getRealQuantity().intValue());
    }

    /**
     * 测试大库存数量
     */
    @Test
    public void testFormatGoodsRequest_LargeStock() {
        GoodsMatchEnhance matchEnhance = createTestMatchEnhance();
        GoodsStockCalculationResult goodsStockInfo = GoodsStockCalculationResult.success(BigDecimal.valueOf(999999), BigDecimal.valueOf(999999), new HashMap<>());
        
        // 执行测试
        BusinessBatchSyncStockRequestGoodInfo result = operation.formatGoodsRequest(matchEnhance, goodsStockInfo);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(999999, result.getQuantity().intValue());
        assertEquals(999999, result.getRealQuantity().intValue());
    }

    /**
     * 测试小数库存（应该转换为整数）
     */
    @Test
    public void testFormatGoodsRequest_DecimalStock() {
        GoodsMatchEnhance matchEnhance = createTestMatchEnhance();
        GoodsStockCalculationResult goodsStockInfo = GoodsStockCalculationResult.success(new BigDecimal("123.75"), new BigDecimal("123.75"), new HashMap<>());
        
        // 执行测试
        BusinessBatchSyncStockRequestGoodInfo result = operation.formatGoodsRequest(matchEnhance, goodsStockInfo);
        
        // 验证结果 - 小数部分应该被截断
        assertNotNull(result);
        assertEquals(123, result.getQuantity().intValue());
        assertEquals(123, result.getRealQuantity().intValue());
    }

    /**
     * 测试空匹配数据字段
     */
    @Test
    public void testFormatGoodsRequest_NullMatchData() {
        ApiSysMatchDO sysMatch = new ApiSysMatchDO();
        // 设置为null的字段
        sysMatch.settBOuterID(null);
        sysMatch.setSkuOuterID(null);
        sysMatch.setNumiid(null);
        sysMatch.setSkuID(null);
        
        GoodsMatchEnhance matchEnhance = GoodsMatchEnhance.create(sysMatch, "MULTI_SIGN_TEST");
        GoodsStockCalculationResult goodsStockInfo = createTestGoodsStockInfo();
        
        // 执行测试
        BusinessBatchSyncStockRequestGoodInfo result = operation.formatGoodsRequest(matchEnhance, goodsStockInfo);
        
        // 验证结果 - 应该能处理null值
        assertNotNull(result);
        assertNull(result.getOuterId());
        assertNull(result.getOutSkuId());
        assertNull(result.getPlatProductId());
        assertNull(result.getSkuId());
        assertEquals("MULTI_SIGN_TEST", result.getWhseCode());
    }

    /**
     * 测试空上下文
     */
    @Test
    public void testGetUpOrDownStatus_NullContext() {
        StockSyncRequestOperation opWithNullContext = new StockSyncRequestOperation(null);
        
        String result = opWithNullContext.getUpOrDownStatus(BigDecimal.valueOf(100));
        
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试空同步配置
     */
    @Test
    public void testGetUpOrDownStatus_NullSyncStockConfig() {
        when(mockContext.getSyncStockConfig()).thenReturn(null);
        
        String result = operation.getUpOrDownStatus(BigDecimal.valueOf(100));
        
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试自动上架启用 - 正库存
     */
    @Test
    public void testGetUpOrDownStatus_AutoShelvesEnabled_PositiveStock() {
        when(mockSyncStockConfig.getIsSyncMoreZeroAutoShelves()).thenReturn(true);
        
        String result = operation.getUpOrDownStatus(BigDecimal.valueOf(100));
        
        assertEquals("up", result);
    }

    /**
     * 测试自动上架启用 - 零库存
     */
    @Test
    public void testGetUpOrDownStatus_AutoShelvesEnabled_ZeroStock() {
        when(mockSyncStockConfig.getIsSyncMoreZeroAutoShelves()).thenReturn(true);
        
        String result = operation.getUpOrDownStatus(BigDecimal.ZERO);
        
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试自动上架启用 - 负库存
     */
    @Test
    public void testGetUpOrDownStatus_AutoShelvesEnabled_NegativeStock() {
        when(mockSyncStockConfig.getIsSyncMoreZeroAutoShelves()).thenReturn(true);
        
        String result = operation.getUpOrDownStatus(BigDecimal.valueOf(-10));
        
        assertEquals(StringUtils.EMPTY, result);
    }

    /**
     * 测试自动上架禁用
     */
    @Test
    public void testGetUpOrDownStatus_AutoShelvesDisabled() {
        when(mockSyncStockConfig.getIsSyncMoreZeroAutoShelves()).thenReturn(false);
        
        String result1 = operation.getUpOrDownStatus(BigDecimal.valueOf(100));
        String result2 = operation.getUpOrDownStatus(BigDecimal.ZERO);
        String result3 = operation.getUpOrDownStatus(BigDecimal.valueOf(-10));
        
        assertEquals(StringUtils.EMPTY, result1);
        assertEquals(StringUtils.EMPTY, result2);
        assertEquals(StringUtils.EMPTY, result3);
    }

    /**
     * 测试空数量
     */
    @Test
    public void testGetUpOrDownStatus_NullQuantity() {
        when(mockSyncStockConfig.getIsSyncMoreZeroAutoShelves()).thenReturn(true);
        
        // 这里应该抛出异常或有特定处理逻辑
        assertThrows(NullPointerException.class, () -> {
            operation.getUpOrDownStatus(null);
        });
    }
    /**
     * 测试边界值
     */
    @Test
    public void testGetUpOrDownStatus_EdgeCaseValues() {
        when(mockSyncStockConfig.getIsSyncMoreZeroAutoShelves()).thenReturn(true);
        
        // 测试非常小的正数
        String result1 = operation.getUpOrDownStatus(new BigDecimal("0.01"));
        assertEquals("up", result1);
        
        // 测试非常小的负数
        String result2 = operation.getUpOrDownStatus(new BigDecimal("-0.01"));
        assertEquals(StringUtils.EMPTY, result2);
        
        // 测试大数值
        String result3 = operation.getUpOrDownStatus(new BigDecimal("999999999"));
        assertEquals("up", result3);
    }

    /**
     * 测试完整流程集成
     */
    @Test
    public void testIntegration_CompleteFlow() {
        // 设置自动上架
        when(mockSyncStockConfig.getIsSyncMoreZeroAutoShelves()).thenReturn(true);
        
        GoodsMatchEnhance matchEnhance = createTestMatchEnhance();
        GoodsStockCalculationResult goodsStockInfo = createTestGoodsStockInfo();
        
        // 执行完整流程
        StockSyncGoodsRequestPair result = operation.formatGoodsRequestPair(matchEnhance, goodsStockInfo);
        
        // 验证完整结果
        assertNotNull(result);
        assertNotNull(result.getMatchEnhance());
        assertNotNull(result.getGoodsInfo());
        
        BusinessBatchSyncStockRequestGoodInfo goodsRequest = result.getGoodsInfo();
        assertEquals("up", goodsRequest.getStatus()); // 库存>0应该上架
        assertEquals(100, goodsRequest.getQuantity().intValue());
        assertEquals(SyncStockTypeEnum.Whole.getCode(), goodsRequest.getSyncStockType());
    }

    /**
     * 测试零库存自动下架集成
     */
    @Test
    public void testIntegration_ZeroStockWithAutoShelves() {
        when(mockSyncStockConfig.getIsSyncMoreZeroAutoShelves()).thenReturn(true);
        
        GoodsMatchEnhance matchEnhance = createTestMatchEnhance();
        GoodsStockCalculationResult goodsStockInfo = GoodsStockCalculationResult.success(BigDecimal.ZERO, new HashMap<>());
        
        // 执行测试
        StockSyncGoodsRequestPair result = operation.formatGoodsRequestPair(matchEnhance, goodsStockInfo);
        
        // 验证结果
        assertNotNull(result);
        BusinessBatchSyncStockRequestGoodInfo goodsRequest = result.getGoodsInfo();
        assertEquals(StringUtils.EMPTY, goodsRequest.getStatus()); // 库存=0应该下架
        assertEquals(0, goodsRequest.getQuantity().intValue());
    }

    //region 私有方法
    /**
     * 创建测试上下文
     */
    private StockSyncContext createTestContext() {
        StockSyncContext context = new StockSyncContext();
        context.setVipUser("testUser");
        context.setShopId(12345);
        context.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        
        ApiShopBaseDto shopBase = new ApiShopBaseDto();
        shopBase.setShopId(12345);
        shopBase.setShopName("测试店铺");
        shopBase.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        context.setShopBase(shopBase);
        
        SyncStockShopConfig syncConfig = new SyncStockShopConfig();
        syncConfig.setIsSyncMoreZeroAutoShelves(true);
        context.setSyncStockConfig(syncConfig);
        
        return context;
    }

    /**
     * 创建测试匹配数据
     */
    private GoodsMatchEnhance createTestMatchEnhance() {
        ApiSysMatchDO sysMatch = new ApiSysMatchDO();
        sysMatch.settBOuterID("TEST_OUTER_ID");
        sysMatch.setSkuOuterID("TEST_SKU_OUTER_ID");
        sysMatch.setNumiid("12345");
        sysMatch.setSkuID("67890");
        sysMatch.setbTBGoods(1); // 设置平台类型
        
        return GoodsMatchEnhance.create(sysMatch, "MULTI_SIGN_TEST");
    }

    /**
     * 创建测试商品库存信息
     */
    private GoodsStockCalculationResult createTestGoodsStockInfo() {
        return GoodsStockCalculationResult.success(BigDecimal.valueOf(100), BigDecimal.valueOf(100), new HashMap<>());
    }
    //endregion
}
