package com.differ.wdgj.api.user.biz.utils;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.AfterSalesShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shop.ShopTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiAfterSaleTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.utils.shop.AfterSalesShopConfigUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * 售后店铺配置工具类
 *
 * <AUTHOR>
 * @date 2024/12/3 下午7:34
 */
@Ignore
public class AfterSalesShopConfigUtilsTest extends AbstractSpringTest{
    /**
     * 根据店铺id获取售后店铺配置
     */
    @Test
    public void singleByShopIdTest(){
        AfterSalesShopConfig afterSalesShopConfig = AfterSalesShopConfigUtils.singleByShopId("api2017", 1382);
        Assert.assertNotNull(afterSalesShopConfig);
    }

    /**
     * 校验订单售后类型是否在店铺配置中
     */
    @Test
    public void checkShopTypeConfigTest() {
        List<AfterSalesShopConfig.TypeItem> bizShopOrderTypesOne = new ArrayList<>();
        bizShopOrderTypesOne.add(createTypeItem(ApiAfterSaleTypeEnum.REFUND));
        bizShopOrderTypesOne.add(createTypeItem(ApiAfterSaleTypeEnum.REFUND_PAY));

        ApiAfterSaleTypeEnum refundAll = ApiAfterSaleTypeEnum.REFUND_ALL;
        boolean oneResult = AfterSalesShopConfigUtils.checkShopTypeConfig(refundAll, bizShopOrderTypesOne);
        Assert.assertTrue(oneResult);

        ApiAfterSaleTypeEnum refundPayTransit = ApiAfterSaleTypeEnum.REFUND_PAY_TRANSIT;
        boolean twoResult = AfterSalesShopConfigUtils.checkShopTypeConfig(refundPayTransit, bizShopOrderTypesOne);
        Assert.assertTrue(twoResult);

        ApiAfterSaleTypeEnum refundBj = ApiAfterSaleTypeEnum.REFUND_BJ;
        boolean threeResult = AfterSalesShopConfigUtils.checkShopTypeConfig(refundBj, bizShopOrderTypesOne);
        Assert.assertFalse(threeResult);
    }

    //region 私有方法
    /**
     * 创建售后店铺配置项
     *
     * @param afterSaleType 售后类型
     * @return 售后店铺配置项
     */
    private AfterSalesShopConfig.TypeItem createTypeItem(ApiAfterSaleTypeEnum afterSaleType) {
        AfterSalesShopConfig.TypeItem typeItem = new AfterSalesShopConfig.TypeItem();
        typeItem.setBizType(ShopTypeEnum.DEFAULT.getCode());
        typeItem.setBizValue(afterSaleType.getCode());
        return typeItem;
    }
    //endregion
}
