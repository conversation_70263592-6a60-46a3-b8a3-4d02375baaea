package com.differ.wdgj.api.user.biz.domain.stock.notice.handler;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.ErpStockNoticeChangeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockFlagEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.notice.ApiSysMatchNotice;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.notice.erp.processor.handler.filter.DegradeStockNoticeFilter;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchExtDO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.time.LocalDateTime;

/**
 * 匹配业务降级过滤
 *
 * <AUTHOR>
 * @date 2025/3/13 下午2:57
 */
@Ignore
public class DegradeStockNoticeFilterTest extends AbstractSpringTest {
    //region 常量
    /**
     * 待测试的过滤器实例
     */
    private DegradeStockNoticeFilter filter;

    /**
     * 测试数据 - 库存同步上下文
     */
    private StockSyncContext context;

    /**
     * 测试数据 - 系统匹配通知
     */
    private ApiSysMatchNotice notice;

    /**
     * 测试数据 - 匹配信息
     */
    private ApiSysMatchDO matchDO;

    /**
     * 测试数据 - 匹配信息扩展
     */
    private ApiSysMatchExtDO extDO;
    //endregion

    //region 初始化
    /**
     * 每个测试方法执行前的初始化工作
     */
    @Before
    public void setUp() throws Exception {

        // 初始化过滤器
        filter = new DegradeStockNoticeFilter();

        // 初始化测试数据
        initTestData();
    }

    /**
     * 初始化测试数据
     */
    private void initTestData() {
        // 初始化上下文
        context = new StockSyncContext();

        // 初始化匹配信息
        matchDO = new ApiSysMatchDO();
        matchDO.setId(1);

        // 初始化匹配信息扩展
        extDO = new ApiSysMatchExtDO();
        extDO.setIncreFlag(SyncStockFlagEnum.Whole.getValue());
        extDO.setNextResetSyncTime(LocalDateTime.now().plusHours(1));
        matchDO.setExtEntity(extDO);

        // 初始化系统匹配通知
        notice = new ApiSysMatchNotice();
        notice.setPlatGoodsMatch(matchDO);
        notice.setChangeSource(ErpStockNoticeChangeEnum.WAREHOUSING.getValue().toString());
    }
    //endregion

    /**
     * 测试正常流程
     * 预期：所有配置正确时，返回成功
     */
    @Test
    public void testProcessSuccess() {
        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertTrue("正常流程应该返回成功", result.getSuccess());
    }

    /**
     * 测试增量限制且为入库操作场景
     * 预期：当nextResetSyncTime未到时，返回失败
     */
    @Test
    public void testProcessWithIncrementAndInStock() {
        // 设置增量标识
        extDO.setIncreFlag(SyncStockFlagEnum.Increment.getValue());
        // 设置为入库操作
        notice.setChangeSource(ErpStockNoticeChangeEnum.WAREHOUSING.getValue().toString());
        // 设置未来时间
        extDO.setNextResetSyncTime(LocalDateTime.now().plusHours(1));

        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertFalse("增量限制且未到重置时间应该返回失败", result.getSuccess());
        Assert.assertTrue("错误信息应包含限制说明",
                result.getMessage().contains("限制增量库存"));
    }

    /**
     * 测试限制同步场景
     * 预期：当nextResetSyncTime未到时，返回失败
     */
    @Test
    public void testProcessWithLimitSync() {
        // 设置限制标识
        extDO.setIncreFlag(SyncStockFlagEnum.Limit.getValue());
        // 设置未来时间
        extDO.setNextResetSyncTime(LocalDateTime.now().plusHours(1));

        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertFalse("限制同步且未到重置时间应该返回失败", result.getSuccess());
        Assert.assertTrue("错误信息应包含限制说明",
                result.getMessage().contains("已限制"));
    }

    /**
     * 测试禁止同步场景
     * 预期：当nextResetSyncTime未到时，返回失败
     */
    @Test
    public void testProcessWithForbidSync() {
        // 设置禁止标识
        extDO.setIncreFlag(SyncStockFlagEnum.Forbid.getValue());
        // 设置未来时间
        extDO.setNextResetSyncTime(LocalDateTime.now().plusHours(1));

        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertFalse("禁止同步且未到重置时间应该返回失败", result.getSuccess());
        Assert.assertTrue("错误信息应包含禁止说明",
                result.getMessage().contains("已禁止"));
    }

    /**
     * 测试重置时间已过期场景
     * 预期：当nextResetSyncTime已过期时，返回成功
     */
    @Test
    public void testProcessWithExpiredResetTime() {
        // 设置限制标识
        extDO.setIncreFlag(SyncStockFlagEnum.Limit.getValue());
        // 设置过去时间
        extDO.setNextResetSyncTime(LocalDateTime.now().minusHours(1));

        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertTrue("重置时间已过期应该返回成功", result.getSuccess());
    }

    /**
     * 测试无扩展信息场景
     * 预期：当没有扩展信息时，返回成功
     */
    @Test
    public void testProcessWithoutExtEntity() {
        matchDO.setExtEntity(null);

        StockContentResult<?> result = filter.process(context, notice);
        Assert.assertTrue("无扩展信息应该返回成功", result.getSuccess());
    }
}
