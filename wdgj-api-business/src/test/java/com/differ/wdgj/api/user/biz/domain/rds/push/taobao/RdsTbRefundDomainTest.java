package com.differ.wdgj.api.user.biz.domain.rds.push.taobao;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.RdsTbRefundOutRequest;
import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.TbAfterSaleRdsPageQueryResponse;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.rdspush.TbRefundRdsDo;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.TbRefundRdsMapper;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 淘宝Rds - 退货退款相关操作（jdp_tb_refund）集成测试
 *
 * <AUTHOR>
 * @date 2025/4/10 下午1:39
 */
@Ignore
public class RdsTbRefundDomainTest extends AbstractSpringTest {

    @Autowired
    private TbRefundRdsMapper tbRefundRdsMapper;

    private RdsTbRefundDomain rdsTbRefundDomain;
    private String memberName = "api2017";

    @Before
    public void setUp() {
        rdsTbRefundDomain = new RdsTbRefundDomain(memberName);
    }

    /**
     * 测试查询淘宝退货退款单总数
     */
    @Test
    public void testQueryPolyRefundOrderCount() {
        // 创建请求参数
        RdsTbRefundOutRequest request = new RdsTbRefundOutRequest();
        request.setStartUpdateTime(LocalDateTime.now().minusDays(7));
        request.setEndUpdateTime(LocalDateTime.now());
        request.setSellNick("单元测试专用");

        // 直接调用数据库查询总数
        int count = DBSwitchUtil.doDBWithRds(memberName, () -> 
            tbRefundRdsMapper.getRefundsCountByModified(
                request.getStartUpdateTime(), 
                request.getEndUpdateTime(), 
                request.getSellNick()
            )
        );

        // 通过领域对象查询总数
        int domainCount = rdsTbRefundDomain.queryPolyRefundOrderCount(request);

        // 验证结果
        Assert.assertEquals("直接查询和领域对象查询结果应该一致", count, domainCount);
    }

    /**
     * 测试查询淘宝退货退款单（转换为菠萝派对象）
     */
    @Test
    public void testQueryPolyRefundOrder() {
        // 创建请求参数
        RdsTbRefundOutRequest request = new RdsTbRefundOutRequest();
        request.setStartUpdateTime(LocalDateTime.now().minusDays(7));
        request.setEndUpdateTime(LocalDateTime.now());
        request.setSellNick("单元测试专用");
        request.setPageSize(10);
        request.setPageIndex(1);

        // 直接调用数据库查询数据
        List<TbRefundRdsDo> refundRdsDos = DBSwitchUtil.doDBWithRds(memberName, () -> 
            tbRefundRdsMapper.getRDSRefundOrderDataByModified(
                request.getStartUpdateTime(), 
                request.getEndUpdateTime(), 
                request.getSellNick(),
                (request.getPageIndex() - 1) * request.getPageSize(),
                request.getPageSize()
            )
        );

        // 通过领域对象查询数据
        TbAfterSaleRdsPageQueryResponse response = rdsTbRefundDomain.queryPolyRefundOrder(request);

        // 验证结果
        Assert.assertNotNull("响应不应为空", response);
        Assert.assertNotNull("退款列表不应为空", response.getRefunds());
        
        // 如果数据库中有数据，则验证转换后的数据
        if (!refundRdsDos.isEmpty()) {
            Assert.assertTrue("转换后的退款列表不应为空", CollectionUtils.isNotEmpty(response.getRefunds()));
        }
    }

    /**
     * 测试查询淘宝退货退款单 - 空请求
     */
    @Test
    public void testQueryPolyRefundOrderWithNullRequest() {
        // 测试空请求
        TbAfterSaleRdsPageQueryResponse response = rdsTbRefundDomain.queryPolyRefundOrder(null);
        Assert.assertNotNull("响应不应为空", response);
        Assert.assertNotNull("退款列表不应为空", response.getRefunds());
        Assert.assertEquals("空请求应返回空列表", 0, response.getRefunds().size());
    }

    /**
     * 测试查询淘宝退货退款单总数 - 空请求
     */
    @Test
    public void testQueryPolyRefundOrderCountWithNullRequest() {
        // 测试空请求
        int count = rdsTbRefundDomain.queryPolyRefundOrderCount(null);
        Assert.assertEquals("空请求应返回0", 0, count);
    }

    /**
     * 测试查询淘宝退货退款单 - 分页参数
     */
    @Test
    public void testQueryPolyRefundOrderWithPagination() {
        // 创建请求参数
        RdsTbRefundOutRequest request = new RdsTbRefundOutRequest();
        request.setStartUpdateTime(LocalDateTime.now().minusDays(7));
        request.setEndUpdateTime(LocalDateTime.now());
        request.setSellNick("testSeller");
        request.setPageSize(5);
        request.setPageIndex(1);

        // 第一页
        TbAfterSaleRdsPageQueryResponse response1 = rdsTbRefundDomain.queryPolyRefundOrder(request);
        
        // 第二页
        request.setPageIndex(2);
        TbAfterSaleRdsPageQueryResponse response2 = rdsTbRefundDomain.queryPolyRefundOrder(request);

        // 验证结果
        Assert.assertNotNull("第一页响应不应为空", response1);
        Assert.assertNotNull("第二页响应不应为空", response2);
        
        // 如果数据库中有数据，则验证分页结果
        if (response1.getRefunds() != null && !response1.getRefunds().isEmpty()) {
            Assert.assertTrue("第一页数据不应为空", response1.getRefunds().size() > 0);
            Assert.assertTrue("第一页数据不应超过页大小", response1.getRefunds().size() <= request.getPageSize());
        }
    }
}
