package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchExtDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Collections;

/**
 * api商品匹配扩展仓储
 * <AUTHOR>
 * @date 2024-03-19 20:14
 */
@Ignore
public class ApiSysMatchExtMapperTest extends AbstractSpringTest {

    @Autowired
    ApiSysMatchExtMapper mapper;

    /**
     * 批量新增或更新api商品匹配扩展
     */
    @Test
    public void addOrUpdateGoodsMatchExtraTest(){
        ApiSysMatchExtDO apiSysMatchExtDO = new ApiSysMatchExtDO();
        apiSysMatchExtDO.setId(1);
        apiSysMatchExtDO.setRestrictedMode(null);
        apiSysMatchExtDO.setIncreFlag(1);
        apiSysMatchExtDO.setNextResetSyncTime(LocalDateTime.now());
        DBSwitchUtil.doDBWithUser("api2017", () -> mapper.addOrUpdateGoodsMatchExtra(Collections.singletonList(apiSysMatchExtDO)));
    }

    /**
     * 批量新增或更新api商品匹配扩展
     */
    @Test
    public void doTransactionTest(){

        ApiSysMatchExtDO apiSysMatchExtDO = new ApiSysMatchExtDO();
        apiSysMatchExtDO.setId(1);
        apiSysMatchExtDO.setRestrictedMode(null);
        apiSysMatchExtDO.setIncreFlag(1);
        apiSysMatchExtDO.setNextResetSyncTime(LocalDateTime.now());
        DBSwitchUtil.doTransaction("api2017", () -> {
            mapper.addOrUpdateGoodsMatchExtra(Collections.singletonList(apiSysMatchExtDO));
            apiSysMatchExtDO.setId(2);
            apiSysMatchExtDO.getJsonParams().getBytes(StandardCharsets.UTF_8);
            mapper.addOrUpdateGoodsMatchExtra(Collections.singletonList(apiSysMatchExtDO));
            return true;
        });
    }
}
