package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.core;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyExchangeStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyRefundGoodsStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseExchangeGoodInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeResponseRefundGoodInfo;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * BusinessGetExchangeOrderResponseOrderItem 随机数据生成器
 * 用于生成符合电商业务场景的换货单测试数据
 *
 * <AUTHOR>
 * @date 2024/12/19 下午10:00
 */
public class BusinessGetExchangeOrderResponseOrderItemGenerator {

    //region 常量定义

    /**
     * 随机数生成器
     */
    private static final Random RANDOM = new Random();

    /**
     * 日期时间格式化器
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /**
     * 买家昵称前缀池
     */
    private static final String[] BUYER_NICK_PREFIXES = {
        "tb_", "user_", "buyer_", "客户_", "用户_", "买家_", "会员_"
    };

    /**
     * 卖家昵称池
     */
    private static final String[] SELLER_NICKS = {
        "官方旗舰店", "品牌专营店", "优选商城", "精品小店", "全球购", "海外直营",
        "数码专营", "服装旗舰", "美妆专柜", "母婴用品", "家居生活", "运动户外"
    };

    /**
     * 商品名称池
     */
    private static final String[] PRODUCT_NAMES = {
        "iPhone 15 Pro Max 256GB", "华为Mate60 Pro", "小米14 Ultra", "OPPO Find X7",
        "Nike Air Jordan 1", "Adidas Ultra Boost", "New Balance 990v5", "Converse Chuck Taylor",
        "雅诗兰黛小棕瓶精华", "兰蔻小黑瓶精华", "SK-II神仙水", "资生堂红腰子精华",
        "戴森V15吸尘器", "小米扫地机器人", "美的空气炸锅", "九阳豆浆机",
        "优衣库羽绒服", "ZARA连衣裙", "H&M毛衣", "GAP牛仔裤"
    };

    /**
     * SKU规格池
     */
    private static final String[] SKU_SPECS = {
        "颜色:黑色;尺寸:XL", "颜色:白色;尺寸:L", "颜色:红色;尺寸:M", "颜色:蓝色;尺寸:S",
        "容量:256GB;颜色:深空灰", "容量:128GB;颜色:银色", "容量:512GB;颜色:金色",
        "尺码:42;颜色:黑白", "尺码:40;颜色:全白", "尺码:44;颜色:红黑"
    };

    /**
     * 换货原因池
     */
    private static final String[] EXCHANGE_REASONS = {
        "尺寸不合适", "颜色不喜欢", "款式不满意", "质量问题",
        "收到商品有瑕疵", "与描述不符", "功能不符合预期", "包装破损",
        "发错颜色", "发错尺寸", "商品有划痕", "材质不满意"
    };

    /**
     * 物流公司池
     */
    private static final String[] LOGISTICS_COMPANIES = {
        "顺丰速运", "圆通快递", "中通快递", "申通快递", "韵达快递",
        "百世快递", "德邦快递", "京东物流", "菜鸟网络", "邮政EMS"
    };

    /**
     * 省份池
     */
    private static final String[] PROVINCES = {
        "北京市", "上海市", "广东省", "浙江省", "江苏省", "山东省",
        "河南省", "四川省", "湖北省", "湖南省", "福建省", "安徽省"
    };

    /**
     * 城市池
     */
    private static final String[] CITIES = {
        "北京市", "上海市", "广州市", "深圳市", "杭州市", "南京市",
        "济南市", "郑州市", "成都市", "武汉市", "长沙市", "福州市"
    };

    /**
     * 区域池
     */
    private static final String[] AREAS = {
        "朝阳区", "海淀区", "浦东新区", "黄浦区", "天河区", "福田区",
        "西湖区", "拱墅区", "玄武区", "鼓楼区", "历下区", "市中区"
    };

    /**
     * 买家姓名池
     */
    private static final String[] BUYER_NAMES = {
        "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十",
        "陈一", "刘二", "杨三", "黄四", "朱五", "林六", "何七", "郭八"
    };

    //endregion

    //region 公共方法

    /**
     * 生成随机的BusinessGetExchangeOrderResponseOrderItem
     * 
     * @return 随机生成的换货单对象
     */
    public static BusinessGetExchangeOrderResponseOrderItem generateRandom() {
        return generateRandom(null);
    }

    /**
     * 生成随机的BusinessGetExchangeOrderResponseOrderItem
     * 如果传入的template不为空，则不为空的属性不会被随机生成
     * 
     * @param template 模板对象，不为空的属性将被保留
     * @return 随机生成的换货单对象
     */
    public static BusinessGetExchangeOrderResponseOrderItem generateRandom(BusinessGetExchangeOrderResponseOrderItem template) {
        BusinessGetExchangeOrderResponseOrderItem item = new BusinessGetExchangeOrderResponseOrderItem();

        // 必定随机生成的字段
        item.setExchangeOrderNo(generateRandomExchangeOrderNo());
        item.setPlatOrderNo(generateRandomPlatOrderNo());

        // 基础信息
        setIfNull(item::setRefundAmount, item.getRefundAmount(), template, BusinessGetExchangeOrderResponseOrderItem::getRefundAmount, 
                () -> generateRandomAmount(10, 2000));
        setIfNull(item::setPayment, item.getPayment(), template, BusinessGetExchangeOrderResponseOrderItem::getPayment,
                () -> generateRandomAmount(50, 5000));

        // 买家信息
        setIfNull(item::setBuyerNick, item.getBuyerNick(), template, BusinessGetExchangeOrderResponseOrderItem::getBuyerNick,
                () -> generateRandomBuyerNick());
        setIfNull(item::setBuyerName, item.getBuyerName(), template, BusinessGetExchangeOrderResponseOrderItem::getBuyerName,
                () -> getRandomElement(BUYER_NAMES));
        setIfNull(item::setBuyerPhone, item.getBuyerPhone(), template, BusinessGetExchangeOrderResponseOrderItem::getBuyerPhone,
                () -> generateRandomPhone());

        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime createTime = now.minusDays(RANDOM.nextInt(30)).minusHours(RANDOM.nextInt(24));
        LocalDateTime updateTime = createTime.plusHours(RANDOM.nextInt(48));
        
        setIfNull(item::setCreateTime, item.getCreateTime(), template, BusinessGetExchangeOrderResponseOrderItem::getCreateTime,
                () -> createTime);
        setIfNull(item::setUpdateTime, item.getUpdateTime(), template, BusinessGetExchangeOrderResponseOrderItem::getUpdateTime,
                () -> updateTime);
        setIfNull(item::setRdsCreateTime, item.getRdsCreateTime(), template, BusinessGetExchangeOrderResponseOrderItem::getRdsCreateTime,
                () -> createTime.plusMinutes(RANDOM.nextInt(60)));
        setIfNull(item::setRdsModifyTime, item.getRdsModifyTime(), template, BusinessGetExchangeOrderResponseOrderItem::getRdsModifyTime,
                () -> updateTime.plusMinutes(RANDOM.nextInt(60)));

        // 订单信息
        setIfNull(item::setPlatSubOrderNo, item.getPlatSubOrderNo(), template, BusinessGetExchangeOrderResponseOrderItem::getPlatSubOrderNo,
                () -> item.getPlatOrderNo() + "-" + (RANDOM.nextInt(9) + 1));

        // 换货状态
        PolyExchangeStatusEnum exchangeStatus = getRandomEnum(PolyExchangeStatusEnum.values(), 
                PolyExchangeStatusEnum.JH_06, PolyExchangeStatusEnum.JH_02, PolyExchangeStatusEnum.JH_03);
        setIfNull(item::setOrderStatus, item.getOrderStatus(), template, BusinessGetExchangeOrderResponseOrderItem::getOrderStatus,
                () -> exchangeStatus.getCode());

        // 换货原因和描述
        String reason = getRandomElement(EXCHANGE_REASONS);
        setIfNull(item::setReason, item.getReason(), template, BusinessGetExchangeOrderResponseOrderItem::getReason,
                () -> reason);
        setIfNull(item::setDesc, item.getDesc(), template, BusinessGetExchangeOrderResponseOrderItem::getDesc,
                () -> "客户申请换货：" + reason + "，请及时处理。");

        // 物流信息
        generateLogisticsInfo(item, template);

        // 地址信息
        generateAddressInfo(item, template);

        // 卖家信息
        setIfNull(item::setSellerNick, item.getSellerNick(), template, BusinessGetExchangeOrderResponseOrderItem::getSellerNick,
                () -> getRandomElement(SELLER_NICKS));

        // 换货商品明细
        setIfNull(item::setRefundGoods, item.getRefundGoods(), template, BusinessGetExchangeOrderResponseOrderItem::getRefundGoods,
                () -> generateRefundGoods(item));
        setIfNull(item::setExchangeGoods, item.getExchangeGoods(), template, BusinessGetExchangeOrderResponseOrderItem::getExchangeGoods,
                () -> generateExchangeGoods(item));

        // 扩展字段
        setIfNull(item::setIsSuccess, item.getIsSuccess(), template, BusinessGetExchangeOrderResponseOrderItem::getIsSuccess,
                () -> "true");
        setIfNull(item::setSubCode, item.getSubCode(), template, BusinessGetExchangeOrderResponseOrderItem::getSubCode,
                () -> "SUCCESS");
        setIfNull(item::setSubMessage, item.getSubMessage(), template, BusinessGetExchangeOrderResponseOrderItem::getSubMessage,
                () -> "操作成功");
        setIfNull(item::setOaid, item.getOaid(), template, BusinessGetExchangeOrderResponseOrderItem::getOaid,
                () -> generateRandomOaid());
        setIfNull(item::setNewExchangeRepair, item.getNewExchangeRepair(), template, BusinessGetExchangeOrderResponseOrderItem::getNewExchangeRepair,
                () -> RANDOM.nextBoolean() ? "1" : "0");

        return item;
    }

    //endregion

    //region 私有方法

    /**
     * 生成随机换货单号
     * 格式：EX + yyyyMMddHHmmss + 4位随机数
     */
    private static String generateRandomExchangeOrderNo() {
        String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        int randomSuffix = RANDOM.nextInt(9000) + 1000;
        return "EX" + timestamp + randomSuffix;
    }

    /**
     * 生成随机平台订单号
     * 格式：PO + yyyyMMddHHmmss + 4位随机数
     */
    private static String generateRandomPlatOrderNo() {
        String timestamp = LocalDateTime.now().format(DATE_TIME_FORMATTER);
        int randomSuffix = RANDOM.nextInt(9000) + 1000;
        return "PO" + timestamp + randomSuffix;
    }

    /**
     * 生成随机金额
     */
    private static BigDecimal generateRandomAmount(double min, double max) {
        double amount = min + (max - min) * RANDOM.nextDouble();
        return BigDecimal.valueOf(amount).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 生成随机买家昵称
     */
    private static String generateRandomBuyerNick() {
        String prefix = getRandomElement(BUYER_NICK_PREFIXES);
        String suffix = String.valueOf(RANDOM.nextInt(900000) + 100000);
        return prefix + suffix;
    }

    /**
     * 生成随机手机号
     */
    private static String generateRandomPhone() {
        String[] prefixes = {"138", "139", "150", "151", "152", "158", "159", "188", "189"};
        String prefix = getRandomElement(prefixes);
        StringBuilder phone = new StringBuilder(prefix);
        for (int i = 0; i < 8; i++) {
            phone.append(RANDOM.nextInt(10));
        }
        return phone.toString();
    }

    /**
     * 生成随机物流单号
     */
    private static String generateRandomLogisticsNo() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 12; i++) {
            if (RANDOM.nextBoolean()) {
                sb.append((char) ('A' + RANDOM.nextInt(26)));
            } else {
                sb.append(RANDOM.nextInt(10));
            }
        }
        return sb.toString();
    }

    /**
     * 生成随机OAID
     */
    private static String generateRandomOaid() {
        return "oaid_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * 生成物流信息
     */
    private static void generateLogisticsInfo(BusinessGetExchangeOrderResponseOrderItem item, BusinessGetExchangeOrderResponseOrderItem template) {
        // 买家发货物流信息
        String buyerLogisticCompany = getRandomElement(LOGISTICS_COMPANIES);
        setIfNull(item::setBuyerLogisticName, item.getBuyerLogisticName(), template, BusinessGetExchangeOrderResponseOrderItem::getBuyerLogisticName,
                () -> buyerLogisticCompany);
        setIfNull(item::setBuyerLogisticNo, item.getBuyerLogisticNo(), template, BusinessGetExchangeOrderResponseOrderItem::getBuyerLogisticNo,
                () -> generateRandomLogisticsNo());

        // 卖家发货物流信息
        String sellerLogisticCompany = getRandomElement(LOGISTICS_COMPANIES);
        setIfNull(item::setSellerLogisticName, item.getSellerLogisticName(), template, BusinessGetExchangeOrderResponseOrderItem::getSellerLogisticName,
                () -> sellerLogisticCompany);
        setIfNull(item::setSellerLogisticNo, item.getSellerLogisticNo(), template, BusinessGetExchangeOrderResponseOrderItem::getSellerLogisticNo,
                () -> generateRandomLogisticsNo());

        // 通用物流单号（通常是买家发货的物流单号）
        setIfNull(item::setLogisticNo, item.getLogisticNo(), template, BusinessGetExchangeOrderResponseOrderItem::getLogisticNo,
                () -> item.getBuyerLogisticNo());
    }

    /**
     * 生成地址信息
     */
    private static void generateAddressInfo(BusinessGetExchangeOrderResponseOrderItem item, BusinessGetExchangeOrderResponseOrderItem template) {
        String province = getRandomElement(PROVINCES);
        String city = getRandomElement(CITIES);
        String area = getRandomElement(AREAS);
        String town = area + "街道";
        String detailAddress = "某某小区" + (RANDOM.nextInt(999) + 1) + "号楼" + (RANDOM.nextInt(99) + 1) + "单元" + (RANDOM.nextInt(999) + 100) + "室";

        // 买家收货地址
        setIfNull(item::setProvince, item.getProvince(), template, BusinessGetExchangeOrderResponseOrderItem::getProvince,
                () -> province);
        setIfNull(item::setCity, item.getCity(), template, BusinessGetExchangeOrderResponseOrderItem::getCity,
                () -> city);
        setIfNull(item::setArea, item.getArea(), template, BusinessGetExchangeOrderResponseOrderItem::getArea,
                () -> area);
        setIfNull(item::setTown, item.getTown(), template, BusinessGetExchangeOrderResponseOrderItem::getTown,
                () -> town);
        setIfNull(item::setAddress, item.getAddress(), template, BusinessGetExchangeOrderResponseOrderItem::getAddress,
                () -> province + city + area + town + detailAddress);
        setIfNull(item::setBuyerAddress, item.getBuyerAddress(), template, BusinessGetExchangeOrderResponseOrderItem::getBuyerAddress,
                () -> item.getAddress());

        // 卖家收货地址
        String sellerProvince = getRandomElement(PROVINCES);
        String sellerCity = getRandomElement(CITIES);
        String sellerArea = getRandomElement(AREAS);
        setIfNull(item::setSellerAddress, item.getSellerAddress(), template, BusinessGetExchangeOrderResponseOrderItem::getSellerAddress,
                () -> sellerProvince + sellerCity + sellerArea + "商家收货地址" + (RANDOM.nextInt(99) + 1) + "号");
        setIfNull(item::setSellerReceiveAddressId, item.getSellerReceiveAddressId(), template, BusinessGetExchangeOrderResponseOrderItem::getSellerReceiveAddressId,
                () -> "ADDR_" + (RANDOM.nextInt(90000) + 10000));
    }

    /**
     * 生成退回商品明细
     */
    private static List<BusinessGetExchangeResponseRefundGoodInfo> generateRefundGoods(BusinessGetExchangeOrderResponseOrderItem item) {
        List<BusinessGetExchangeResponseRefundGoodInfo> refundGoods = new ArrayList<>();
        
        // 生成1-2个退回商品
        int goodsCount = RANDOM.nextInt(2) + 1;
        for (int i = 0; i < goodsCount; i++) {
            BusinessGetExchangeResponseRefundGoodInfo goodInfo = new BusinessGetExchangeResponseRefundGoodInfo();
            
            goodInfo.setPlatProductId("PROD_" + (RANDOM.nextInt(900000) + 100000) + "_" + (i + 1));
            goodInfo.setSku(getRandomElement(SKU_SPECS));
            goodInfo.setOuterId("OUTER_" + (RANDOM.nextInt(90000) + 10000));
            goodInfo.setOutSkuId("OUTER_SKU_" + (RANDOM.nextInt(90000) + 10000));
            goodInfo.setSkuSpec(goodInfo.getSku());
            goodInfo.setProductName(getRandomElement(PRODUCT_NAMES));
            goodInfo.setRefundAmount(generateRandomAmount(10, 500));
            goodInfo.setPrice(generateRandomAmount(50, 1000));
            goodInfo.setProductNum(RANDOM.nextInt(3) + 1);
            goodInfo.setRefundProductNum(goodInfo.getProductNum());
            goodInfo.setSubTradeNo(item.getPlatSubOrderNo() + "_" + (i + 1));
            goodInfo.setGoodsStatus(getRandomEnum(PolyRefundGoodsStatusEnum.values()).getCode());
            goodInfo.setGoodsStatusDesc(getRandomEnum(PolyRefundGoodsStatusEnum.values()).getDescription());
            
            refundGoods.add(goodInfo);
        }
        
        return refundGoods;
    }

    /**
     * 生成换出商品明细
     */
    private static List<BusinessGetExchangeResponseExchangeGoodInfo> generateExchangeGoods(BusinessGetExchangeOrderResponseOrderItem item) {
        List<BusinessGetExchangeResponseExchangeGoodInfo> exchangeGoods = new ArrayList<>();
        
        // 生成1-2个换出商品
        int goodsCount = RANDOM.nextInt(2) + 1;
        for (int i = 0; i < goodsCount; i++) {
            BusinessGetExchangeResponseExchangeGoodInfo goodInfo = new BusinessGetExchangeResponseExchangeGoodInfo();
            
            goodInfo.setPlatProductId("PROD_" + (RANDOM.nextInt(900000) + 100000) + "_EX_" + (i + 1));
            goodInfo.setSku(getRandomElement(SKU_SPECS));
            goodInfo.setOuterId("OUTER_EX_" + (RANDOM.nextInt(90000) + 10000));
            goodInfo.setOutSkuId("OUTER_SKU_EX_" + (RANDOM.nextInt(90000) + 10000));
            goodInfo.setSkuSpec(goodInfo.getSku());
            goodInfo.setProductName(getRandomElement(PRODUCT_NAMES));
            goodInfo.setRefundAmount(generateRandomAmount(10, 500));
            goodInfo.setPrice(generateRandomAmount(50, 1000));
            goodInfo.setProductNum(RANDOM.nextInt(3) + 1);
            goodInfo.setRefundProductNum(goodInfo.getProductNum());
            goodInfo.setSubTradeNo(item.getPlatSubOrderNo() + "_EX_" + (i + 1));
            goodInfo.setGoodsStatus(getRandomEnum(PolyRefundGoodsStatusEnum.values()).getCode());
            goodInfo.setGoodsStatusDesc(getRandomEnum(PolyRefundGoodsStatusEnum.values()).getDescription());
            
            exchangeGoods.add(goodInfo);
        }
        
        return exchangeGoods;
    }

    /**
     * 从数组中随机选择一个元素
     */
    private static <T> T getRandomElement(T[] array) {
        return array[RANDOM.nextInt(array.length)];
    }

    /**
     * 从枚举中随机选择一个值，可以指定偏好的枚举值
     */
    @SafeVarargs
    private static <T extends Enum<T>> T getRandomEnum(T[] values, T... preferred) {
        // 70%概率选择偏好的枚举值
        if (preferred.length > 0 && RANDOM.nextDouble() < 0.7) {
            return getRandomElement(preferred);
        }
        return getRandomElement(values);
    }

    /**
     * 条件设置值的通用方法
     */
    private static <T> void setIfNull(java.util.function.Consumer<T> setter, T currentValue, 
                                     BusinessGetExchangeOrderResponseOrderItem template,
                                     java.util.function.Function<BusinessGetExchangeOrderResponseOrderItem, T> templateGetter,
                                     java.util.function.Supplier<T> randomGenerator) {
        T valueToSet = null;
        
        // 如果当前值不为空，使用当前值
        if (currentValue != null && !isEmptyValue(currentValue)) {
            valueToSet = currentValue;
        }
        // 如果模板不为空且模板中该字段不为空，使用模板值
        else if (template != null) {
            T templateValue = templateGetter.apply(template);
            if (templateValue != null && !isEmptyValue(templateValue)) {
                valueToSet = templateValue;
            }
        }
        
        // 如果仍然为空，使用随机生成的值
        if (valueToSet == null || isEmptyValue(valueToSet)) {
            valueToSet = randomGenerator.get();
        }
        
        setter.accept(valueToSet);
    }

    /**
     * 判断值是否为空
     */
    private static boolean isEmptyValue(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return StringUtils.isBlank((String) value);
        }
        if (value instanceof Collection) {
            return ((Collection<?>) value).isEmpty();
        }
        if (value instanceof Map) {
            return ((Map<?, ?>) value).isEmpty();
        }
        return false;
    }

    //endregion
}
