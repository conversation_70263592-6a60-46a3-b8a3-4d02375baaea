package com.differ.wdgj.api.user.biz.infrastructure.cache.remote;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.wdgj.WdgjYunDataSourceCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.remote.WdgjYunDataSourceDto;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 网店管家云端 - 数据库链接缓存
 *
 * <AUTHOR>
 * @date 2024-03-26 15:27
 */
@Ignore
public class WdgjYunDataSourceCacheTest extends AbstractSpringTest {

    /**
     * 获取 数据库链接缓存
     */
    @Test
    public void getDataSourceTest() {
        WdgjYunDataSourceDto dataSource = WdgjYunDataSourceCache.singleton().getDataSource("1");
        Assert.assertNotNull(dataSource);
    }
}
