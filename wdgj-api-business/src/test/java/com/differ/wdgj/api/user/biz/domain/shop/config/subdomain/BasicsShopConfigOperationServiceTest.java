package com.differ.wdgj.api.user.biz.domain.shop.config.subdomain;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.shop.config.subdomain.impl.BasicsShopConfigOperationService;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ApiShopConfigBizTypes;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 店铺配置基础操作实现
 *
 * <AUTHOR>
 * @date 2025/2/25 上午10:20
 */
@Ignore
public class BasicsShopConfigOperationServiceTest  extends AbstractSpringTest {
    /**
     * 店铺配置基础操作实现服务
     */
    private IBasicsShopConfigOperationService service = new BasicsShopConfigOperationService();

    /**
     * 更新店铺配置
     */
    @Test
    public void updateShopConfigTest(){
        String memberName = "api2017";
        int shopId = 1027;
        ApiShopConfigBizTypes bizType = ApiShopConfigBizTypes.DOWNLOAD_ORDER;
        String configValue = "{\"isnotdownloadtopgoods\":\"0\",\"miraviaorder\":\"0\",\"miraviarefundorder\":\"0\",\"isconfirmorderbyapi\":\"0\",\"miraviashoplist\":[],\"shoptypelist\":[3],\"isautodownload\":\"0\",\"autodownload\":{\"interval\":\"30\"},\"isnotdownloadclosedorder\":\"1\",\"isnotdownloadunpayorder\":\"1\",\"isnotdownloadpartialdelivery\":\"1\",\"ismergebuyersaddress\":\"1\",\"isdownloadbeforecheckorder\":\"1\",\"isdownloadautoignoremerchantcode\":\"0\",\"isenableorderjammingcode\":\"0\",\"orderjammingcode\":\"\",\"notloadgoods\":\"\",\"isnotloadgoods\":\"0\",\"onlyloadgoods\":\"\",\"isonlyloadgoods\":\"0\",\"extremark\":\"\",\"isextractcertificatenum\":\"0\",\"isdownrefundform\":\"0\",\"isdownonlyrefundform\":\"0\",\"isusesettleprice\":\"0\",\"isthinkorderdiscount\":\"0\",\"isusereceivermobileasnick\":\"0\",\"notdownloadorderflag\":[],\"isenablepickcodefromtitle\":\"0\"}";

        boolean result = service.updateShopConfig(memberName, shopId, bizType, configValue);
        Assert.assertTrue(result);
    }

    /**
     * 更新api店铺Id
     */
    @Test
    public void updateApiShopIdTest(){
        String memberName = "api2017";
        int shopId = 1027;
        int apiShopId = 2734;

        boolean result = service.updateApiShopId(memberName, shopId, apiShopId);
        Assert.assertTrue(result);
    }
}
