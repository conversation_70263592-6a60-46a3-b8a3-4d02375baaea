package com.differ.wdgj.api.component.util;

import com.differ.wdgj.api.component.util.tools.AESCommonECB;
import com.differ.wdgj.api.component.util.tools.Md5Utils;
import org.junit.Ignore;
import org.junit.Test;

import java.nio.charset.StandardCharsets;

/**
 *  aes算法加解密，运算模式：ecb，填充模式：PKCS7
 *
 * <AUTHOR>
 * @date 2024/8/27 下午2:00
 */
@Ignore
public class AESCommonECBTest {
    private AESCommonECB aesEcb;

    //region 构造
    public AESCommonECBTest() throws Exception {
        aesEcb = new AESCommonECB();
        String appSecret = "1e2c62096829450baac80180193249e2";
        String password = Md5Utils.encryptBitStream(appSecret, StandardCharsets.UTF_8);
        aesEcb.init(password);
    }
    //endregion

    /**
     * 加密
     */
    @Test
    public void encryptTest(){
        String text = "55555";
        String encrypt = aesEcb.encrypt(text);
    }

    /**
     * 解密
     */
    @Test
    public void decryptTest() throws Exception {
        String text = "683340C5B04BE27BE7053BBB49879F98";
        String decrypt = aesEcb.decrypt(text);
    }
}
