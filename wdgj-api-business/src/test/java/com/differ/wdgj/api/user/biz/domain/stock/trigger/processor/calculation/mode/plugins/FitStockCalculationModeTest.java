package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.plugins;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.ErpGoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.GoodsStockCalculationDto;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.subdomain.calculation.IErpGoodsStockCalculationService;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ShopConfigStockSyncRuleEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 组合商品库存计算模式单元测试
 *
 * <AUTHOR>
 * @date 2025/6/17 12:10
 */
public class FitStockCalculationModeTest {

    @Mock
    private IErpGoodsStockCalculationService mockErpService;

    private StockSyncContext context;
    private GoodsStockCalculationDto calculationDto;
    private FitStockCalculationMode fitMode;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 创建测试上下文
        context = createTestContext();
        
        // 创建计算参数
        calculationDto = createCalculationDto();
        
        // 创建测试对象，使用反射注入mock服务
        fitMode = new FitStockCalculationMode(context);
        injectMockService(fitMode, mockErpService);
    }

    /**
     * 测试模式名称
     */
    @Test
    public void testGetModeName() {
        assertEquals("组合商品库存计算模式", fitMode.getModeName());
    }

    /**
     * 测试成功获取组合商品库存
     */
    @Test
    public void testDoGetActualStock_Success() {
        // 设置mock返回成功结果
        BigDecimal stockCount = BigDecimal.valueOf(50);
        String detailCount = ""; // 组合商品通常返回空详情
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, detailCount);
        
        when(mockErpService.calculationFitStock(
            eq(calculationDto.getShopConfigStockSyncRule().getValue()),
            eq(calculationDto.getErpGoodsId()),
            eq(calculationDto.getErpWarehouseIds())
        )).thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = fitMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(stockCount, result.getStockCount());
        
        // 验证库存详情为空（组合商品特性）
        assertNotNull(result.getStockDetailMap());
        assertTrue(result.getStockDetailMap().isEmpty());
        
        // 验证调用参数
        verify(mockErpService, times(1)).calculationFitStock(
            eq(calculationDto.getShopConfigStockSyncRule().getValue()),
            eq(calculationDto.getErpGoodsId()),
            eq(calculationDto.getErpWarehouseIds())
        );
    }

    /**
     * 测试ERP服务返回失败
     */
    @Test
    public void testDoGetActualStock_ErpServiceFailed() {
        String errorMessage = "组合商品计算失败";
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.failed(errorMessage);
        
        when(mockErpService.calculationFitStock(anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = fitMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isFailed());
        assertEquals(errorMessage, result.getMessage());
        
        // 验证调用
        verify(mockErpService, times(1)).calculationFitStock(anyInt(), anyInt(), anyList());
    }

    /**
     * 测试零库存组合商品
     */
    @Test
    public void testDoGetActualStock_ZeroStock() {
        BigDecimal stockCount = BigDecimal.ZERO;
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, "");
        
        when(mockErpService.calculationFitStock(anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = fitMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(BigDecimal.ZERO, result.getStockCount());
        assertTrue(result.getStockDetailMap().isEmpty());
    }

    /**
     * 测试大库存数量组合商品
     */
    @Test
    public void testDoGetActualStock_LargeStock() {
        BigDecimal stockCount = BigDecimal.valueOf(888888);
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, "");
        
        when(mockErpService.calculationFitStock(anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = fitMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(stockCount, result.getStockCount());
        assertTrue(result.getStockDetailMap().isEmpty());
    }

    /**
     * 测试不同的同步规则
     */
    @Test
    public void testDoGetActualStock_DifferentSyncRules() {
        BigDecimal stockCount = BigDecimal.valueOf(30);
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, "");
        
        when(mockErpService.calculationFitStock(anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 测试规则1
        calculationDto.setShopConfigStockSyncRule(ShopConfigStockSyncRuleEnum.RULE_ONE);
        GoodsStockCalculationResult result1 = fitMode.getActualStock(calculationDto);
        assertTrue(result1.isSuccess());
        verify(mockErpService).calculationFitStock(eq(1), anyInt(), anyList());
        
        // 测试规则2
        calculationDto.setShopConfigStockSyncRule(ShopConfigStockSyncRuleEnum.RULE_TWO);
        GoodsStockCalculationResult result2 = fitMode.getActualStock(calculationDto);
        assertTrue(result2.isSuccess());
        verify(mockErpService).calculationFitStock(eq(2), anyInt(), anyList());
        
        // 测试规则32
        calculationDto.setShopConfigStockSyncRule(ShopConfigStockSyncRuleEnum.RULE_THIRTYTWO);
        GoodsStockCalculationResult result32 = fitMode.getActualStock(calculationDto);
        assertTrue(result32.isSuccess());
        verify(mockErpService).calculationFitStock(eq(32), anyInt(), anyList());
    }

    /**
     * 测试不同的商品ID
     */
    @Test
    public void testDoGetActualStock_DifferentGoodsIds() {
        BigDecimal stockCount = BigDecimal.valueOf(25);
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, "");
        
        when(mockErpService.calculationFitStock(anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 测试商品ID 1001
        calculationDto.setErpGoodsId(1001);
        GoodsStockCalculationResult result1 = fitMode.getActualStock(calculationDto);
        assertTrue(result1.isSuccess());
        verify(mockErpService).calculationFitStock(anyInt(), eq(1001), anyList());
        
        // 测试商品ID 2002
        calculationDto.setErpGoodsId(2002);
        GoodsStockCalculationResult result2 = fitMode.getActualStock(calculationDto);
        assertTrue(result2.isSuccess());
        verify(mockErpService).calculationFitStock(anyInt(), eq(2002), anyList());
    }

    /**
     * 测试不同的仓库ID列表
     */
    @Test
    public void testDoGetActualStock_DifferentWarehouseIds() {
        BigDecimal stockCount = BigDecimal.valueOf(40);
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, "");
        
        when(mockErpService.calculationFitStock(anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 测试单个仓库
        calculationDto.setErpWarehouseIds(Arrays.asList(101));
        GoodsStockCalculationResult result1 = fitMode.getActualStock(calculationDto);
        assertTrue(result1.isSuccess());
        verify(mockErpService).calculationFitStock(anyInt(), anyInt(), eq(Arrays.asList(101)));
        
        // 测试多个仓库
        calculationDto.setErpWarehouseIds(Arrays.asList(101, 102, 103, 104));
        GoodsStockCalculationResult result2 = fitMode.getActualStock(calculationDto);
        assertTrue(result2.isSuccess());
        verify(mockErpService).calculationFitStock(anyInt(), anyInt(), eq(Arrays.asList(101, 102, 103, 104)));
    }

    /**
     * 测试异常处理
     */
    @Test
    public void testDoGetActualStock_Exception() {
        when(mockErpService.calculationFitStock(anyInt(), anyInt(), anyList()))
            .thenThrow(new RuntimeException("组合商品计算异常"));
        
        // 执行测试
        GoodsStockCalculationResult result = fitMode.getActualStock(calculationDto);
        
        // 验证异常处理
        assertTrue(result.isFailed());
        assertTrue(result.getMessage().contains("testUser"));
        assertTrue(result.getMessage().contains("组合商品库存计算模式"));
        assertTrue(result.getMessage().contains("组合商品计算异常"));
    }

    /**
     * 测试空参数处理
     */
    @Test
    public void testDoGetActualStock_NullParameters() {
        // 测试空计算参数
        GoodsStockCalculationResult result = fitMode.getActualStock(null);
        assertTrue(result.isFailed());
        assertTrue(result.getMessage().contains("testUser"));
    }

    /**
     * 测试小数库存
     */
    @Test
    public void testDoGetActualStock_DecimalStock() {
        BigDecimal stockCount = new BigDecimal("123.45");
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, "");
        
        when(mockErpService.calculationFitStock(anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = fitMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(stockCount, result.getStockCount());
        assertTrue(result.getStockDetailMap().isEmpty());
    }

    /**
     * 测试负库存（虽然业务上不应该出现，但测试边界情况）
     */
    @Test
    public void testDoGetActualStock_NegativeStock() {
        BigDecimal stockCount = BigDecimal.valueOf(-10);
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, "");
        
        when(mockErpService.calculationFitStock(anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = fitMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(stockCount, result.getStockCount());
        assertTrue(result.getStockDetailMap().isEmpty());
    }

    //region 私有方法
    /**
     * 创建测试上下文
     */
    private StockSyncContext createTestContext() {
        StockSyncContext context = new StockSyncContext();
        context.setVipUser("testUser");
        context.setShopId(12345);
        context.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        
        ApiShopBaseDto shopBase = new ApiShopBaseDto();
        shopBase.setShopId(12345);
        shopBase.setShopName("测试店铺");
        shopBase.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        context.setShopBase(shopBase);
        
        SyncStockShopConfig syncConfig = new SyncStockShopConfig();
        context.setSyncStockConfig(syncConfig);
        
        return context;
    }

    /**
     * 创建计算参数
     */
    private GoodsStockCalculationDto createCalculationDto() {
        GoodsStockCalculationDto dto = new GoodsStockCalculationDto();
        dto.setShopId(12345);
        dto.setErpGoodsId(1001);
        dto.setErpSpecId(2001);
        dto.setGoodsType(2); // 组合商品
        dto.setErpWarehouseIds(Arrays.asList(101, 102, 103));
        dto.setShopConfigStockSyncRule(ShopConfigStockSyncRuleEnum.RULE_THREE);
        return dto;
    }

    /**
     * 注入Mock服务（使用反射）
     */
    private void injectMockService(FitStockCalculationMode mode, IErpGoodsStockCalculationService mockService) {
        try {
            java.lang.reflect.Field field = mode.getClass().getSuperclass().getDeclaredField("erpGoodsStockCalculationService");
            field.setAccessible(true);
            field.set(mode, mockService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject mock service", e);
        }
    }
    //endregion
}
