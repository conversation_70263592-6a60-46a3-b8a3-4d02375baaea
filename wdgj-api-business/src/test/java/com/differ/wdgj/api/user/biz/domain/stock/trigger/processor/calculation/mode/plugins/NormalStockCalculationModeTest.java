package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.mode.plugins;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockDetailCountTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.ErpGoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.GoodsStockCalculationDto;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.subdomain.calculation.IErpGoodsStockCalculationService;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.ApiShopBaseDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.shop.config.SyncStockShopConfig;
import com.differ.wdgj.api.user.biz.infrastructure.data.enums.shopconfig.ShopConfigStockSyncRuleEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 普通库存计算模式单元测试
 *
 * <AUTHOR>
 * @date 2025/6/17 12:00
 */
public class NormalStockCalculationModeTest {

    @Mock
    private IErpGoodsStockCalculationService mockErpService;

    private StockSyncContext context;
    private GoodsStockCalculationDto calculationDto;
    private NormalStockCalculationMode normalMode;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 创建测试上下文
        context = createTestContext();
        
        // 创建计算参数
        calculationDto = createCalculationDto();
        
        // 创建测试对象，使用反射注入mock服务
        normalMode = new NormalStockCalculationMode(context);
        injectMockService(normalMode, mockErpService);
    }

    /**
     * 测试模式名称
     */
    @Test
    public void testGetModeName() {
        assertEquals("普通库存计算模式", normalMode.getModeName());
    }

    /**
     * 测试成功获取实际库存
     */
    @Test
    public void testDoGetActualStock_Success() {
        // 设置mock返回成功结果
        BigDecimal stockCount = BigDecimal.valueOf(100);
        String detailCount = "1:100;2:10;3:5"; // 实际库存:100, 未付款:10, 订购量:5
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, detailCount);
        
        when(mockErpService.calculationNormalStock(
            eq(calculationDto.getShopId()),
            eq(calculationDto.getErpGoodsId()),
            eq(calculationDto.getErpSpecId()),
            eq(calculationDto.getErpWarehouseIds())
        )).thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = normalMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(stockCount, result.getStockCount());
        
        // 验证库存详情解析
        Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap = result.getStockDetailMap();
        assertNotNull(stockDetailMap);
        assertEquals(BigDecimal.valueOf(100), stockDetailMap.get(StockDetailCountTypeEnum.InStock));
        assertEquals(BigDecimal.valueOf(10), stockDetailMap.get(StockDetailCountTypeEnum.OrderQuantity));
        assertEquals(BigDecimal.valueOf(5), stockDetailMap.get(StockDetailCountTypeEnum.WaitSend));
        
        // 验证调用
        verify(mockErpService, times(1)).calculationNormalStock(
            eq(calculationDto.getShopId()),
            eq(calculationDto.getErpGoodsId()),
            eq(calculationDto.getErpSpecId()),
            eq(calculationDto.getErpWarehouseIds())
        );
    }

    /**
     * 测试ERP服务返回失败
     */
    @Test
    public void testDoGetActualStock_ErpServiceFailed() {
        String errorMessage = "ERP服务调用失败";
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.failed(errorMessage);
        
        when(mockErpService.calculationNormalStock(anyInt(), anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = normalMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isFailed());
        assertEquals(errorMessage, result.getMessage());
        
        // 验证调用
        verify(mockErpService, times(1)).calculationNormalStock(anyInt(), anyInt(), anyInt(), anyList());
    }

    /**
     * 测试空的库存详情
     */
    @Test
    public void testDoGetActualStock_EmptyDetailCount() {
        BigDecimal stockCount = BigDecimal.valueOf(50);
        String detailCount = ""; // 空详情
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, detailCount);
        
        when(mockErpService.calculationNormalStock(anyInt(), anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = normalMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(stockCount, result.getStockCount());
        
        // 验证空详情处理
        Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap = result.getStockDetailMap();
        assertNotNull(stockDetailMap);
        assertTrue(stockDetailMap.isEmpty());
    }

    /**
     * 测试null的库存详情
     */
    @Test
    public void testDoGetActualStock_NullDetailCount() {
        BigDecimal stockCount = BigDecimal.valueOf(75);
        String detailCount = null; // null详情
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, detailCount);
        
        when(mockErpService.calculationNormalStock(anyInt(), anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = normalMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(stockCount, result.getStockCount());
        
        // 验证null详情处理
        Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap = result.getStockDetailMap();
        assertNotNull(stockDetailMap);
        assertTrue(stockDetailMap.isEmpty());
    }

    /**
     * 测试复杂的库存详情
     */
    @Test
    public void testDoGetActualStock_ComplexDetailCount() {
        BigDecimal stockCount = BigDecimal.valueOf(200);
        String detailCount = "1:200;2:15;3:8;4:12;5:25;6:5"; // 多种库存类型
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, detailCount);
        
        when(mockErpService.calculationNormalStock(anyInt(), anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = normalMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(stockCount, result.getStockCount());
        
        // 验证复杂详情解析
        Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap = result.getStockDetailMap();
        assertNotNull(stockDetailMap);
        assertEquals(6, stockDetailMap.size());
        assertEquals(BigDecimal.valueOf(200), stockDetailMap.get(StockDetailCountTypeEnum.InStock));
        assertEquals(BigDecimal.valueOf(15), stockDetailMap.get(StockDetailCountTypeEnum.OrderQuantity));
        assertEquals(BigDecimal.valueOf(8), stockDetailMap.get(StockDetailCountTypeEnum.WaitSend));
    }

    /**
     * 测试无效的库存详情格式
     */
    @Test
    public void testDoGetActualStock_InvalidDetailCount() {
        BigDecimal stockCount = BigDecimal.valueOf(80);
        String detailCount = "invalid:format;1:abc;2:"; // 无效格式
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, detailCount);
        
        when(mockErpService.calculationNormalStock(anyInt(), anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = normalMode.getActualStock(calculationDto);
        
        // 验证结果 - 应该成功但详情解析可能部分失败
        assertTrue(result.isSuccess());
        assertEquals(stockCount, result.getStockCount());
        
        // 验证详情处理 - 无效格式应该被忽略
        Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap = result.getStockDetailMap();
        assertNotNull(stockDetailMap);
        // 具体的处理逻辑取决于convertDetailCount方法的实现
    }

    /**
     * 测试零库存
     */
    @Test
    public void testDoGetActualStock_ZeroStock() {
        BigDecimal stockCount = BigDecimal.ZERO;
        String detailCount = "1:0;2:0;3:0";
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, detailCount);
        
        when(mockErpService.calculationNormalStock(anyInt(), anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = normalMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(BigDecimal.ZERO, result.getStockCount());
        
        // 验证零库存详情
        Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap = result.getStockDetailMap();
        assertNotNull(stockDetailMap);
        assertEquals(BigDecimal.ZERO, stockDetailMap.get(StockDetailCountTypeEnum.InStock));
    }

    /**
     * 测试大库存数量
     */
    @Test
    public void testDoGetActualStock_LargeStock() {
        BigDecimal stockCount = BigDecimal.valueOf(999999);
        String detailCount = "1:999999;2:1000;3:500";
        ErpGoodsStockCalculationResult erpResult = ErpGoodsStockCalculationResult.success(stockCount, detailCount);
        
        when(mockErpService.calculationNormalStock(anyInt(), anyInt(), anyInt(), anyList()))
            .thenReturn(erpResult);
        
        // 执行测试
        GoodsStockCalculationResult result = normalMode.getActualStock(calculationDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(stockCount, result.getStockCount());
        
        // 验证大数值处理
        Map<StockDetailCountTypeEnum, BigDecimal> stockDetailMap = result.getStockDetailMap();
        assertNotNull(stockDetailMap);
        assertEquals(BigDecimal.valueOf(999999), stockDetailMap.get(StockDetailCountTypeEnum.InStock));
    }

    /**
     * 测试异常处理
     */
    @Test
    public void testDoGetActualStock_Exception() {
        when(mockErpService.calculationNormalStock(anyInt(), anyInt(), anyInt(), anyList()))
            .thenThrow(new RuntimeException("数据库连接异常"));
        
        // 执行测试
        GoodsStockCalculationResult result = normalMode.getActualStock(calculationDto);
        
        // 验证异常处理
        assertTrue(result.isFailed());
        assertTrue(result.getMessage().contains("testUser"));
        assertTrue(result.getMessage().contains("普通库存计算模式"));
        assertTrue(result.getMessage().contains("数据库连接异常"));
    }

    //region 私有方法
    /**
     * 创建测试上下文
     */
    private StockSyncContext createTestContext() {
        StockSyncContext context = new StockSyncContext();
        context.setVipUser("testUser");
        context.setShopId(12345);
        context.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        
        ApiShopBaseDto shopBase = new ApiShopBaseDto();
        shopBase.setShopId(12345);
        shopBase.setShopName("测试店铺");
        shopBase.setPlat(PolyPlatEnum.BUSINESS_Taobao);
        context.setShopBase(shopBase);
        
        SyncStockShopConfig syncConfig = new SyncStockShopConfig();
        context.setSyncStockConfig(syncConfig);
        
        return context;
    }

    /**
     * 创建计算参数
     */
    private GoodsStockCalculationDto createCalculationDto() {
        GoodsStockCalculationDto dto = new GoodsStockCalculationDto();
        dto.setShopId(12345);
        dto.setErpGoodsId(1001);
        dto.setErpSpecId(2001);
        dto.setGoodsType(1); // 普通商品
        dto.setErpWarehouseIds(Arrays.asList(101, 102, 103));
        dto.setShopConfigStockSyncRule(ShopConfigStockSyncRuleEnum.RULE_THREE);
        return dto;
    }

    /**
     * 注入Mock服务（使用反射）
     */
    private void injectMockService(NormalStockCalculationMode mode, IErpGoodsStockCalculationService mockService) {
        try {
            java.lang.reflect.Field field = mode.getClass().getSuperclass().getDeclaredField("erpGoodsStockCalculationService");
            field.setAccessible(true);
            field.set(mode, mockService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject mock service", e);
        }
    }
    //endregion
}
