package com.differ.wdgj.api;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jodd.util.URLDecoder;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.security.MessageDigest;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/7/15 14:59
 */
//@Ignore
public class UrlTest {

    @Test
    public void test() {
        String json = "%7B%22pageSize%22:10,%22pageIndex%22:1,%22plat%22:%22%22,%22module%22:%22%22,%22ruleType%22:%22%22,%22keywordDescription%22:%22授权%22%7D";
        String decode = URLDecoder.decode(json);
        String decode2 = URLDecoder.decode(json, "UTF-8");
    }

    @Test
    public void test2(){
        // JSON文件路径
        String jsonFilePath = "C:\\Users\\<USER>\\Desktop\\lnwmqq问题单.txt";

        // 创建一个Map来存储结果
        Map<String, List<String>> orderProductChannelMap = new HashMap<>();
        Map<String, Integer> orderStatusMap = new HashMap<>();

        // 创建ObjectMapper对象
        ObjectMapper objectMapper = new ObjectMapper();

        try (BufferedReader br = new BufferedReader(new FileReader(jsonFilePath))) {
            String line;
            int lineNumber = 0; // 用于记录行号，方便调试
            while ((line = br.readLine()) != null) {
                lineNumber++;
                try {
                    // 解析每一行的JSON
                    JsonNode rootNode = objectMapper.readTree(line);

                    // 获取shop_order_detail节点
                    JsonNode shopOrderDetailNode = rootNode.path("shop_order_detail");

                    // 获取shop_order_detail下的order_id
                    String orderId = shopOrderDetailNode.path("order_id").asText();

                    // 遍历sku_order_list数组
                    JsonNode skuOrderListNode = shopOrderDetailNode.path("sku_order_list");
                    for (JsonNode skuOrderNode : skuOrderListNode) {
                        // 获取product_channel_id
                        String productChannelId = skuOrderNode.path("product_channel_info").path("product_channel_id").asText();

                        // 获取order_status
                        int orderStatus = skuOrderNode.path("order_status").asInt();

                        // 将product_channel_id添加到Map中
                        orderProductChannelMap.computeIfAbsent(orderId, k -> new ArrayList<>()).add(productChannelId);

                        // 保存order_status
                        orderStatusMap.put(orderId, orderStatus);
                    }
                } catch (Exception e) {
                    // 捕获解析异常，记录错误信息并跳过当前行
                    System.err.println("Error parsing line " + lineNumber);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 输出结果
        List<String> needProcessIds = new ArrayList<>();
        needProcessIds.add("3420898941993730");
        needProcessIds.add("3426879431094786");
        needProcessIds.add("3430084950234882");
        needProcessIds.add("3432919841698050");
        needProcessIds.add("3436333214191362");
        needProcessIds.add("3439439559872514");

        for (Map.Entry<String, List<String>> entry : orderProductChannelMap.entrySet()) {
            String orderId = entry.getKey();
            List<String> productChannelIds = entry.getValue();
            int orderStatus = orderStatusMap.get(orderId);

            // 将product_channel_id列表转换为逗号分隔的字符串
            String productChannelIdsStr = String.join(",", productChannelIds);

            if(orderStatus == 4 || productChannelIds.stream().noneMatch(x -> needProcessIds.stream().anyMatch(x::equalsIgnoreCase))){
                System.out.println("Order ID: " + orderId + ", Product Channel IDs: " + productChannelIdsStr + ", Order Status: " + orderStatus);
            }
        }
    }

    @Test
    public void test3() throws Exception {
        String str = "İ";
        String sign0 = encrypt(str, "");
        String lower = str.toLowerCase(new java.util.Locale("tr", "TR"));
        String sign1 = encrypt(lower, "");
        String lower2 = str.toLowerCase();
        String sign2 = encrypt(lower2, "");
        String lower3 = str.toLowerCase(Locale.ROOT);
        String sign3 = encrypt(lower3, "");
        boolean equals = lower.equals(lower2);
    }

    public String encrypt(String text, String encoding) throws Exception {
        if (encoding == null || encoding.isEmpty()) {
            encoding = "UTF-8";
        }
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] resultByte = text.getBytes(encoding);
        byte[] md5Bytes = md5.digest(resultByte);
        StringBuilder hexValue = new StringBuilder();
        for (byte md5Byte : md5Bytes) {
            int val = (md5Byte) & 0xff;
            if (val < 16) {
                hexValue.append("0");
            }
            hexValue.append(Integer.toHexString(val));
        }
        return hexValue.toString();
    }
}
