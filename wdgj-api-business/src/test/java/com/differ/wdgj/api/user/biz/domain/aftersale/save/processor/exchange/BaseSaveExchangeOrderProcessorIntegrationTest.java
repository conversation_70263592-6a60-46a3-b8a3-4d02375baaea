package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.exchange;

import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.AfterSaleSaveContext;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.out.SaveAfterSaleOrderResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.SaveAfterSaleResult;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.data.result.order.SaveOrderResultComposite;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.ISaveAfterSaleOrder;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.core.BaseAfterSaleOrderTest;
import com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.core.BusinessGetExchangeOrderResponseOrderItemGenerator;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.aftersale.PolyExchangeStatusEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetExchangeOrderResponseOrderItem;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.Month;
import java.util.Arrays;
import java.util.List;

/**
 * BaseSaveExchangeOrderProcessor 集成测试
 * 基于 BusinessGetExchangeOrderResponseOrderItemGenerator 生成测试数据
 * 直接依赖 Spring 和数据库，不使用桩对象
 *
 * <AUTHOR>
 * @date 2024/12/19 下午10:30
 */
public class BaseSaveExchangeOrderProcessorIntegrationTest extends BaseAfterSaleOrderTest {

    //region 基础集成测试

    /**
     * 基础换货单保存集成测试
     * 测试完整的保存流程，包括所有插件逻辑
     */
    @Test
    public void testSaveExchangeOrder_BasicIntegration() {
        // 生成随机测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom();
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96936);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("保存操作应该成功", result.isSuccess());
        
        // 打印测试信息
        printTestInfo("基础集成测试", orderItem, result);
    }

    /**
     * 批量换货单保存集成测试
     * 测试批量处理逻辑
     */
    @Test
    public void testSaveExchangeOrder_BatchIntegration() {
        // 生成多个随机测试数据
        List<BusinessGetExchangeOrderResponseOrderItem> orderItems = Arrays.asList(
            BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom(),
            BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom(),
            BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom()
        );
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96936);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(orderItems);
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("批量保存操作应该成功", result.isSuccess());
        Assert.assertNotNull("保存结果数据不应为空", result.getContent());
        
        // 打印批量测试信息
        System.out.println("=== 批量集成测试结果 ===");
        System.out.println("处理订单数量: " + orderItems.size());
        System.out.println("保存结果: " + (result.isSuccess() ? "成功" : "失败"));
        if (result.getContent() != null) {
            System.out.println("处理结果数量: " + result.getContent().size());
        }
        System.out.println("===================");
    }

    //endregion

    //region 不同换货状态测试

    /**
     * 换货成功状态(JH_06)集成测试
     * 测试换货成功状态的处理逻辑
     */
    @Test
    public void testSaveExchangeOrder_ExchangeSuccess_JH06() {
        // 创建换货成功状态模板
        BusinessGetExchangeOrderResponseOrderItem template = new BusinessGetExchangeOrderResponseOrderItem();
        template.setOrderStatus(PolyExchangeStatusEnum.JH_06.getCode());
        
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = 
            BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96936);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("换货成功状态保存应该成功", result.isSuccess());
        
        // 验证换货状态
        Assert.assertEquals("换货状态应为JH_06", PolyExchangeStatusEnum.JH_06.getCode(), orderItem.getOrderStatus());
        
        printTestInfo("换货成功状态(JH_06)测试", orderItem, result);
    }

    /**
     * 卖家已同意换货等待买家换货(JH_02)集成测试
     * 测试等待买家换货状态的处理逻辑
     */
    @Test
    public void testSaveExchangeOrder_WaitingBuyerExchange_JH02() {
        // 创建等待买家换货状态模板
        BusinessGetExchangeOrderResponseOrderItem template = new BusinessGetExchangeOrderResponseOrderItem();
        template.setOrderStatus(PolyExchangeStatusEnum.JH_02.getCode());
        
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = 
            BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96936);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("等待买家换货状态保存应该成功", result.isSuccess());
        
        // 验证换货状态
        Assert.assertEquals("换货状态应为JH_02", PolyExchangeStatusEnum.JH_02.getCode(), orderItem.getOrderStatus());
        
        printTestInfo("等待买家换货状态(JH_02)测试", orderItem, result);
    }

    /**
     * 买家已退货等待卖家确认收货(JH_03)集成测试
     * 测试等待卖家确认收货状态的处理逻辑
     */
    @Test
    public void testSaveExchangeOrder_WaitingSellerConfirm_JH03() {
        // 创建等待卖家确认收货状态模板
        BusinessGetExchangeOrderResponseOrderItem template = new BusinessGetExchangeOrderResponseOrderItem();
        template.setOrderStatus(PolyExchangeStatusEnum.JH_03.getCode());
        
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = 
            BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96936);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("等待卖家确认收货状态保存应该成功", result.isSuccess());
        
        // 验证换货状态
        Assert.assertEquals("换货状态应为JH_03", PolyExchangeStatusEnum.JH_03.getCode(), orderItem.getOrderStatus());
        
        printTestInfo("等待卖家确认收货状态(JH_03)测试", orderItem, result);
    }

    /**
     * 退货退款不换货(JH_10)集成测试
     * 测试退货退款不换货状态的处理逻辑
     */
    @Test
    public void testSaveExchangeOrder_RefundInsteadOfExchange_JH10() {
        // 创建退货退款不换货状态模板
        BusinessGetExchangeOrderResponseOrderItem template = new BusinessGetExchangeOrderResponseOrderItem();
        template.setOrderStatus(PolyExchangeStatusEnum.JH_10.getCode());
        template.setRefundAmount(new BigDecimal("299.99")); // 设置退款金额
        
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = 
            BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96936);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("退货退款不换货状态保存应该成功", result.isSuccess());
        
        // 验证换货状态和退款金额
        Assert.assertEquals("换货状态应为JH_10", PolyExchangeStatusEnum.JH_10.getCode(), orderItem.getOrderStatus());
        Assert.assertNotNull("退款金额不应为空", orderItem.getRefundAmount());
        Assert.assertTrue("退款金额应大于0", orderItem.getRefundAmount().compareTo(BigDecimal.ZERO) > 0);
        
        printTestInfo("退货退款不换货状态(JH_10)测试", orderItem, result);
    }

    //endregion

    //region 平台级特殊处理测试

    /**
     * 拼多多平台特殊处理集成测试
     * 测试拼多多平台的特殊逻辑
     */
    @Test
    public void testSaveExchangeOrder_PDD_PlatformSpecific() {
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom();
        
        // 创建拼多多上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 96936, PolyPlatEnum.BUSINESS_Yangkeduo);
        
        // 创建处理器（会自动选择PddSaveExchangeOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        
        printPlatformTestInfo("拼多多", orderItem, result);
    }

    /**
     * 抖音放心购平台特殊处理集成测试
     * 测试抖音放心购平台的特殊逻辑
     */
    @Test
    public void testSaveExchangeOrder_ByteDance_PlatformSpecific() {
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom();
        
        // 创建抖音放心购上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 1033, PolyPlatEnum.BUSINESS_FangXinGou);
        
        // 创建处理器（会自动选择ByteDanceSaveExchangeOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        
        printPlatformTestInfo("抖音放心购", orderItem, result);
    }

    /**
     * 抖音放心购平台特殊处理集成测试
     * 消费者删除售后单，导致更新时间变了
     */
    @Test
    public void testSaveExchangeOrder_ByteDance_CustomerDeleteOrder_PlatformSpecific() {
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom();

        //售后单创建时间：2024-10-20 12:00:00
        orderItem.setCreateTime(LocalDateTime.of(2024, Month.OCTOBER, 20, 12, 00, 00));
        //消费者删除售后单时间
        orderItem.setUpdateTime(LocalDateTime.now());

        // 创建抖音放心购上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 1033, PolyPlatEnum.BUSINESS_FangXinGou);
        // 创建处理器（会自动选择ByteDanceSaveExchangeOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = SaveExchangeOrderFactory.createProcessor(context);
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        printPlatformTestInfo("抖音放心购", orderItem, result);
    }

    /**
     * 小红书平台特殊处理集成测试
     * 测试小红书平台的特殊逻辑
     */
    @Test
    public void testSaveExchangeOrder_XiaoHS_PlatformSpecific() {
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom();
        
        // 创建小红书上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 96230, PolyPlatEnum.BUSINESS_XiaoHS);
        
        // 创建处理器（会自动选择XiaoHSSaveExchangeOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        
        printPlatformTestInfo("小红书", orderItem, result);
    }

    /**
     * 京东平台特殊处理集成测试
     * 测试京东平台的换货单处理逻辑
     */
    @Test
    public void testSaveExchangeOrder_JD_PlatformSpecific() {
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom();
        
        // 创建京东上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 2001, PolyPlatEnum.BUSINESS_JD);
        
        // 创建处理器（会自动选择JDSaveExchangeOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        
        printPlatformTestInfo("京东", orderItem, result);
    }

    /**
     * 天猫平台特殊处理集成测试
     * 测试天猫平台的换货单处理逻辑
     */
    @Test
    public void testSaveExchangeOrder_Tmall_PlatformSpecific() {
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom();
        
        // 创建天猫上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 1100, PolyPlatEnum.BUSINESS_Taobao);
        
        // 创建处理器（会自动选择TmallSaveExchangeOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        
        printPlatformTestInfo("天猫", orderItem, result);
    }

    /**
     * 淘宝平台特殊处理集成测试
     * 测试淘宝平台的换货单处理逻辑
     */
    @Test
    public void testSaveExchangeOrder_Taobao_PlatformSpecific() {
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom();
        
        // 创建淘宝上下文
        AfterSaleSaveContext context = getContextForPlatform("api2017", 1001, PolyPlatEnum.BUSINESS_Taobao);
        
        // 创建处理器（会自动选择TaobaoSaveExchangeOrderProcessor）
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        
        printPlatformTestInfo("淘宝", orderItem, result);
    }

    //endregion

    //region 业务场景测试

    /**
     * 高金额换货单集成测试
     * 测试大额换货的处理逻辑
     */
    @Test
    public void testSaveExchangeOrder_HighAmountExchange() {
        // 创建高金额模板
        BusinessGetExchangeOrderResponseOrderItem template = new BusinessGetExchangeOrderResponseOrderItem();
        template.setPayment(new BigDecimal("9999.99"));
        template.setRefundAmount(new BigDecimal("8888.88"));
        
        // 生成测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = 
            BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96936);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("高金额换货单保存应该成功", result.isSuccess());
        
        // 验证金额
        Assert.assertEquals("支付金额应为9999.99", new BigDecimal("9999.99"), orderItem.getPayment());
        Assert.assertEquals("退款金额应为8888.88", new BigDecimal("8888.88"), orderItem.getRefundAmount());
        
        printTestInfo("高金额换货单测试", orderItem, result);
    }

    /**
     * 多商品换货单集成测试
     * 测试包含多个商品的换货单处理逻辑
     */
    @Test
    public void testSaveExchangeOrder_MultipleGoods() {
        // 生成包含多个商品的测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom();
        
        // 确保有多个换货商品
        Assert.assertNotNull("退回商品列表不应为空", orderItem.getRefundGoods());
        Assert.assertFalse("退回商品列表不应为空", orderItem.getRefundGoods().isEmpty());
        Assert.assertNotNull("换出商品列表不应为空", orderItem.getExchangeGoods());
        Assert.assertFalse("换出商品列表不应为空", orderItem.getExchangeGoods().isEmpty());
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96936);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("多商品换货单保存应该成功", result.isSuccess());
        
        System.out.println("=== 多商品换货单测试 ===");
        System.out.println("换货单号: " + orderItem.getExchangeOrderNo());
        System.out.println("退回商品数量: " + orderItem.getRefundGoods().size());
        System.out.println("换出商品数量: " + orderItem.getExchangeGoods().size());
        System.out.println("处理结果: " + (result.isSuccess() ? "成功" : "失败"));
        orderItem.getRefundGoods().forEach(goods -> {
            System.out.println("  退回商品: " + goods.getProductName() + ", 数量: " + goods.getRefundProductNum());
        });
        orderItem.getExchangeGoods().forEach(goods -> {
            System.out.println("  换出商品: " + goods.getProductName() + ", 数量: " + goods.getRefundProductNum());
        });
        System.out.println("==================");
    }

    /**
     * 完整物流信息换货单集成测试
     * 测试包含完整物流信息的换货单处理逻辑
     */
    @Test
    public void testSaveExchangeOrder_CompleteLogisticsInfo() {
        // 生成包含完整物流信息的测试数据
        BusinessGetExchangeOrderResponseOrderItem orderItem = BusinessGetExchangeOrderResponseOrderItemGenerator.generateRandom();
        
        // 验证物流信息
        Assert.assertNotNull("买家物流公司不应为空", orderItem.getBuyerLogisticName());
        Assert.assertNotNull("买家物流单号不应为空", orderItem.getBuyerLogisticNo());
        Assert.assertNotNull("卖家物流公司不应为空", orderItem.getSellerLogisticName());
        Assert.assertNotNull("卖家物流单号不应为空", orderItem.getSellerLogisticNo());
        
        // 创建上下文
        AfterSaleSaveContext context = getContext("api2017", 96936);
        
        // 创建处理器
        ISaveAfterSaleOrder<BusinessGetExchangeOrderResponseOrderItem> processor = 
            SaveExchangeOrderFactory.createProcessor(context);
        
        // 执行保存操作
        SaveAfterSaleResult<List<SaveOrderResultComposite>> result = processor.saveOrder(Arrays.asList(orderItem));
        
        // 验证结果
        Assert.assertNotNull("保存结果不应为空", result);
        Assert.assertTrue("完整物流信息换货单保存应该成功", result.isSuccess());
        
        System.out.println("=== 完整物流信息换货单测试 ===");
        System.out.println("换货单号: " + orderItem.getExchangeOrderNo());
        System.out.println("买家物流: " + orderItem.getBuyerLogisticName() + " - " + orderItem.getBuyerLogisticNo());
        System.out.println("卖家物流: " + orderItem.getSellerLogisticName() + " - " + orderItem.getSellerLogisticNo());
        System.out.println("处理结果: " + (result.isSuccess() ? "成功" : "失败"));
        System.out.println("========================");
    }

    //endregion

    //region 工具方法

    /**
     * 创建指定平台的上下文
     */
    private AfterSaleSaveContext getContextForPlatform(String memberName, int shopId, PolyPlatEnum platform) {
        AfterSaleSaveContext context = getContext(memberName, shopId);
        // 设置平台信息
        context.setPlat(platform);
        return context;
    }

    /**
     * 打印测试信息
     */
    private void printTestInfo(String testName, BusinessGetExchangeOrderResponseOrderItem orderItem, 
                              SaveAfterSaleResult<List<SaveOrderResultComposite>> result) {
        System.out.println("=== " + testName + " ===");
        System.out.println("换货单号: " + orderItem.getExchangeOrderNo());
        System.out.println("平台订单号: " + orderItem.getPlatOrderNo());
        System.out.println("换货状态: " + orderItem.getOrderStatus());
        System.out.println("支付金额: " + orderItem.getPayment());
        System.out.println("退款金额: " + orderItem.getRefundAmount());
        System.out.println("买家昵称: " + orderItem.getBuyerNick());
        System.out.println("换货原因: " + orderItem.getReason());
        System.out.println("处理结果: " + (result.isSuccess() ? "成功" : "失败"));
        if (!result.isSuccess()) {
            System.out.println("失败原因: " + result.getMessage());
        }

        // 订单级结果
        if(result.isSuccess()){
            result.getContent().forEach(composite -> {
                SaveAfterSaleOrderResult orderResult = composite.convergedResults();
                String orderInfo = String.format("订单号: %s, 结果: %s, 详细信息：%s", orderResult.getAfterSaleNo(), orderResult.isSuccess() ? "成功" : "失败", orderResult.getMessage());
                System.out.println(orderInfo);
            });
        }
        System.out.println("===================");
    }

    /**
     * 打印平台测试信息
     */
    private void printPlatformTestInfo(String platformName, BusinessGetExchangeOrderResponseOrderItem orderItem, 
                                     SaveAfterSaleResult<List<SaveOrderResultComposite>> result) {
        System.out.println("=== " + platformName + "平台特殊处理测试 ===");
        System.out.println("换货单号: " + orderItem.getExchangeOrderNo());
        System.out.println("平台订单号: " + orderItem.getPlatOrderNo());
        System.out.println("换货状态: " + orderItem.getOrderStatus());
        System.out.println("买家昵称: " + orderItem.getBuyerNick());
        System.out.println("买家姓名: " + orderItem.getBuyerName());
        System.out.println("买家电话: " + orderItem.getBuyerPhone());
        System.out.println("处理结果: " + (result.isSuccess() ? "成功" : "失败"));
        if (!result.isSuccess()) {
            System.out.println("失败原因: " + result.getMessage());
        }
        if (result.getContent() != null) {
            System.out.println("处理结果数量: " + result.getContent().size());
        }

        // 订单级结果
        if(result.isSuccess()){
            result.getContent().forEach(composite -> {
                SaveAfterSaleOrderResult orderResult = composite.convergedResults();
                String orderInfo = String.format("订单号: %s, 结果: %s, 详细信息：%s", orderResult.getAfterSaleNo(), orderResult.isSuccess() ? "成功" : "失败", orderResult.getMessage());
                System.out.println(orderInfo);
            });
        }

        System.out.println("========================");
    }

    //endregion
}
