package com.differ.wdgj.api.user.biz.domain.aftersale.save.processor.core;

import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.getRefund.BusinessGetRefundOrderResponseOrderItem;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

/**
 * BusinessGetRefundOrderResponseOrderItemGenerator 测试类
 *
 * <AUTHOR>
 * @date 2024/12/19 下午8:30
 */
public class BusinessGetRefundOrderResponseOrderItemGeneratorTest {

    /**
     * 测试生成随机数据的基本功能
     */
    @Test
    public void testGenerateRandom_BasicFunctionality() {
        // 生成随机数据
        BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
        
        // 验证必定随机生成的字段
        Assert.assertNotNull("退款单号不应为空", item.getRefundNo());
        Assert.assertTrue("退款单号应以RF开头", item.getRefundNo().startsWith("RF"));
        Assert.assertNotNull("平台订单号不应为空", item.getPlatOrderNo());
        Assert.assertTrue("平台订单号应以PO开头", item.getPlatOrderNo().startsWith("PO"));
        
        // 验证基础字段不为空
        Assert.assertNotNull("总金额不应为空", item.getTotalAmount());
        Assert.assertNotNull("退款金额不应为空", item.getRefundAmount());
        Assert.assertNotNull("支付金额不应为空", item.getPayAmount());
        Assert.assertNotNull("买家昵称不应为空", item.getBuyerNick());
        Assert.assertNotNull("创建时间不应为空", item.getCreateTime());
        Assert.assertNotNull("更新时间不应为空", item.getUpdateTime());
        Assert.assertNotNull("售后类型不应为空", item.getRefundType());
        Assert.assertNotNull("商品名称不应为空", item.getProductName());
        
        // 验证金额的合理性
        Assert.assertTrue("总金额应大于0", item.getTotalAmount().compareTo(BigDecimal.ZERO) > 0);
        Assert.assertTrue("退款金额应大于0", item.getRefundAmount().compareTo(BigDecimal.ZERO) > 0);
        Assert.assertTrue("退款金额不应超过总金额", item.getRefundAmount().compareTo(item.getTotalAmount()) <= 0);
        
        // 验证退款商品明细
        Assert.assertNotNull("退款商品明细不应为空", item.getRefundGoods());
        Assert.assertFalse("退款商品明细不应为空列表", item.getRefundGoods().isEmpty());
        
        // 打印生成的数据用于调试
        System.out.println("=== 生成的随机数据 ===");
        System.out.println("退款单号: " + item.getRefundNo());
        System.out.println("平台订单号: " + item.getPlatOrderNo());
        System.out.println("买家昵称: " + item.getBuyerNick());
        System.out.println("商品名称: " + item.getProductName());
        System.out.println("总金额: " + item.getTotalAmount());
        System.out.println("退款金额: " + item.getRefundAmount());
        System.out.println("售后类型: " + item.getRefundType() + " - " + item.getRefundTypeDesc());
        System.out.println("退款商品数量: " + item.getRefundGoods().size());
        System.out.println("==================");
    }

    /**
     * 测试退款单号和平台订单号的唯一性
     */
    @Test
    public void testGenerateRandom_UniquenessOfOrderNumbers() {
        Set<String> refundNos = new HashSet<>();
        Set<String> platOrderNos = new HashSet<>();
        
        // 生成100个随机数据，验证唯一性
        for (int i = 0; i < 100; i++) {
            BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
            
            String refundNo = item.getRefundNo();
            String platOrderNo = item.getPlatOrderNo();
            
            Assert.assertFalse("退款单号应该是唯一的: " + refundNo, refundNos.contains(refundNo));
            Assert.assertFalse("平台订单号应该是唯一的: " + platOrderNo, platOrderNos.contains(platOrderNo));
            
            refundNos.add(refundNo);
            platOrderNos.add(platOrderNo);
        }
        
        System.out.println("生成了 " + refundNos.size() + " 个唯一的退款单号");
        System.out.println("生成了 " + platOrderNos.size() + " 个唯一的平台订单号");
    }

    /**
     * 测试使用模板对象的功能
     */
    @Test
    public void testGenerateRandom_WithTemplate() {
        // 创建模板对象，设置一些固定值
        BusinessGetRefundOrderResponseOrderItem template = new BusinessGetRefundOrderResponseOrderItem();
        template.setBuyerNick("固定买家昵称");
        template.setTotalAmount(new BigDecimal("999.99"));
        template.setRefundType("JH_04");
        template.setProductName("固定商品名称");
        
        // 使用模板生成随机数据
        BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template);
        
        // 验证模板中的非空字段被保留
        Assert.assertEquals("买家昵称应该使用模板值", "固定买家昵称", item.getBuyerNick());
        Assert.assertEquals("总金额应该使用模板值", new BigDecimal("999.99"), item.getTotalAmount());
        Assert.assertEquals("售后类型应该使用模板值", "JH_04", item.getRefundType());
        Assert.assertEquals("商品名称应该使用模板值", "固定商品名称", item.getProductName());
        
        // 验证退款单号和平台订单号仍然是随机生成的
        Assert.assertNotNull("退款单号仍应随机生成", item.getRefundNo());
        Assert.assertNotNull("平台订单号仍应随机生成", item.getPlatOrderNo());
        Assert.assertTrue("退款单号应以RF开头", item.getRefundNo().startsWith("RF"));
        Assert.assertTrue("平台订单号应以PO开头", item.getPlatOrderNo().startsWith("PO"));
        
        // 验证模板中为空的字段被随机生成
        Assert.assertNotNull("卖家昵称应该被随机生成", item.getSellerNick());
        Assert.assertNotNull("创建时间应该被随机生成", item.getCreateTime());
        
        System.out.println("=== 使用模板生成的数据 ===");
        System.out.println("买家昵称(模板): " + item.getBuyerNick());
        System.out.println("总金额(模板): " + item.getTotalAmount());
        System.out.println("售后类型(模板): " + item.getRefundType());
        System.out.println("商品名称(模板): " + item.getProductName());
        System.out.println("退款单号(随机): " + item.getRefundNo());
        System.out.println("平台订单号(随机): " + item.getPlatOrderNo());
        System.out.println("卖家昵称(随机): " + item.getSellerNick());
        System.out.println("=====================");
    }

    /**
     * 测试不同售后类型的业务逻辑
     */
    @Test
    public void testGenerateRandom_DifferentRefundTypes() {
        // 测试退货退款单（JH_04）
        BusinessGetRefundOrderResponseOrderItem template1 = new BusinessGetRefundOrderResponseOrderItem();
        template1.setRefundType("JH_04");
        BusinessGetRefundOrderResponseOrderItem item1 = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template1);
        
        Assert.assertEquals("售后类型应为JH_04", "JH_04", item1.getRefundType());
        Assert.assertTrue("退货退款单应需要退货", item1.getHasGoodsReturn());
        Assert.assertNotNull("退货退款单应有物流信息", item1.getLogisticName());
        Assert.assertNotNull("退货退款单应有物流单号", item1.getLogisticNo());
        
        // 测试仅退款单（JH_03）
        BusinessGetRefundOrderResponseOrderItem template2 = new BusinessGetRefundOrderResponseOrderItem();
        template2.setRefundType("JH_03");
        BusinessGetRefundOrderResponseOrderItem item2 = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(template2);
        
        Assert.assertEquals("售后类型应为JH_03", "JH_03", item2.getRefundType());
        // JH_03通常不需要退货，但这取决于具体的业务逻辑
        
        System.out.println("=== 不同售后类型测试 ===");
        System.out.println("JH_04 - 需要退货: " + item1.getHasGoodsReturn() + ", 物流公司: " + item1.getLogisticName());
        System.out.println("JH_03 - 需要退货: " + item2.getHasGoodsReturn() + ", 物流公司: " + item2.getLogisticName());
        System.out.println("=====================");
    }

    /**
     * 测试生成数据的业务合理性
     */
    @Test
    public void testGenerateRandom_BusinessLogicValidation() {
        BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
        
        // 验证时间逻辑
        Assert.assertTrue("更新时间应晚于或等于创建时间", 
                         item.getUpdateTime().compareTo(item.getCreateTime()) >= 0);
        
        // 验证金额逻辑
        Assert.assertTrue("退款金额应小于等于总金额", 
                         item.getRefundAmount().compareTo(item.getTotalAmount()) <= 0);
        
        // 验证订单号关联性
        if (item.getSubPlatOrderNo() != null) {
            Assert.assertTrue("子订单号应包含主订单号", 
                             item.getSubPlatOrderNo().contains(item.getPlatOrderNo()));
        }
        
        // 验证退款商品明细的合理性
        if (item.getRefundGoods() != null && !item.getRefundGoods().isEmpty()) {
            item.getRefundGoods().forEach(goods -> {
                Assert.assertNotNull("商品ID不应为空", goods.getPlatProductId());
                Assert.assertNotNull("商品名称不应为空", goods.getProductName());
                Assert.assertTrue("商品数量应大于0", goods.getProductNum() > 0);
                Assert.assertTrue("退款商品数量应大于0", goods.getRefundProductNum() > 0);
                Assert.assertTrue("退款金额应大于0", goods.getRefundAmount().compareTo(BigDecimal.ZERO) > 0);
            });
        }
        
        System.out.println("=== 业务逻辑验证通过 ===");
        System.out.println("创建时间: " + item.getCreateTime());
        System.out.println("更新时间: " + item.getUpdateTime());
        System.out.println("总金额: " + item.getTotalAmount());
        System.out.println("退款金额: " + item.getRefundAmount());
        System.out.println("退款商品数量: " + item.getRefundGoods().size());
        System.out.println("==================");
    }

    /**
     * 测试空模板的处理
     */
    @Test
    public void testGenerateRandom_WithEmptyTemplate() {
        // 使用空模板
        BusinessGetRefundOrderResponseOrderItem emptyTemplate = new BusinessGetRefundOrderResponseOrderItem();
        BusinessGetRefundOrderResponseOrderItem item = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom(emptyTemplate);
        
        // 验证所有字段都被正确生成
        Assert.assertNotNull("退款单号不应为空", item.getRefundNo());
        Assert.assertNotNull("平台订单号不应为空", item.getPlatOrderNo());
        Assert.assertNotNull("买家昵称不应为空", item.getBuyerNick());
        Assert.assertNotNull("总金额不应为空", item.getTotalAmount());
        
        // 与不使用模板的结果应该类似
        BusinessGetRefundOrderResponseOrderItem itemWithoutTemplate = BusinessGetRefundOrderResponseOrderItemGenerator.generateRandom();
        
        // 验证两者都有完整的数据
        Assert.assertNotNull("无模板生成的数据应完整", itemWithoutTemplate.getBuyerNick());
        Assert.assertNotNull("空模板生成的数据应完整", item.getBuyerNick());
        
        System.out.println("=== 空模板测试通过 ===");
    }
}
