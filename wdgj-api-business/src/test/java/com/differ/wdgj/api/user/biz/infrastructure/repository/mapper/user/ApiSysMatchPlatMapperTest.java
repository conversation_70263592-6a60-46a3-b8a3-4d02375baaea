package com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchPlatDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 平台商品库存变动记录 仓储
 * 表g_api_sysMatch_plat
 *
 * <AUTHOR>
 * @date 2025/3/26 下午5:02
 */
@Ignore
public class ApiSysMatchPlatMapperTest extends AbstractSpringTest {

    @Autowired
    ApiSysMatchPlatMapper mapper;

    /**
     * 根据ApiSysMatchId查询记录
     */
    @Test
    public void selectByApiSysMatchIdTest(){
        List<ApiSysMatchPlatDO> platNotices = DBSwitchUtil.doDBWithUser("api2017", () -> mapper.selectByApiSysMatchId(4600));
        Assert.assertFalse(platNotices.isEmpty());
    }
}
