package com.differ.wdgj.api.user.biz.domain.stock.notice.processor;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.PlatStockNoticeStatusEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.SyncStockStatusEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.notice.ApiStockNoticeSaveResult;
import com.differ.wdgj.api.user.biz.domain.stock.notice.erp.processor.plugins.MultiWarePlatStockNoticeProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchPlatDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchTempDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchPlatMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchTempMapper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import java.util.List;

/**
 * erp货品变动通知处理 - 多仓库存同步
 * </p>
 * 我需要生成基于AbstractPlatStockNoticeProcessor的子类MultiWarePlatStockNoticeProcessor的集成测试，
 * 其中的流程为将表g_api_sysMatch_temp（数据库实体ApiSysMatchTempDO）中的数据通过一定的业务场景转换为表g_api_sysMatch_plat（数据库实体ApiSysMatchPlatDO）的数据。
 * 集成测试的流程如下，帮我生成尽可能覆盖完整的测试代码
 * 1、依赖表g_api_sysMatch_temp生成测试数据，其中GoodsID为15306，SpecID为0；
 * 2、调用类MultiWarePlatStockNoticeProcessor方法convertPlatStockNotice
 *
 * <AUTHOR>
 * @date 2025/3/13 下午4:24
 */
@Ignore
public class MultiWarePlatStockNoticeProcessorTest extends AbstractSpringTest {
    //region 变量
    private ApiSysMatchTempMapper apiSysMatchTempMapper;

    private ApiSysMatchPlatMapper apiSysMatchPlatMapper;

    private MultiWarePlatStockNoticeProcessor processor;

    private static final String TEST_MEMBER_NAME = "api2017";
    private static final Integer TEST_GOODS_ID = 15306;
    private static final Integer TEST_SPEC_ID = 0;
    //endregion

    //region 初始化
    @Before
    public void setUp() {
        processor = new MultiWarePlatStockNoticeProcessor(TEST_MEMBER_NAME);
        apiSysMatchTempMapper = BeanContextUtil.getBean(ApiSysMatchTempMapper.class);
        apiSysMatchPlatMapper = BeanContextUtil.getBean(ApiSysMatchPlatMapper.class);
    }
    //endregion

    /**
     * 无仓库匹配测试数据
     */
    @Test
    public void testConvertPlatStockNotice() {
        // 准备测试数据
        ApiSysMatchTempDO testData = createTestData();
        DBSwitchUtil.doDBWithUser(TEST_MEMBER_NAME, () -> apiSysMatchTempMapper.insert(testData));

        // 执行转换
        StockContentResult<ApiStockNoticeSaveResult> result = processor.convertPlatStockNotice();

        // 验证结果
        Assert.assertNotNull(result);

        // 验证目标表数据
        List<ApiSysMatchPlatDO> platNotices = DBSwitchUtil.doDBWithUser(TEST_MEMBER_NAME, () -> apiSysMatchPlatMapper.selectByApiSysMatchId(4600));
        Assert.assertFalse(platNotices.isEmpty());
    }

    /**
     * 多仓测试数据
     */
    @Test
    public void testConvertPlatStockNoticeWithMultipleWarehouses() {
        // 准备多仓库测试数据
        ApiSysMatchTempDO testData1 = createTestData();
        testData1.setWarehouseId("1000");
        ApiSysMatchTempDO testData2 = createTestData();
        testData2.setWarehouseId("1001");

        DBSwitchUtil.doDBWithUser(TEST_MEMBER_NAME, () -> {
            apiSysMatchTempMapper.insert(testData1);
            apiSysMatchTempMapper.insert(testData2);
        });

        // 执行转换
        StockContentResult<ApiStockNoticeSaveResult> result = processor.convertPlatStockNotice();

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getContent().getApiStockNoticeCount() > 0);

        // 验证多仓库数据转换结果
        List<ApiSysMatchPlatDO> platNotices = DBSwitchUtil.doDBWithUser(TEST_MEMBER_NAME, () -> apiSysMatchPlatMapper.selectByApiSysMatchId(4600));
        Assert.assertFalse(platNotices.isEmpty());
        Assert.assertTrue(platNotices.stream().filter(x -> PlatStockNoticeStatusEnum.WAIT_SYNC.getValue().equals(x.getStatus())).map(ApiSysMatchPlatDO::getPlatWarehouseCode).distinct().count() > 1);
    }

    /**
     * 无效测试数据
     */
    @Test
    public void testConvertPlatStockNoticeWithInvalidData() {
        // 准备无效数据
        ApiSysMatchTempDO invalidData = createTestData();
        invalidData.setGoodsId(-1);  // 设置无效的商品ID
        DBSwitchUtil.doDBWithUser(TEST_MEMBER_NAME, () -> apiSysMatchTempMapper.insert(invalidData));

        // 执行转换
        StockContentResult<ApiStockNoticeSaveResult> result = processor.convertPlatStockNotice();

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getContent().getApiStockNoticeCount() == 0);
    }


    //region 私有方法
    private ApiSysMatchTempDO createTestData() {
        ApiSysMatchTempDO tempDO = new ApiSysMatchTempDO();
        tempDO.setGoodsId(TEST_GOODS_ID);
        tempDO.setSpecId(TEST_SPEC_ID);
        tempDO.setIsSys(SyncStockStatusEnum.NoSync.getValue());
        tempDO.setIsSysMulti(SyncStockStatusEnum.WaitSync.getValue());
        tempDO.setWarehouseId("1");  // 设置测试仓库ID
        tempDO.setGoodsType(0);      // 设置商品类型
        tempDO.setChangeSource("1");    // 设置变动来源
        // 设置其他必要字段...
        return tempDO;
    }
    //endregion
}
