package com.differ.wdgj.api.user.biz.domain.apicall.adapter;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallPlatAuthorize;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyAPITypeEnum;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 适配器 - 平台授权
 *
 * <AUTHOR>
 * @date 2024/8/26 下午8:30
 */
@Ignore
public class PlatAuthorizeAdapterTest extends AbstractSpringTest {
    //region 常量
    /**
     * 平台授权适配器
     */
    private final PlatAuthorizeAdapter platAuthorizeAdapter = new PlatAuthorizeAdapter();
    //endregion

    /**
     * 获取平台授权信息 - 其他平台
     */
    @Test

    public void getPlatAuthorizeTest(){
        ApiCallPlatAuthorize platAuthorize = platAuthorizeAdapter.getPlatAuthorize(PolyAPITypeEnum.BUSINESS_GETORDER, PolyPlatEnum.BUSINESS_JD, "api2017", 1431);
        Assert.assertSame("f2c0a820992743f8b293f4b027265df0s", platAuthorize.getAppKey());
        Assert.assertSame("1e2c62096829450baac80180193249e2", platAuthorize.getAppSecret());
    }

    /**
     * 获取平台授权信息 - 菠萝派商城
     */
    @Test
    public void getPlatAuthorizeToPolyMallTest(){
        ApiCallPlatAuthorize platAuthorize = platAuthorizeAdapter.getPlatAuthorize(PolyAPITypeEnum.BUSINESS_GETORDER, PolyPlatEnum.BUSINESS_PolyMall, "api2017", 96933);
        Assert.assertEquals("123456", platAuthorize.getAppKey());
        Assert.assertEquals("123456", platAuthorize.getAppSecret());
    }
}
