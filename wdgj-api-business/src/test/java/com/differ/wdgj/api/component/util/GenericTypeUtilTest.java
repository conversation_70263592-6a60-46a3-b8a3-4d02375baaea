package com.differ.wdgj.api.component.util;

import org.apache.commons.lang3.StringUtils;
import org.junit.Ignore;
import org.junit.Test;

/**
 * 泛型
 *
 * <AUTHOR>
 * @date 2024/8/14 下午4:56
 */
@Ignore
public class GenericTypeUtilTest {
    /**
     * 递归解析直到获取到泛型对象的数据类型
     */
    @Test
    public void getGenericObjectDataTypeTest(){
        GenericTypeTest<String> test = new GenericTypeTest<>();
        Class<String> dataClazz = test.getDataClazz();
        String a = StringUtils.EMPTY;
    }
}

