package com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.strategy;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.processor.calculation.rule.StockCalculationRule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

/**
 * 抽象库存计算策略单元测试
 *
 * <AUTHOR>
 * @date 2025/6/17 11:10
 */
public class AbstractStockCalculationStrategyTest {

    @Mock
    private StockSyncContext mockContext;

    @Mock
    private StockCalculationRule mockRule;

    private TestableAbstractStockCalculationStrategy strategy;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 设置上下文基本信息
        when(mockContext.getVipUser()).thenReturn("testUser");
        when(mockContext.getPlat()).thenReturn(PolyPlatEnum.BUSINESS_Taobao);
        when(mockContext.getShopId()).thenReturn(12345);
        
        strategy = new TestableAbstractStockCalculationStrategy(mockContext);
    }

    /**
     * 测试成功计算
     */
    @Test
    public void testCalculateSyncQuantity_Success() {
        BigDecimal actualStock = BigDecimal.valueOf(100);
        BigDecimal expectedResult = BigDecimal.valueOf(80);
        
        strategy.setMockResult(GoodsStockCalculationResult.success(expectedResult, new HashMap<>()));
        
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isSuccess());
        assertEquals(expectedResult, result.getStockCount());
        assertTrue(strategy.isDoCalculateCalled());
    }

    /**
     * 测试异常处理
     */
    @Test
    public void testCalculateSyncQuantity_Exception() {
        BigDecimal actualStock = BigDecimal.valueOf(100);
        
        strategy.setThrowException(true);
        
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isFailed());
        assertTrue(result.getMessage().contains("testUser"));
        assertTrue(result.getMessage().contains("测试策略"));
        assertTrue(result.getMessage().contains("测试异常"));
    }

    /**
     * 测试失败结果
     */
    @Test
    public void testCalculateSyncQuantity_FailedResult() {
        BigDecimal actualStock = BigDecimal.valueOf(100);
        String errorMessage = "计算失败";
        
        strategy.setMockResult(GoodsStockCalculationResult.failed(errorMessage));
        
        GoodsStockCalculationResult result = strategy.calculateSyncQuantity(mockRule, actualStock);
        
        assertTrue(result.isFailed());
        assertEquals(errorMessage, result.getMessage());
    }

    /**
     * 可测试的抽象策略实现类
     */
    private static class TestableAbstractStockCalculationStrategy extends AbstractStockCalculationStrategy {
        private GoodsStockCalculationResult mockResult;
        private boolean throwException = false;
        private boolean doCalculateCalled = false;

        public TestableAbstractStockCalculationStrategy(StockSyncContext context) {
            super(context);
        }

        @Override
        protected GoodsStockCalculationResult doCalculateSyncQuantity(StockCalculationRule rule, BigDecimal actualStock) {
            doCalculateCalled = true;
            
            if (throwException) {
                throw new RuntimeException("测试异常");
            }
            
            return mockResult != null ? mockResult : GoodsStockCalculationResult.success(actualStock, new HashMap<>());
        }

        @Override
        public String getStrategyName() {
            return "测试策略";
        }

        public void setMockResult(GoodsStockCalculationResult result) {
            this.mockResult = result;
        }

        public void setThrowException(boolean throwException) {
            this.throwException = throwException;
        }

        public boolean isDoCalculateCalled() {
            return doCalculateCalled;
        }
    }
}
